<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.alibaba.smart.processor</groupId>
		<artifactId>processor</artifactId>
		<version>1.0.0</version>
		<relativePath>../pom.xml</relativePath>
	</parent>
	<artifactId>processor-start</artifactId>
	<packaging>jar</packaging>
	<name>smart-processor-start</name>

	<properties>
		<mybatis-generator.version>1.4.1</mybatis-generator.version>
		<mysql.version>5.1.13</mysql.version>
		<mybatis.version>3.5.2</mybatis.version>
	</properties>

	<dependencies>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-actuator-autoconfigure</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-actuator</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-web</artifactId>
		</dependency>
		<dependency>
			<groupId>com.alibaba.boot</groupId>
			<artifactId>pandora-eagleeye-spring-boot-starter</artifactId>
		</dependency>
		<dependency>
			<groupId>com.alibaba.smart.processor</groupId>
			<artifactId>processor-facade</artifactId>
		</dependency>
		<dependency>
			<groupId>com.taobao.pandora</groupId>
			<artifactId>pandora-boot-bootstrap</artifactId>
		</dependency>
		<dependency>
			<groupId>com.taobao.pandora</groupId>
			<artifactId>taobao-hsf.sar</artifactId>
		</dependency>
		<dependency>
			<groupId>org.slf4j</groupId>
			<artifactId>jcl-over-slf4j</artifactId>
		</dependency>

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-test</artifactId>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>com.taobao.pandora</groupId>
			<artifactId>pandora-boot-test</artifactId>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>junit</groupId>
			<artifactId>junit</artifactId>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.mockito</groupId>
			<artifactId>mockito-core</artifactId>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-test</artifactId>
			<scope>test</scope>
		</dependency>

		<dependency>
			<groupId>com.alibaba.boot</groupId>
			<artifactId>velocity-spring-boot-starter</artifactId>
		</dependency>
		<dependency>
			<groupId>org.apache.velocity</groupId>
			<artifactId>velocity-tools</artifactId>
		</dependency>
		<dependency>
			<groupId>org.apache.velocity</groupId>
			<artifactId>velocity-engine-core</artifactId>
		</dependency>
		<dependency>
			<groupId>com.alibaba</groupId>
			<artifactId>fastjson</artifactId>
		</dependency>
		<dependency>
			<groupId>com.dingtalk.dbase</groupId>
			<artifactId>dbase-trial</artifactId>
		</dependency>

		<dependency>
			<groupId>com.dingtalk</groupId>
			<artifactId>lippi-common-sso</artifactId>
		</dependency>

		<dependency>
			<groupId>com.laiwang</groupId>
			<artifactId>lippi-org.api</artifactId>
		</dependency>

		<dependency>
			<groupId>com.laiwang</groupId>
			<artifactId>lippi-user.api</artifactId>
		</dependency>

		<dependency>
			<groupId>mysql</groupId>
			<artifactId>mysql-connector-java</artifactId>
			<version>${mysql.version}</version>
		</dependency>
		<dependency>
			<groupId>org.mybatis.generator</groupId>
			<artifactId>mybatis-generator-core</artifactId>
			<version>${mybatis-generator.version}</version>
		</dependency>
		<dependency>
			<groupId>org.mybatis</groupId>
			<artifactId>mybatis</artifactId>
			<version>${mybatis.version}</version>
		</dependency>

	</dependencies>
	<build>
		<plugins>
			<plugin>
				<groupId>com.taobao.pandora</groupId>
				<artifactId>pandora-boot-maven-plugin</artifactId>
				<executions>
					<execution>
						<id>pandora-boot-maven-plugin</id>
						<phase>package</phase>
						<goals>
							<goal>repackage</goal>
						</goals>
					</execution>
				</executions>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-antrun-plugin</artifactId>
				<executions>
					<execution>
						<id>maven-antrun-plugin</id>
						<phase>package</phase>
						<goals>
							<goal>run</goal>
						</goals>
						<configuration>
							<tasks>
								<unzip src="${project.build.directory}/${project.build.finalName}.${project.packaging}"  dest="${project.build.directory}/smart-processor"/>
							</tasks>
						</configuration>
					</execution>
				</executions>
			</plugin>

			<plugin>
				<groupId>org.mybatis.generator</groupId>
				<artifactId>mybatis-generator-maven-plugin</artifactId>
				<version>${mybatis-generator.version}</version>
				<configuration>
					<configurationFile>../processor-start/src/main/resources/mybatis/generatorConfig.xml</configurationFile>
					<overwrite>true</overwrite>
					<verbose>true</verbose>
				</configuration>
				<dependencies>
					<dependency>
						<groupId>mysql</groupId>
						<artifactId>mysql-connector-java</artifactId>
						<version>${mysql.version}</version>
					</dependency>
					<dependency>
						<groupId>com.alibaba.smart.processor</groupId>
						<artifactId>processor-dao</artifactId>
						<version>${project.version}</version>
					</dependency>
				</dependencies>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-compiler-plugin</artifactId>
				<configuration>
					<source>11</source>
					<target>11</target>
					<encoding>UTF-8</encoding>
				</configuration>
			</plugin>
		</plugins>
	</build>
</project>
