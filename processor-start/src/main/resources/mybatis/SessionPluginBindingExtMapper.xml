<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.alibaba.smart.processor.mapper.SessionPluginBindingExtMapper">
  <resultMap id="BaseResultMap" type="com.alibaba.smart.processor.model.SessionPluginBindingDO">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Jun 16 20:06:22 CST 2025.
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="modifier" jdbcType="VARCHAR" property="modifier" />
    <result column="is_deleted" jdbcType="VARCHAR" property="isDeleted" />
    <result column="session_id" jdbcType="VARCHAR" property="sessionId" />
    <result column="plugin_id" jdbcType="VARCHAR" property="pluginId" />
    <result column="corp_id" jdbcType="VARCHAR" property="corpId" />
  </resultMap>

  <sql id="selectRowNoId">
      <![CDATA[gmt_create, gmt_modified, creator, modifier, is_deleted, session_id, plugin_id, corp_id]]>
  </sql>

  <sql id="batchInsertRowNoId">
      <![CDATA[
    #{item.gmtCreate}, #{item.gmtModified}, #{item.creator}, #{item.modifier}, #{item.isDeleted},#{item.sessionId},#{item.pluginId},#{item.corpId} ]]>
  </sql>

  <insert id="batchInsert" parameterType="com.alibaba.smart.processor.model.SessionPluginBindingDO" keyProperty="id"
          useGeneratedKeys="true">
    insert into smart_processor_session_plugins_binding(<include refid="selectRowNoId"/>)
    values
    <foreach collection="list" item="item" separator=",">
      (<include refid="batchInsertRowNoId"/>)
    </foreach>
  </insert>

  <update id="batchUpdate">
    update smart_processor_session_plugins_binding
    set gmt_modified = now(), is_deleted = 'y', modifier = #{modifier}
    where session_id = #{sessionId}
    and plugin_id IN
    <foreach collection="pluginIds" item="item" open="(" close=")" separator=",">
      #{item}
    </foreach>
  </update>

  <delete id="deleteBySessionId">
    delete from smart_processor_session_plugins_binding where session_id = #{sessionId}
  </delete>

</mapper>