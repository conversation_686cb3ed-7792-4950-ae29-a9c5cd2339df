<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.alibaba.smart.processor.mapper.ChatMessageMapper">
  <resultMap id="BaseResultMap" type="com.alibaba.smart.processor.model.ChatMessageDO">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue May 20 15:31:42 CST 2025.
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="modifier" jdbcType="VARCHAR" property="modifier" />
    <result column="is_deleted" jdbcType="VARCHAR" property="isDeleted" />
    <result column="session_id" jdbcType="VARCHAR" property="sessionId" />
    <result column="sender" jdbcType="VARCHAR" property="sender" />
    <result column="sender_type" jdbcType="VARCHAR" property="senderType" />
    <result column="message_id" jdbcType="VARCHAR" property="messageId" />
    <result column="corp_id" jdbcType="VARCHAR" property="corpId" />
    <result column="files" jdbcType="VARCHAR" property="files" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.alibaba.smart.processor.model.ChatMessageDO">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue May 20 15:31:42 CST 2025.
    -->
    <result column="message" jdbcType="LONGVARCHAR" property="message" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue May 20 15:31:42 CST 2025.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue May 20 15:31:42 CST 2025.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue May 20 15:31:42 CST 2025.
    -->
    id, gmt_create, gmt_modified, creator, modifier, is_deleted, session_id, sender, 
    sender_type, message_id, corp_id, files
  </sql>
  <sql id="Blob_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue May 20 15:31:42 CST 2025.
    -->
    message
  </sql>
  <select id="selectByExampleWithBLOBs" parameterType="com.alibaba.smart.processor.model.ChatMessageDOExample" resultMap="ResultMapWithBLOBs">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue May 20 15:31:42 CST 2025.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from smart_processor_chat_message
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExample" parameterType="com.alibaba.smart.processor.model.ChatMessageDOExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue May 20 15:31:42 CST 2025.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from smart_processor_chat_message
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="ResultMapWithBLOBs">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue May 20 15:31:42 CST 2025.
    -->
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from smart_processor_chat_message
    where id = #{id,jdbcType=BIGINT}
  </select>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.alibaba.smart.processor.model.ChatMessageDO" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue May 20 15:31:42 CST 2025.
    -->
    insert into smart_processor_chat_message (gmt_create, gmt_modified, creator, 
      modifier, is_deleted, session_id, 
      sender, sender_type, message_id, 
      corp_id, message,  files)
    values (#{gmtCreate,jdbcType=TIMESTAMP}, #{gmtModified,jdbcType=TIMESTAMP}, #{creator,jdbcType=VARCHAR}, 
      #{modifier,jdbcType=VARCHAR}, #{isDeleted,jdbcType=VARCHAR}, #{sessionId,jdbcType=VARCHAR}, 
      #{sender,jdbcType=VARCHAR}, #{senderType,jdbcType=VARCHAR}, #{messageId,jdbcType=VARCHAR}, 
      #{corpId,jdbcType=VARCHAR}, #{message,jdbcType=LONGVARCHAR}, #{files,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.alibaba.smart.processor.model.ChatMessageDO" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue May 20 15:31:42 CST 2025.
    -->
    insert into smart_processor_chat_message
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="modifier != null">
        modifier,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="sessionId != null">
        session_id,
      </if>
      <if test="sender != null">
        sender,
      </if>
      <if test="senderType != null">
        sender_type,
      </if>
      <if test="messageId != null">
        message_id,
      </if>
      <if test="corpId != null">
        corp_id,
      </if>
      <if test="message != null">
        message,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=VARCHAR},
      </if>
      <if test="sessionId != null">
        #{sessionId,jdbcType=VARCHAR},
      </if>
      <if test="sender != null">
        #{sender,jdbcType=VARCHAR},
      </if>
      <if test="senderType != null">
        #{senderType,jdbcType=VARCHAR},
      </if>
      <if test="messageId != null">
        #{messageId,jdbcType=VARCHAR},
      </if>
      <if test="corpId != null">
        #{corpId,jdbcType=VARCHAR},
      </if>
      <if test="message != null">
        #{message,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.alibaba.smart.processor.model.ChatMessageDOExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue May 20 15:31:42 CST 2025.
    -->
    select count(*) from smart_processor_chat_message
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue May 20 15:31:42 CST 2025.
    -->
    update smart_processor_chat_message
    <set>
      <if test="row.id != null">
        id = #{row.id,jdbcType=BIGINT},
      </if>
      <if test="row.gmtCreate != null">
        gmt_create = #{row.gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="row.gmtModified != null">
        gmt_modified = #{row.gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="row.creator != null">
        creator = #{row.creator,jdbcType=VARCHAR},
      </if>
      <if test="row.modifier != null">
        modifier = #{row.modifier,jdbcType=VARCHAR},
      </if>
      <if test="row.isDeleted != null">
        is_deleted = #{row.isDeleted,jdbcType=VARCHAR},
      </if>
      <if test="row.sessionId != null">
        session_id = #{row.sessionId,jdbcType=VARCHAR},
      </if>
      <if test="row.sender != null">
        sender = #{row.sender,jdbcType=VARCHAR},
      </if>
      <if test="row.senderType != null">
        sender_type = #{row.senderType,jdbcType=VARCHAR},
      </if>
      <if test="row.messageId != null">
        message_id = #{row.messageId,jdbcType=VARCHAR},
      </if>
      <if test="row.corpId != null">
        corp_id = #{row.corpId,jdbcType=VARCHAR},
      </if>
      <if test="row.message != null">
        message = #{row.message,jdbcType=LONGVARCHAR},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue May 20 15:31:42 CST 2025.
    -->
    update smart_processor_chat_message
    set id = #{row.id,jdbcType=BIGINT},
      gmt_create = #{row.gmtCreate,jdbcType=TIMESTAMP},
      gmt_modified = #{row.gmtModified,jdbcType=TIMESTAMP},
      creator = #{row.creator,jdbcType=VARCHAR},
      modifier = #{row.modifier,jdbcType=VARCHAR},
      is_deleted = #{row.isDeleted,jdbcType=VARCHAR},
      session_id = #{row.sessionId,jdbcType=VARCHAR},
      sender = #{row.sender,jdbcType=VARCHAR},
      sender_type = #{row.senderType,jdbcType=VARCHAR},
      message_id = #{row.messageId,jdbcType=VARCHAR},
      corp_id = #{row.corpId,jdbcType=VARCHAR},
      files = #{row.files,jdbcType=VARCHAR},
      message = #{row.message,jdbcType=LONGVARCHAR}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue May 20 15:31:42 CST 2025.
    -->
    update smart_processor_chat_message
    set id = #{row.id,jdbcType=BIGINT},
      gmt_create = #{row.gmtCreate,jdbcType=TIMESTAMP},
      gmt_modified = #{row.gmtModified,jdbcType=TIMESTAMP},
      creator = #{row.creator,jdbcType=VARCHAR},
      modifier = #{row.modifier,jdbcType=VARCHAR},
      is_deleted = #{row.isDeleted,jdbcType=VARCHAR},
      session_id = #{row.sessionId,jdbcType=VARCHAR},
      sender = #{row.sender,jdbcType=VARCHAR},
      sender_type = #{row.senderType,jdbcType=VARCHAR},
      message_id = #{row.messageId,jdbcType=VARCHAR},
      files = #{row.files,jdbcType=VARCHAR},
      corp_id = #{row.corpId,jdbcType=VARCHAR}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.alibaba.smart.processor.model.ChatMessageDO">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue May 20 15:31:42 CST 2025.
    -->
    update smart_processor_chat_message
    <set>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        modifier = #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=VARCHAR},
      </if>
      <if test="sessionId != null">
        session_id = #{sessionId,jdbcType=VARCHAR},
      </if>
      <if test="sender != null">
        sender = #{sender,jdbcType=VARCHAR},
      </if>
      <if test="senderType != null">
        sender_type = #{senderType,jdbcType=VARCHAR},
      </if>
      <if test="messageId != null">
        message_id = #{messageId,jdbcType=VARCHAR},
      </if>
      <if test="corpId != null">
        corp_id = #{corpId,jdbcType=VARCHAR},
      </if>
      <if test="files != null">
        files = #{files,jdbcType=VARCHAR},
      </if>
      <if test="message != null">
        message = #{message,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.alibaba.smart.processor.model.ChatMessageDO">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue May 20 15:31:42 CST 2025.
    -->
    update smart_processor_chat_message
    set gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      creator = #{creator,jdbcType=VARCHAR},
      modifier = #{modifier,jdbcType=VARCHAR},
      is_deleted = #{isDeleted,jdbcType=VARCHAR},
      session_id = #{sessionId,jdbcType=VARCHAR},
      sender = #{sender,jdbcType=VARCHAR},
      sender_type = #{senderType,jdbcType=VARCHAR},
      message_id = #{messageId,jdbcType=VARCHAR},
      corp_id = #{corpId,jdbcType=VARCHAR},
      files = #{files,jdbcType=VARCHAR},
      message = #{message,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.alibaba.smart.processor.model.ChatMessageDO">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue May 20 15:31:42 CST 2025.
    -->
    update smart_processor_chat_message
    set gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      creator = #{creator,jdbcType=VARCHAR},
      modifier = #{modifier,jdbcType=VARCHAR},
      is_deleted = #{isDeleted,jdbcType=VARCHAR},
      session_id = #{sessionId,jdbcType=VARCHAR},
      sender = #{sender,jdbcType=VARCHAR},
      sender_type = #{senderType,jdbcType=VARCHAR},
      message_id = #{messageId,jdbcType=VARCHAR},
      files = #{files,jdbcType=VARCHAR},
      corp_id = #{corpId,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <select id="selectByExampleWithBLOBsWithRowbounds" parameterType="com.alibaba.smart.processor.model.ChatMessageDOExample" resultMap="ResultMapWithBLOBs">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue May 20 15:31:42 CST 2025.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from smart_processor_chat_message
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExampleWithRowbounds" parameterType="com.alibaba.smart.processor.model.ChatMessageDOExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue May 20 15:31:42 CST 2025.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from smart_processor_chat_message
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
</mapper>