<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.alibaba.smart.processor.mapper.ChatMessageHistoryMapper">
  <resultMap id="BaseResultMap" type="com.alibaba.smart.processor.model.ChatMessageHistoryDO">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue May 20 15:31:42 CST 2025.
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="modifier" jdbcType="VARCHAR" property="modifier" />
    <result column="is_deleted" jdbcType="VARCHAR" property="isDeleted" />
    <result column="session_id" jdbcType="VARCHAR" property="sessionId" />
    <result column="message_id" jdbcType="VARCHAR" property="messageId" />
    <result column="extension" jdbcType="VARCHAR" property="extension" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.alibaba.smart.processor.model.ChatMessageHistoryDO">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue May 20 15:31:42 CST 2025.
    -->
    <result column="content" jdbcType="LONGVARCHAR" property="content" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue May 20 15:31:42 CST 2025.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue May 20 15:31:42 CST 2025.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue May 20 15:31:42 CST 2025.
    -->
    id, gmt_create, gmt_modified, creator, modifier, is_deleted, session_id, message_id, 
    extension
  </sql>
  <sql id="Blob_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue May 20 15:31:42 CST 2025.
    -->
    content
  </sql>
  <select id="selectByExampleWithBLOBs" parameterType="com.alibaba.smart.processor.model.ChatMessageHistoryDOExample" resultMap="ResultMapWithBLOBs">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue May 20 15:31:42 CST 2025.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from smart_processor_chat_message_history
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExample" parameterType="com.alibaba.smart.processor.model.ChatMessageHistoryDOExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue May 20 15:31:42 CST 2025.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from smart_processor_chat_message_history
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="ResultMapWithBLOBs">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue May 20 15:31:42 CST 2025.
    -->
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from smart_processor_chat_message_history
    where id = #{id,jdbcType=BIGINT}
  </select>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.alibaba.smart.processor.model.ChatMessageHistoryDO" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue May 20 15:31:42 CST 2025.
    -->
    insert into smart_processor_chat_message_history (gmt_create, gmt_modified, creator, 
      modifier, is_deleted, session_id, 
      message_id, extension, content
      )
    values (#{gmtCreate,jdbcType=TIMESTAMP}, #{gmtModified,jdbcType=TIMESTAMP}, #{creator,jdbcType=VARCHAR}, 
      #{modifier,jdbcType=VARCHAR}, #{isDeleted,jdbcType=VARCHAR}, #{sessionId,jdbcType=VARCHAR}, 
      #{messageId,jdbcType=VARCHAR}, #{extension,jdbcType=VARCHAR}, #{content,jdbcType=LONGVARCHAR}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.alibaba.smart.processor.model.ChatMessageHistoryDO" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue May 20 15:31:42 CST 2025.
    -->
    insert into smart_processor_chat_message_history
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="modifier != null">
        modifier,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="sessionId != null">
        session_id,
      </if>
      <if test="messageId != null">
        message_id,
      </if>
      <if test="extension != null">
        extension,
      </if>
      <if test="content != null">
        content,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=VARCHAR},
      </if>
      <if test="sessionId != null">
        #{sessionId,jdbcType=VARCHAR},
      </if>
      <if test="messageId != null">
        #{messageId,jdbcType=VARCHAR},
      </if>
      <if test="extension != null">
        #{extension,jdbcType=VARCHAR},
      </if>
      <if test="content != null">
        #{content,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.alibaba.smart.processor.model.ChatMessageHistoryDOExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue May 20 15:31:42 CST 2025.
    -->
    select count(*) from smart_processor_chat_message_history
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue May 20 15:31:42 CST 2025.
    -->
    update smart_processor_chat_message_history
    <set>
      <if test="row.id != null">
        id = #{row.id,jdbcType=BIGINT},
      </if>
      <if test="row.gmtCreate != null">
        gmt_create = #{row.gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="row.gmtModified != null">
        gmt_modified = #{row.gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="row.creator != null">
        creator = #{row.creator,jdbcType=VARCHAR},
      </if>
      <if test="row.modifier != null">
        modifier = #{row.modifier,jdbcType=VARCHAR},
      </if>
      <if test="row.isDeleted != null">
        is_deleted = #{row.isDeleted,jdbcType=VARCHAR},
      </if>
      <if test="row.sessionId != null">
        session_id = #{row.sessionId,jdbcType=VARCHAR},
      </if>
      <if test="row.messageId != null">
        message_id = #{row.messageId,jdbcType=VARCHAR},
      </if>
      <if test="row.extension != null">
        extension = #{row.extension,jdbcType=VARCHAR},
      </if>
      <if test="row.content != null">
        content = #{row.content,jdbcType=LONGVARCHAR},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue May 20 15:31:42 CST 2025.
    -->
    update smart_processor_chat_message_history
    set id = #{row.id,jdbcType=BIGINT},
      gmt_create = #{row.gmtCreate,jdbcType=TIMESTAMP},
      gmt_modified = #{row.gmtModified,jdbcType=TIMESTAMP},
      creator = #{row.creator,jdbcType=VARCHAR},
      modifier = #{row.modifier,jdbcType=VARCHAR},
      is_deleted = #{row.isDeleted,jdbcType=VARCHAR},
      session_id = #{row.sessionId,jdbcType=VARCHAR},
      message_id = #{row.messageId,jdbcType=VARCHAR},
      extension = #{row.extension,jdbcType=VARCHAR},
      content = #{row.content,jdbcType=LONGVARCHAR}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue May 20 15:31:42 CST 2025.
    -->
    update smart_processor_chat_message_history
    set id = #{row.id,jdbcType=BIGINT},
      gmt_create = #{row.gmtCreate,jdbcType=TIMESTAMP},
      gmt_modified = #{row.gmtModified,jdbcType=TIMESTAMP},
      creator = #{row.creator,jdbcType=VARCHAR},
      modifier = #{row.modifier,jdbcType=VARCHAR},
      is_deleted = #{row.isDeleted,jdbcType=VARCHAR},
      session_id = #{row.sessionId,jdbcType=VARCHAR},
      message_id = #{row.messageId,jdbcType=VARCHAR},
      extension = #{row.extension,jdbcType=VARCHAR}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.alibaba.smart.processor.model.ChatMessageHistoryDO">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue May 20 15:31:42 CST 2025.
    -->
    update smart_processor_chat_message_history
    <set>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        modifier = #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=VARCHAR},
      </if>
      <if test="sessionId != null">
        session_id = #{sessionId,jdbcType=VARCHAR},
      </if>
      <if test="messageId != null">
        message_id = #{messageId,jdbcType=VARCHAR},
      </if>
      <if test="extension != null">
        extension = #{extension,jdbcType=VARCHAR},
      </if>
      <if test="content != null">
        content = #{content,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.alibaba.smart.processor.model.ChatMessageHistoryDO">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue May 20 15:31:42 CST 2025.
    -->
    update smart_processor_chat_message_history
    set gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      creator = #{creator,jdbcType=VARCHAR},
      modifier = #{modifier,jdbcType=VARCHAR},
      is_deleted = #{isDeleted,jdbcType=VARCHAR},
      session_id = #{sessionId,jdbcType=VARCHAR},
      message_id = #{messageId,jdbcType=VARCHAR},
      extension = #{extension,jdbcType=VARCHAR},
      content = #{content,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.alibaba.smart.processor.model.ChatMessageHistoryDO">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue May 20 15:31:42 CST 2025.
    -->
    update smart_processor_chat_message_history
    set gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      creator = #{creator,jdbcType=VARCHAR},
      modifier = #{modifier,jdbcType=VARCHAR},
      is_deleted = #{isDeleted,jdbcType=VARCHAR},
      session_id = #{sessionId,jdbcType=VARCHAR},
      message_id = #{messageId,jdbcType=VARCHAR},
      extension = #{extension,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <select id="selectByExampleWithBLOBsWithRowbounds" parameterType="com.alibaba.smart.processor.model.ChatMessageHistoryDOExample" resultMap="ResultMapWithBLOBs">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue May 20 15:31:42 CST 2025.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from smart_processor_chat_message_history
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExampleWithRowbounds" parameterType="com.alibaba.smart.processor.model.ChatMessageHistoryDOExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Tue May 20 15:31:42 CST 2025.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from smart_processor_chat_message_history
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
</mapper>