<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.alibaba.smart.processor.mapper.FileRecordMapper">
    <resultMap type="com.alibaba.smart.processor.model.attach.FileRecord" id="fileRecordMap">
        <result property="id" column="id" jdbcType="BIGINT"/>
        <result property="gmtCreate" column="gmt_create" jdbcType="TIMESTAMP"/>
        <result property="gmtModified" column="gmt_modified" jdbcType="TIMESTAMP"/>
        <result property="creator" column="creator" jdbcType="VARCHAR"/>
        <result property="modifier" column="modifier" jdbcType="VARCHAR"/>
        <result property="corpId" column="corp_id" jdbcType="VARCHAR"/>
        <result property="isDeleted" column="is_deleted" jdbcType="VARCHAR"/>
        <result property="fileName" column="file_name" jdbcType="VARCHAR"/>
        <result property="fileSize" column="file_size" jdbcType="BIGINT"/>
        <result property="fileExtType" column="file_ext_type" jdbcType="VARCHAR"/>
        <result property="fileMeta" column="file_meta" jdbcType="VARCHAR"/>
        <result property="ossKey" column="oss_key" jdbcType="VARCHAR"/>
        <result property="sessionId" column="session_id" jdbcType="VARCHAR"/>
        <result property="dingMediaId" column="ding_media_id" jdbcType="VARCHAR"/>
        <result property="immKey" column="imm_key" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="selectRow">
        <![CDATA[ id, gmt_create, gmt_modified, corp_id, creator, modifier, is_deleted, file_name, file_size, file_ext_type, file_meta, oss_key, session_id, ding_media_id, imm_key]]>
    </sql>

    <sql id="insertRow">
        <![CDATA[ #{id}, #{gmtCreate}, #{gmtModified}, #{corpId}, #{creator}, #{modifier},#{isDeleted}, #{fileName},#{fileSize},#{fileExtType},#{fileMeta},#{ossKey},#{sessionId},#{dingMediaId},#{immKey}]]>
    </sql>

    <sql id="selectNoRowId">
        <![CDATA[ gmt_create, gmt_modified, corp_id, creator, modifier, is_deleted, file_name, file_size, file_ext_type, file_meta, oss_key, session_id, ding_media_id,imm_key]]>
    </sql>

    <sql id="insertNoRowId">
        <![CDATA[ #{gmtCreate}, #{gmtModified}, #{corpId}, #{creator}, #{modifier},#{isDeleted}, #{fileName},#{fileSize},#{fileExtType},#{fileMeta},#{ossKey},#{sessionId},#{dingMediaId},#{immKey}]]>
    </sql>
    <insert id="insert" parameterType="com.alibaba.smart.processor.model.attach.FileRecord">
        insert into smart_processor_file_record(<include refid="selectNoRowId"/>)
        values(<include refid="insertNoRowId"/>)
    </insert>

    <select id="getFileByKey" resultType="com.alibaba.smart.processor.model.attach.FileRecord" resultMap="fileRecordMap">
        select * from smart_processor_file_record where oss_key =#{ossKey} and is_deleted='n'
    </select>

    <update id="updateImmKey">
        update smart_processor_file_record set gmt_modified=now(), imm_key=#{immKey}
        where oss_key=#{ossKey} and is_deleted='n'
    </update>
</mapper>

