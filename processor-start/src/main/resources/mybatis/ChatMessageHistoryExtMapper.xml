<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.alibaba.smart.processor.mapper.ChatMessageHistoryExtMapper">
  <resultMap id="BaseResultMap" type="com.alibaba.smart.processor.model.ChatMessageHistoryDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="modifier" jdbcType="VARCHAR" property="modifier" />
    <result column="is_deleted" jdbcType="VARCHAR" property="isDeleted" />
    <result column="session_id" jdbcType="VARCHAR" property="sessionId" />
    <result column="message_id" jdbcType="VARCHAR" property="messageId" />
    <result column="extension" jdbcType="VARCHAR" property="extension" />
    <result column="content" jdbcType="VARCHAR" property="content" />
  </resultMap>

  <sql id="selectRowNoId">
      <![CDATA[gmt_create, gmt_modified, creator, modifier, is_deleted, session_id, message_id, extension, content]]>
  </sql>

  <sql id="batchInsertRowNoId">
      <![CDATA[
          #{item.gmtCreate}, #{item.gmtModified}, #{item.creator}, #{item.modifier}, #{item.isDeleted},#{item.sessionId},#{item.messageId},#{item.extension},#{item.content} ]]>
  </sql>

  <insert id="batchInsert" parameterType="com.alibaba.smart.processor.model.ChatMessageHistoryDO" keyProperty="id" useGeneratedKeys="true">
    insert into smart_processor_chat_message_history(<include refid="selectRowNoId"/>)
    values
    <foreach collection="list" item="item" separator=",">
      (<include refid="batchInsertRowNoId"/>)
    </foreach>
  </insert>
  
  <delete id="deleteByMessageId">
    delete from smart_processor_chat_message_history where message_id = #{messageId} and session_id = #{sessionId}
  </delete>
</mapper>