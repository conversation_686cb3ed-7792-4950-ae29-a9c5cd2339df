<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.alibaba.smart.processor.mapper.AppResourceMapper">
  <resultMap id="BaseResultMap" type="com.alibaba.smart.processor.model.AppResourceDO">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri May 23 15:27:49 CST 2025.
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="modifier" jdbcType="VARCHAR" property="modifier" />
    <result column="is_deleted" jdbcType="VARCHAR" property="isDeleted" />
    <result column="app_id" jdbcType="VARCHAR" property="appId" />
    <result column="resource_id" jdbcType="VARCHAR" property="resourceId" />
    <result column="source_key" jdbcType="VARCHAR" property="sourceKey" />
    <result column="source_compiled_key" jdbcType="VARCHAR" property="sourceCompiledKey" />
    <result column="resource_type" jdbcType="VARCHAR" property="resourceType" />
    <result column="version" jdbcType="VARCHAR" property="version" />
    <result column="corp_id" jdbcType="VARCHAR" property="corpId" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri May 23 15:27:49 CST 2025.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri May 23 15:27:49 CST 2025.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri May 23 15:27:49 CST 2025.
    -->
    id, gmt_create, gmt_modified, creator, modifier, is_deleted, app_id, resource_id, 
    source_key, source_compiled_key, resource_type, version, corp_id
  </sql>
  <select id="selectByExample" parameterType="com.alibaba.smart.processor.model.AppResourceDOExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri May 23 15:27:49 CST 2025.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from smart_processor_app_resource
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri May 23 15:27:49 CST 2025.
    -->
    select 
    <include refid="Base_Column_List" />
    from smart_processor_app_resource
    where id = #{id,jdbcType=BIGINT}
  </select>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.alibaba.smart.processor.model.AppResourceDO" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri May 23 15:27:49 CST 2025.
    -->
    insert into smart_processor_app_resource (gmt_create, gmt_modified, creator, 
      modifier, is_deleted, app_id, 
      resource_id, source_key, source_compiled_key, 
      resource_type, version, corp_id
      )
    values (#{gmtCreate,jdbcType=TIMESTAMP}, #{gmtModified,jdbcType=TIMESTAMP}, #{creator,jdbcType=VARCHAR}, 
      #{modifier,jdbcType=VARCHAR}, #{isDeleted,jdbcType=VARCHAR}, #{appId,jdbcType=VARCHAR}, 
      #{resourceId,jdbcType=VARCHAR}, #{sourceKey,jdbcType=VARCHAR}, #{sourceCompiledKey,jdbcType=VARCHAR}, 
      #{resourceType,jdbcType=VARCHAR}, #{version,jdbcType=VARCHAR}, #{corpId,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.alibaba.smart.processor.model.AppResourceDO" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri May 23 15:27:49 CST 2025.
    -->
    insert into smart_processor_app_resource
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="modifier != null">
        modifier,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="appId != null">
        app_id,
      </if>
      <if test="resourceId != null">
        resource_id,
      </if>
      <if test="sourceKey != null">
        source_key,
      </if>
      <if test="sourceCompiledKey != null">
        source_compiled_key,
      </if>
      <if test="resourceType != null">
        resource_type,
      </if>
      <if test="version != null">
        version,
      </if>
      <if test="corpId != null">
        corp_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=VARCHAR},
      </if>
      <if test="appId != null">
        #{appId,jdbcType=VARCHAR},
      </if>
      <if test="resourceId != null">
        #{resourceId,jdbcType=VARCHAR},
      </if>
      <if test="sourceKey != null">
        #{sourceKey,jdbcType=VARCHAR},
      </if>
      <if test="sourceCompiledKey != null">
        #{sourceCompiledKey,jdbcType=VARCHAR},
      </if>
      <if test="resourceType != null">
        #{resourceType,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        #{version,jdbcType=VARCHAR},
      </if>
      <if test="corpId != null">
        #{corpId,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.alibaba.smart.processor.model.AppResourceDOExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri May 23 15:27:49 CST 2025.
    -->
    select count(*) from smart_processor_app_resource
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri May 23 15:27:49 CST 2025.
    -->
    update smart_processor_app_resource
    <set>
      <if test="row.id != null">
        id = #{row.id,jdbcType=BIGINT},
      </if>
      <if test="row.gmtCreate != null">
        gmt_create = #{row.gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="row.gmtModified != null">
        gmt_modified = #{row.gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="row.creator != null">
        creator = #{row.creator,jdbcType=VARCHAR},
      </if>
      <if test="row.modifier != null">
        modifier = #{row.modifier,jdbcType=VARCHAR},
      </if>
      <if test="row.isDeleted != null">
        is_deleted = #{row.isDeleted,jdbcType=VARCHAR},
      </if>
      <if test="row.appId != null">
        app_id = #{row.appId,jdbcType=VARCHAR},
      </if>
      <if test="row.resourceId != null">
        resource_id = #{row.resourceId,jdbcType=VARCHAR},
      </if>
      <if test="row.sourceKey != null">
        source_key = #{row.sourceKey,jdbcType=VARCHAR},
      </if>
      <if test="row.sourceCompiledKey != null">
        source_compiled_key = #{row.sourceCompiledKey,jdbcType=VARCHAR},
      </if>
      <if test="row.resourceType != null">
        resource_type = #{row.resourceType,jdbcType=VARCHAR},
      </if>
      <if test="row.version != null">
        version = #{row.version,jdbcType=VARCHAR},
      </if>
      <if test="row.corpId != null">
        corp_id = #{row.corpId,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri May 23 15:27:49 CST 2025.
    -->
    update smart_processor_app_resource
    set id = #{row.id,jdbcType=BIGINT},
      gmt_create = #{row.gmtCreate,jdbcType=TIMESTAMP},
      gmt_modified = #{row.gmtModified,jdbcType=TIMESTAMP},
      creator = #{row.creator,jdbcType=VARCHAR},
      modifier = #{row.modifier,jdbcType=VARCHAR},
      is_deleted = #{row.isDeleted,jdbcType=VARCHAR},
      app_id = #{row.appId,jdbcType=VARCHAR},
      resource_id = #{row.resourceId,jdbcType=VARCHAR},
      source_key = #{row.sourceKey,jdbcType=VARCHAR},
      source_compiled_key = #{row.sourceCompiledKey,jdbcType=VARCHAR},
      resource_type = #{row.resourceType,jdbcType=VARCHAR},
      version = #{row.version,jdbcType=VARCHAR},
      corp_id = #{row.corpId,jdbcType=VARCHAR}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.alibaba.smart.processor.model.AppResourceDO">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri May 23 15:27:49 CST 2025.
    -->
    update smart_processor_app_resource
    <set>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        modifier = #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=VARCHAR},
      </if>
      <if test="appId != null">
        app_id = #{appId,jdbcType=VARCHAR},
      </if>
      <if test="resourceId != null">
        resource_id = #{resourceId,jdbcType=VARCHAR},
      </if>
      <if test="sourceKey != null">
        source_key = #{sourceKey,jdbcType=VARCHAR},
      </if>
      <if test="sourceCompiledKey != null">
        source_compiled_key = #{sourceCompiledKey,jdbcType=VARCHAR},
      </if>
      <if test="resourceType != null">
        resource_type = #{resourceType,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        version = #{version,jdbcType=VARCHAR},
      </if>
      <if test="corpId != null">
        corp_id = #{corpId,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.alibaba.smart.processor.model.AppResourceDO">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri May 23 15:27:49 CST 2025.
    -->
    update smart_processor_app_resource
    set gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      creator = #{creator,jdbcType=VARCHAR},
      modifier = #{modifier,jdbcType=VARCHAR},
      is_deleted = #{isDeleted,jdbcType=VARCHAR},
      app_id = #{appId,jdbcType=VARCHAR},
      resource_id = #{resourceId,jdbcType=VARCHAR},
      source_key = #{sourceKey,jdbcType=VARCHAR},
      source_compiled_key = #{sourceCompiledKey,jdbcType=VARCHAR},
      resource_type = #{resourceType,jdbcType=VARCHAR},
      version = #{version,jdbcType=VARCHAR},
      corp_id = #{corpId,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <select id="selectByExampleWithRowbounds" parameterType="com.alibaba.smart.processor.model.AppResourceDOExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri May 23 15:27:49 CST 2025.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from smart_processor_app_resource
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
</mapper>