<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE generatorConfiguration
        PUBLIC "-//mybatis.org//DTD MyBatis Generator Configuration 1.0//EN"
        "http://mybatis.org/dtd/mybatis-generator-config_1_0.dtd">

<generatorConfiguration>
    <properties resource="mybatis/generator.properties"/>
    <context id="PostgreSQL" targetRuntime="MyBatis3" defaultModelType="flat">
        <property name="javaFileEncoding" value="UTF-8"/>
        <!--覆盖生成XML文件，解决suppressAllComments=true的情况下无法覆盖新生成的xml-->
        <plugin type="org.mybatis.generator.plugins.UnmergeableXmlMappersPlugin"/>
        <!-- 支持分页功能-->
        <plugin type="org.mybatis.generator.plugins.RowBoundsPlugin"/>
        <!-- 自动为Mapper文件添加@Mapper注解-->
        <plugin type="org.mybatis.generator.plugins.MapperAnnotationPlugin" />

        <jdbcConnection driverClass="${jdbc.driverClassName}" connectionURL="${jdbc.url}" userId="${jdbc.username}"
                        password="${jdbc.password}"/>

        <!-- 类型转换 -->
        <javaTypeResolver>
            <!--是否使用 bigDecimal，默认false。 false，把JDBC DECIMAL 和 NUMERIC 类型解析为 Integer
                true，把JDBC DECIMAL 和 NUMERIC 类型解析为java.math.BigDecimal -->
            <property name="forceBigDecimals" value="true"/>
            <!--默认false false，将所有 JDBC 的时间类型解析为 java.util.Date true，将 JDBC 的时间类型按如下规则解析
                DATE -> java.time.LocalDate TIME -> java.time.LocalTime TIMESTAMP -> java.time.LocalDateTime
                TIME_WITH_TIMEZONE -> java.time.OffsetTime TIMESTAMP_WITH_TIMEZONE -> java.time.OffsetDateTime -->
            <property name="useJSR310Types" value="false"/>
        </javaTypeResolver>

        <!-- 生成实体类地址 -->
        <javaModelGenerator
                targetPackage="${generator.domainPackage}"
                targetProject="../processor-dao/src/main/java">
            <!-- 是否在当前路径下新加一层schema,eg：fase路径com.oop.eksp.user.model， true:com.oop.eksp.user.model.[schemaName] -->
            <property name="enableSubPackages" value="true"/>
            <property name="trimStrings" value="true"/>
        </javaModelGenerator>

        <!-- 生成mapxml文件 -->
        <sqlMapGenerator
                targetPackage="${generator.xmlPackage}"
                targetProject="${generator.targetProject}src/main/resources">
            <!-- 是否在当前路径下新加一层schema,eg：fase路径com.oop.eksp.user.model， true:com.oop.eksp.user.model.[schemaName] -->
            <property name="enableSubPackages" value="true"/>
        </sqlMapGenerator>

        <!-- 生成mapxml对应client，也就是接口Mapper -->
        <javaClientGenerator
                targetPackage="${generator.mapperPackage}"
                targetProject="../processor-dao/src/main/java"
                type="XMLMAPPER">
            <!-- 是否在当前路径下新加一层schema,eg：fase路径com.oop.eksp.user.model， true:com.oop.eksp.user.model.[schemaName] -->
            <property name="enableSubPackages" value="true"/>
        </javaClientGenerator>
        <!--
            2022-04-10 guanhai:高版本驱动可以自增，不用按照下面的写：
            <generatedKey column="id" identity="false" type="pre" sqlStatement="SELECT nextval('core_db_info_id_seq')"/>
            可以直接写：
            <generatedKey column="id" sqlStatement="JDBC"/>，测试通过
            2022-05-25 guanhai:真特么扯犊子，个比hologres竟然出问题，还是要先select，哎。。。
        -->
        <table tableName="smart_processor_plugins" domainObjectName="PluginsDO" mapperName="PluginsMapper" enableDeleteByExample="false"
               enableDeleteByPrimaryKey="false">
            <generatedKey column="id" sqlStatement="JDBC"/>
        </table>
    </context>
</generatorConfiguration>



