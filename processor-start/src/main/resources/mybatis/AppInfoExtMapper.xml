<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.alibaba.smart.processor.mapper.AppInfoExtMapper">
  <resultMap id="BaseResultMap" type="com.alibaba.smart.processor.model.AppInfoDO">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri May 23 15:27:49 CST 2025.
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="modifier" jdbcType="VARCHAR" property="modifier" />
    <result column="is_deleted" jdbcType="VARCHAR" property="isDeleted" />
    <result column="app_id" jdbcType="VARCHAR" property="appId" />
    <result column="app_name" jdbcType="VARCHAR" property="appName" />
    <result column="app_desc" jdbcType="VARCHAR" property="appDesc" />
    <result column="app_icon" jdbcType="VARCHAR" property="appIcon" />
    <result column="session_id" jdbcType="VARCHAR" property="sessionId" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="corp_id" jdbcType="VARCHAR" property="corpId" />
    <result column="app_config" jdbcType="VARCHAR" property="appConfig" />
  </resultMap>

  <select id="listAppWithPage" resultMap="BaseResultMap">
    select * from smart_processor_app_info where is_deleted='n' and corp_id=#{corpId}
    order by gmt_modified desc
    limit #{pageIndex},#{pageSize}
  </select>
</mapper>