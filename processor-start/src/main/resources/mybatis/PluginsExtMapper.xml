<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.alibaba.smart.processor.mapper.PluginsExtMapper">
  <resultMap id="BaseResultMap" type="com.alibaba.smart.processor.model.PluginsDO">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Mon Jun 16 11:30:53 CST 2025.
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="modifier" jdbcType="VARCHAR" property="modifier" />
    <result column="is_deleted" jdbcType="VARCHAR" property="isDeleted" />
    <result column="plugin_id" jdbcType="VARCHAR" property="pluginId" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="description" jdbcType="VARCHAR" property="description" />
    <result column="icon" jdbcType="VARCHAR" property="icon" />
    <result column="type" jdbcType="VARCHAR" property="type" />
    <result column="auth_config" jdbcType="VARCHAR" property="authConfig" />
    <result column="content" jdbcType="LONGVARCHAR" property="content" />
    <result column="corp_id" jdbcType="VARCHAR" property="corpId" />
  </resultMap>

  <select id="listPlugins" resultMap="BaseResultMap">
    select * from smart_processor_plugins where is_deleted='n' and corp_id=#{corpId}
    <if test="key != null and key !=''">
      and (
        name like CONCAT('%',#{key},'%') or
        description like CONCAT('%',#{key},'%')
      )
    </if>
    order by gmt_create desc
    limit #{pageIndex},#{pageSize}
  </select>

  <select id="countPlugins" resultType="java.lang.Long">
    select count(*) from smart_processor_plugins where is_deleted='n' and corp_id=#{corpId}
    <if test="key != null and key !=''">
      and (
        name like CONCAT('%',#{key},'%') or
        description like CONCAT('%',#{key},'%')
      )
    </if>
  </select>
</mapper>