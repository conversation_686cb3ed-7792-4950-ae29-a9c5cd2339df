<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.alibaba.smart.processor.mapper.AppVersionExtMapper">
  <resultMap id="BaseResultMap" type="com.alibaba.smart.processor.model.AppVersionDO">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Fri May 23 15:27:49 CST 2025.
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="modifier" jdbcType="VARCHAR" property="modifier" />
    <result column="is_deleted" jdbcType="VARCHAR" property="isDeleted" />
    <result column="app_id" jdbcType="VARCHAR" property="appId" />
    <result column="version" jdbcType="VARCHAR" property="version" />
    <result column="resource_id" jdbcType="VARCHAR" property="resourceId" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="update_note" jdbcType="LONGVARCHAR" property="updateNote" />
  </resultMap>

  <select id="listAppVersion" resultMap="BaseResultMap">
    select * from smart_processor_app_version where is_deleted='n' and app_id=#{appId}
    <if test="key != null and key !=''">
      and update_note like CONCAT('%',#{key},'%')
    </if>
    order by gmt_create desc
      limit #{pageIndex},#{pageSize}
  </select>

  <select id="countAppVersion" resultType="java.lang.Long">
    select count(*) from smart_processor_app_version where is_deleted='n' and app_id=#{appId}
    <if test="key != null and key !=''">
      and update_note like CONCAT('%',#{key},'%')
    </if>
  </select>
</mapper>