<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.alibaba.smart.processor.mapper.ToolInfoMapper">
  <resultMap id="BaseResultMap" type="com.alibaba.smart.processor.model.ToolInfoDO">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu May 15 19:19:16 CST 2025.
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_modified" jdbcType="TIMESTAMP" property="gmtModified" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="modifier" jdbcType="VARCHAR" property="modifier" />
    <result column="is_deleted" jdbcType="VARCHAR" property="isDeleted" />
    <result column="corp_id" jdbcType="VARCHAR" property="corpId" />
    <result column="session_id" jdbcType="VARCHAR" property="sessionId" />
    <result column="agent_id" jdbcType="VARCHAR" property="agentId" />
    <result column="uuid" jdbcType="VARCHAR" property="uuid" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="type" jdbcType="VARCHAR" property="type" />
    <result column="description" jdbcType="VARCHAR" property="description" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="version" jdbcType="VARCHAR" property="version" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.alibaba.smart.processor.model.ToolInfoDO">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu May 15 19:19:16 CST 2025.
    -->
    <result column="content" jdbcType="LONGVARCHAR" property="content" />
    <result column="request_format" jdbcType="LONGVARCHAR" property="requestFormat" />
    <result column="response_format" jdbcType="LONGVARCHAR" property="responseFormat" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu May 15 19:19:16 CST 2025.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu May 15 19:19:16 CST 2025.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu May 15 19:19:16 CST 2025.
    -->
    id, gmt_create, gmt_modified, creator, modifier, is_deleted, corp_id, session_id, 
    agent_id, uuid, name, type, description, status, version
  </sql>
  <sql id="Blob_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu May 15 19:19:16 CST 2025.
    -->
    content, request_format, response_format
  </sql>
  <select id="selectByExampleWithBLOBs" parameterType="com.alibaba.smart.processor.model.ToolInfoDOExample" resultMap="ResultMapWithBLOBs">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu May 15 19:19:16 CST 2025.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from smart_processor_tool_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExample" parameterType="com.alibaba.smart.processor.model.ToolInfoDOExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu May 15 19:19:16 CST 2025.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from smart_processor_tool_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="ResultMapWithBLOBs">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu May 15 19:19:16 CST 2025.
    -->
    select 
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from smart_processor_tool_info
    where id = #{id,jdbcType=BIGINT}
  </select>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.alibaba.smart.processor.model.ToolInfoDO" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu May 15 19:19:16 CST 2025.
    -->
    insert into smart_processor_tool_info (gmt_create, gmt_modified, creator, 
      modifier, is_deleted, corp_id, 
      session_id, agent_id, uuid, 
      name, type, description, 
      status, version, content, 
      request_format, response_format)
    values (#{gmtCreate,jdbcType=TIMESTAMP}, #{gmtModified,jdbcType=TIMESTAMP}, #{creator,jdbcType=VARCHAR}, 
      #{modifier,jdbcType=VARCHAR}, #{isDeleted,jdbcType=VARCHAR}, #{corpId,jdbcType=VARCHAR}, 
      #{sessionId,jdbcType=VARCHAR}, #{agentId,jdbcType=VARCHAR}, #{uuid,jdbcType=VARCHAR}, 
      #{name,jdbcType=VARCHAR}, #{type,jdbcType=VARCHAR}, #{description,jdbcType=VARCHAR}, 
      #{status,jdbcType=VARCHAR}, #{version,jdbcType=VARCHAR}, #{content,jdbcType=LONGVARCHAR}, 
      #{requestFormat,jdbcType=LONGVARCHAR}, #{responseFormat,jdbcType=LONGVARCHAR})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.alibaba.smart.processor.model.ToolInfoDO" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu May 15 19:19:16 CST 2025.
    -->
    insert into smart_processor_tool_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="gmtModified != null">
        gmt_modified,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="modifier != null">
        modifier,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="corpId != null">
        corp_id,
      </if>
      <if test="sessionId != null">
        session_id,
      </if>
      <if test="agentId != null">
        agent_id,
      </if>
      <if test="uuid != null">
        uuid,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="type != null">
        type,
      </if>
      <if test="description != null">
        description,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="version != null">
        version,
      </if>
      <if test="content != null">
        content,
      </if>
      <if test="requestFormat != null">
        request_format,
      </if>
      <if test="responseFormat != null">
        response_format,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=VARCHAR},
      </if>
      <if test="corpId != null">
        #{corpId,jdbcType=VARCHAR},
      </if>
      <if test="sessionId != null">
        #{sessionId,jdbcType=VARCHAR},
      </if>
      <if test="agentId != null">
        #{agentId,jdbcType=VARCHAR},
      </if>
      <if test="uuid != null">
        #{uuid,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        #{type,jdbcType=VARCHAR},
      </if>
      <if test="description != null">
        #{description,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        #{version,jdbcType=VARCHAR},
      </if>
      <if test="content != null">
        #{content,jdbcType=LONGVARCHAR},
      </if>
      <if test="requestFormat != null">
        #{requestFormat,jdbcType=LONGVARCHAR},
      </if>
      <if test="responseFormat != null">
        #{responseFormat,jdbcType=LONGVARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.alibaba.smart.processor.model.ToolInfoDOExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu May 15 19:19:16 CST 2025.
    -->
    select count(*) from smart_processor_tool_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu May 15 19:19:16 CST 2025.
    -->
    update smart_processor_tool_info
    <set>
      <if test="row.id != null">
        id = #{row.id,jdbcType=BIGINT},
      </if>
      <if test="row.gmtCreate != null">
        gmt_create = #{row.gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="row.gmtModified != null">
        gmt_modified = #{row.gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="row.creator != null">
        creator = #{row.creator,jdbcType=VARCHAR},
      </if>
      <if test="row.modifier != null">
        modifier = #{row.modifier,jdbcType=VARCHAR},
      </if>
      <if test="row.isDeleted != null">
        is_deleted = #{row.isDeleted,jdbcType=VARCHAR},
      </if>
      <if test="row.corpId != null">
        corp_id = #{row.corpId,jdbcType=VARCHAR},
      </if>
      <if test="row.sessionId != null">
        session_id = #{row.sessionId,jdbcType=VARCHAR},
      </if>
      <if test="row.agentId != null">
        agent_id = #{row.agentId,jdbcType=VARCHAR},
      </if>
      <if test="row.uuid != null">
        uuid = #{row.uuid,jdbcType=VARCHAR},
      </if>
      <if test="row.name != null">
        name = #{row.name,jdbcType=VARCHAR},
      </if>
      <if test="row.type != null">
        type = #{row.type,jdbcType=VARCHAR},
      </if>
      <if test="row.description != null">
        description = #{row.description,jdbcType=VARCHAR},
      </if>
      <if test="row.status != null">
        status = #{row.status,jdbcType=VARCHAR},
      </if>
      <if test="row.version != null">
        version = #{row.version,jdbcType=VARCHAR},
      </if>
      <if test="row.content != null">
        content = #{row.content,jdbcType=LONGVARCHAR},
      </if>
      <if test="row.requestFormat != null">
        request_format = #{row.requestFormat,jdbcType=LONGVARCHAR},
      </if>
      <if test="row.responseFormat != null">
        response_format = #{row.responseFormat,jdbcType=LONGVARCHAR},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu May 15 19:19:16 CST 2025.
    -->
    update smart_processor_tool_info
    set id = #{row.id,jdbcType=BIGINT},
      gmt_create = #{row.gmtCreate,jdbcType=TIMESTAMP},
      gmt_modified = #{row.gmtModified,jdbcType=TIMESTAMP},
      creator = #{row.creator,jdbcType=VARCHAR},
      modifier = #{row.modifier,jdbcType=VARCHAR},
      is_deleted = #{row.isDeleted,jdbcType=VARCHAR},
      corp_id = #{row.corpId,jdbcType=VARCHAR},
      session_id = #{row.sessionId,jdbcType=VARCHAR},
      agent_id = #{row.agentId,jdbcType=VARCHAR},
      uuid = #{row.uuid,jdbcType=VARCHAR},
      name = #{row.name,jdbcType=VARCHAR},
      type = #{row.type,jdbcType=VARCHAR},
      description = #{row.description,jdbcType=VARCHAR},
      status = #{row.status,jdbcType=VARCHAR},
      version = #{row.version,jdbcType=VARCHAR},
      content = #{row.content,jdbcType=LONGVARCHAR},
      request_format = #{row.requestFormat,jdbcType=LONGVARCHAR},
      response_format = #{row.responseFormat,jdbcType=LONGVARCHAR}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu May 15 19:19:16 CST 2025.
    -->
    update smart_processor_tool_info
    set id = #{row.id,jdbcType=BIGINT},
      gmt_create = #{row.gmtCreate,jdbcType=TIMESTAMP},
      gmt_modified = #{row.gmtModified,jdbcType=TIMESTAMP},
      creator = #{row.creator,jdbcType=VARCHAR},
      modifier = #{row.modifier,jdbcType=VARCHAR},
      is_deleted = #{row.isDeleted,jdbcType=VARCHAR},
      corp_id = #{row.corpId,jdbcType=VARCHAR},
      session_id = #{row.sessionId,jdbcType=VARCHAR},
      agent_id = #{row.agentId,jdbcType=VARCHAR},
      uuid = #{row.uuid,jdbcType=VARCHAR},
      name = #{row.name,jdbcType=VARCHAR},
      type = #{row.type,jdbcType=VARCHAR},
      description = #{row.description,jdbcType=VARCHAR},
      status = #{row.status,jdbcType=VARCHAR},
      version = #{row.version,jdbcType=VARCHAR}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.alibaba.smart.processor.model.ToolInfoDO">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu May 15 19:19:16 CST 2025.
    -->
    update smart_processor_tool_info
    <set>
      <if test="gmtCreate != null">
        gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="gmtModified != null">
        gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="modifier != null">
        modifier = #{modifier,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=VARCHAR},
      </if>
      <if test="corpId != null">
        corp_id = #{corpId,jdbcType=VARCHAR},
      </if>
      <if test="sessionId != null">
        session_id = #{sessionId,jdbcType=VARCHAR},
      </if>
      <if test="agentId != null">
        agent_id = #{agentId,jdbcType=VARCHAR},
      </if>
      <if test="uuid != null">
        uuid = #{uuid,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="type != null">
        type = #{type,jdbcType=VARCHAR},
      </if>
      <if test="description != null">
        description = #{description,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        version = #{version,jdbcType=VARCHAR},
      </if>
      <if test="content != null">
        content = #{content,jdbcType=LONGVARCHAR},
      </if>
      <if test="requestFormat != null">
        request_format = #{requestFormat,jdbcType=LONGVARCHAR},
      </if>
      <if test="responseFormat != null">
        response_format = #{responseFormat,jdbcType=LONGVARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.alibaba.smart.processor.model.ToolInfoDO">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu May 15 19:19:16 CST 2025.
    -->
    update smart_processor_tool_info
    set gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      creator = #{creator,jdbcType=VARCHAR},
      modifier = #{modifier,jdbcType=VARCHAR},
      is_deleted = #{isDeleted,jdbcType=VARCHAR},
      corp_id = #{corpId,jdbcType=VARCHAR},
      session_id = #{sessionId,jdbcType=VARCHAR},
      agent_id = #{agentId,jdbcType=VARCHAR},
      uuid = #{uuid,jdbcType=VARCHAR},
      name = #{name,jdbcType=VARCHAR},
      type = #{type,jdbcType=VARCHAR},
      description = #{description,jdbcType=VARCHAR},
      status = #{status,jdbcType=VARCHAR},
      version = #{version,jdbcType=VARCHAR},
      content = #{content,jdbcType=LONGVARCHAR},
      request_format = #{requestFormat,jdbcType=LONGVARCHAR},
      response_format = #{responseFormat,jdbcType=LONGVARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.alibaba.smart.processor.model.ToolInfoDO">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu May 15 19:19:16 CST 2025.
    -->
    update smart_processor_tool_info
    set gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_modified = #{gmtModified,jdbcType=TIMESTAMP},
      creator = #{creator,jdbcType=VARCHAR},
      modifier = #{modifier,jdbcType=VARCHAR},
      is_deleted = #{isDeleted,jdbcType=VARCHAR},
      corp_id = #{corpId,jdbcType=VARCHAR},
      session_id = #{sessionId,jdbcType=VARCHAR},
      agent_id = #{agentId,jdbcType=VARCHAR},
      uuid = #{uuid,jdbcType=VARCHAR},
      name = #{name,jdbcType=VARCHAR},
      type = #{type,jdbcType=VARCHAR},
      description = #{description,jdbcType=VARCHAR},
      status = #{status,jdbcType=VARCHAR},
      version = #{version,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <select id="selectByExampleWithBLOBsWithRowbounds" parameterType="com.alibaba.smart.processor.model.ToolInfoDOExample" resultMap="ResultMapWithBLOBs">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu May 15 19:19:16 CST 2025.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from smart_processor_tool_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExampleWithRowbounds" parameterType="com.alibaba.smart.processor.model.ToolInfoDOExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu May 15 19:19:16 CST 2025.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from smart_processor_tool_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>

  <update id="updateByToolId">
    update smart_processor_tool_info set gmt_modified=now(), is_deleted='y'
    where uuid=#{toolId}
  </update>

  <delete id="deleteByToolId">
    DELETE FROM smart_processor_tool_info
    WHERE uuid=#{toolId}
  </delete>


  <select id="selectByToolId" parameterType="java.lang.String" resultMap="ResultMapWithBLOBs">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
      This element was generated on Thu May 15 19:19:16 CST 2025.
    -->
    select
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from smart_processor_tool_info
    where uuid = #{toolId}
  </select>
</mapper>