(()=>{var t={390:(t,n)=>{"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.isWasmPipe=n.isJSPipe=n.SysEventTopic=n.Monitor2MainTells=n.Main2MonitorWorkerAsks=n.Worker2FSWorkerAsks=n.Main2FSWorkerAsks=n.SWBroadcastEvents=n.SWTopic=n.SWActions=n.Main2WorkerAsks=n.Main2WorkerTells=n.Worker2MainAsks=n.Worker2MainTells=void 0,n.Worker2MainTells={HttpListen:"httpListen",StopHttpListen:"StopHttpListen",ExecStart:"execStart",Exit:"exit"},n.Worker2MainAsks={Spawn:"spawn",Fork:"fork",Clone:"clone",Exec:"exec",RunThread:"runThread",StopThread:"stopThread",PosixWaitpid:"posix_waitpid",WaitPid:"waitpid",Kill:"kill",FlushChildOutput:"flushOutput",AddFSEventWatch:"addFSEventWatch",RmFSEventWatch:"rmFSEventWatch",BlockGetTTYWindowCols:"BlockGetTTYWindowCols",BlockGetTTYWindowRows:"BlockGetTTYWindowRows",OpenUrl:"openUrl",HttpRequest:"httpRequest",ConnectFSWorker:"connectFSWorker",Malloc:"malloc",Free:"free",StartPipe:"startPipe"},n.Main2WorkerTells={ChildExit:"childExit",ThreadExit:"threadExit",Kill:"kill",FSEvent:"FSEvent"},n.Main2WorkerAsks={Init:"init",Exec:"exec"},n.SWActions={StartListen:"startListen",StopListen:"stopListen",SetBindingsMap:"setBindingsMap"},n.SWTopic="webc::sw",n.SWBroadcastEvents={Bootstrap:"bootstrap"},n.Main2FSWorkerAsks={Init:"init",Connect:"connect",Close:"close",Load:"load",Store:"store",DelPersistence:"delp",SpaceSum:"spaceSum",StartPipe:"startPipe",StopPipe:"stopPipe",DownloadPackage:"downloadPackage",DownloadFile:"downloadFile"},n.Worker2FSWorkerAsks={Write:"write",Read:"read"},n.Main2MonitorWorkerAsks={Init:"init",GetSysLogs:"getLogs"},n.Monitor2MainTells={SysEvent:"sysEvent"},n.SysEventTopic="webc-sysevent",n.isJSPipe=function(t){return t instanceof MessagePort},n.isWasmPipe=function(t){return t&&"number"==typeof t.fd&&t.pid}},477:(t,n)=>{"use strict";Object.defineProperty(n,"__esModule",{value:!0}),n.constructUrlWithInstanceAndPort=n.getOriginUrlFromMockedUrl=n.extractLocalHttpUrl=void 0,n.extractLocalHttpUrl=function(t){if(!t)return[null,null];const n=t.match(/_i\/([a-zA-Z0-9]+)\/_p\/(\d+)/);if(n){const t=n[1];let e=n[2];return e=parseInt(e),[t,e]}return[null,null]},n.getOriginUrlFromMockedUrl=function(t,n,e){const r=t.split(`/_i/${n}/_p/${e}`);if(r&&r.length>1)return r[1];{const n=new URL(t).origin;return t.replace(n,"")}},n.constructUrlWithInstanceAndPort=function(t,n,e){const r=new URL(t),i=r.pathname+r.search+r.hash;return`${r.origin}/_i/${n}/_p/${e}${i}`}},896:(t,n,e)=>{function r(t,n,e){if(!(this instanceof r))return new r(t,n,e);if(this.parent=this,this.offset=0,"base64"==n&&"string"==typeof t)for(t=(i=t).trim?i.trim():i.replace(/^\s+|\s+$/g,"");t.length%4!=0;)t+="=";var i,u;if("number"==typeof e){this.length=s(n);for(var c=0;c<this.length;c++)this[c]=t.get(c+e)}else{switch(u=typeof t){case"number":this.length=s(t);break;case"string":this.length=r.byteLength(t,n);break;case"object":this.length=s(t.length);break;default:throw new TypeError("First argument needs to be a number, array or string.")}if(function(t){return o(t)||r.isBuffer(t)||t&&"object"==typeof t&&"number"==typeof t.length}(t))for(c=0;c<this.length;c++)this[c]=t instanceof r?t.readUInt8(c):(t[c]%256+256)%256;else if("string"==u)this.length=this.write(t,0,n);else if("number"===u)for(c=0;c<this.length;c++)this[c]=0}}function i(t,n,e){return"number"!=typeof t?e:(t=~~t)>=n?n:t>=0||(t+=n)>=0?t:0}function s(t){return(t=~~Math.ceil(+t))<0?0:t}function o(t){return(Array.isArray||function(t){return"[object Array]"=={}.toString.apply(t)})(t)}function u(t){return t<16?"0"+t.toString(16):t.toString(16)}n.hp=r,r.poolSize=8192,n.IS=50,r.prototype.get=function(t){if(t<0||t>=this.length)throw new Error("oob");return this[t]},r.prototype.set=function(t,n){if(t<0||t>=this.length)throw new Error("oob");return this[t]=n},r.byteLength=function(t,n){switch(n||"utf8"){case"hex":return t.length/2;case"utf8":case"utf-8":return a(t).byteLength;case"ascii":case"binary":return t.length;case"base64":return f(t).length;default:throw new Error("Unknown encoding")}},r.prototype.utf8Write=function(t,n,e){return r._charsWritten=h(a(t),this,n,e)},r.prototype.asciiWrite=function(t,n,e){return r._charsWritten=h(function(t){for(var n=[],e=0;e<t.length;e++)n.push(255&t.charCodeAt(e));return n}(t),this,n,e)},r.prototype.binaryWrite=r.prototype.asciiWrite,r.prototype.base64Write=function(t,n,e){return r._charsWritten=h(f(t),this,n,e)},r.prototype.utf8Slice=function(){for(var t=Array.prototype.slice.apply(this,arguments),n="",e="",r=0;r<t.length;)t[r]<=127?(n+=l(e)+String.fromCharCode(t[r]),e=""):e+="%"+t[r].toString(16),r++;return n+l(e)},r.prototype.asciiSlice=function(){for(var t=Array.prototype.slice.apply(this,arguments),n="",e=0;e<t.length;e++)n+=String.fromCharCode(t[e]);return n},r.prototype.binarySlice=r.prototype.asciiSlice,r.prototype.inspect=function(){for(var t=[],e=this.length,r=0;r<e;r++)if(t[r]=u(this[r]),r==n.IS){t[r+1]="...";break}return"<Buffer "+t.join(" ")+">"},r.prototype.hexSlice=function(t,n){var e=this.length;(!t||t<0)&&(t=0),(!n||n<0||n>e)&&(n=e);for(var r="",i=t;i<n;i++)r+=u(this[i]);return r},r.prototype.toString=function(t,n,e){if(t=String(t||"utf8").toLowerCase(),n=+n||0,void 0===e&&(e=this.length),+e==n)return"";switch(t){case"hex":return this.hexSlice(n,e);case"utf8":case"utf-8":return this.utf8Slice(n,e);case"ascii":return this.asciiSlice(n,e);case"binary":return this.binarySlice(n,e);case"base64":return this.base64Slice(n,e);case"ucs2":case"ucs-2":return this.ucs2Slice(n,e);default:throw new Error("Unknown encoding")}},r.prototype.hexWrite=function(t,n,e){n=+n||0;var i=this.length-n;e?(e=+e)>i&&(e=i):e=i;var s=t.length;if(s%2)throw new Error("Invalid hex string");e>s/2&&(e=s/2);for(var o=0;o<e;o++){var u=parseInt(t.substr(2*o,2),16);if(isNaN(u))throw new Error("Invalid hex string");this[n+o]=u}return r._charsWritten=2*o,o},r.prototype.write=function(t,n,e,r){if(isFinite(n))isFinite(e)||(r=e,e=void 0);else{var i=r;r=n,n=e,e=i}n=+n||0;var s=this.length-n;switch(e?(e=+e)>s&&(e=s):e=s,r=String(r||"utf8").toLowerCase()){case"hex":return this.hexWrite(t,n,e);case"utf8":case"utf-8":return this.utf8Write(t,n,e);case"ascii":return this.asciiWrite(t,n,e);case"binary":return this.binaryWrite(t,n,e);case"base64":return this.base64Write(t,n,e);case"ucs2":case"ucs-2":return this.ucs2Write(t,n,e);default:throw new Error("Unknown encoding")}},r.prototype.slice=function(t,n){var e=this.length;return t=i(t,e,0),new r(this,(n=i(n,e,e))-t,+t)},r.prototype.copy=function(t,n,e,r){var i=this;if(e||(e=0),(void 0===r||isNaN(r))&&(r=this.length),n||(n=0),r<e)throw new Error("sourceEnd < sourceStart");if(r===e)return 0;if(0==t.length||0==i.length)return 0;if(n<0||n>=t.length)throw new Error("targetStart out of bounds");if(e<0||e>=i.length)throw new Error("sourceStart out of bounds");if(r<0||r>i.length)throw new Error("sourceEnd out of bounds");r>this.length&&(r=this.length),t.length-n<r-e&&(r=t.length-n+e);for(var s=[],o=e;o<r;o++)s.push(this[o]);for(o=n;o<n+s.length;o++)t[o]=s[o-n]},r.prototype.fill=function(t,n,e){if(t||(t=0),n||(n=0),e||(e=this.length),"string"==typeof t&&(t=t.charCodeAt(0)),"number"!=typeof t||isNaN(t))throw new Error("value is not a number");if(e<n)throw new Error("end < start");if(e===n)return 0;if(0==this.length)return 0;if(n<0||n>=this.length)throw new Error("start out of bounds");if(e<0||e>this.length)throw new Error("end out of bounds");for(var r=n;r<e;r++)this[r]=t},r.isBuffer=function(t){return t instanceof r},r.concat=function(t,n){if(!o(t))throw new Error("Usage: Buffer.concat(list, [totalLength])\n       list should be an Array.");if(0===t.length)return new r(0);if(1===t.length)return t[0];if("number"!=typeof n){n=0;for(var e=0;e<t.length;e++){n+=(u=t[e]).length}}var i=new r(n),s=0;for(e=0;e<t.length;e++){var u;(u=t[e]).copy(i,s),s+=u.length}return i},r.isEncoding=function(t){switch((t+"").toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"binary":case"base64":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":case"raw":return!0;default:return!1}};const c=new TextEncoder;function a(t){return c.encode(t)}function f(t){console.error("base64 encoding not impled!")}function h(t,n,e,r){for(var i=0;i<r&&!(i+e>=n.length||i>=t.length);)n[i+e]=t[i],i++;return i}function l(t){try{return decodeURIComponent(t)}catch(t){return String.fromCharCode(65533)}}function d(t,n,e,r){var i=0;return n>=t.length?0:(e?(i=t[n]<<8,n+1<t.length&&(i|=t[n+1])):(i=t[n],n+1<t.length&&(i|=t[n+1]<<8)),i)}function p(t,n,e,r){var i=0;return n>=t.length?0:(e?(n+1<t.length&&(i=t[n+1]<<16),n+2<t.length&&(i|=t[n+2]<<8),n+3<t.length&&(i|=t[n+3]),i+=t[n]<<24>>>0):(n+2<t.length&&(i=t[n+2]<<16),n+1<t.length&&(i|=t[n+1]<<8),i|=t[n],n+3<t.length&&(i+=t[n+3]<<24>>>0)),i)}function w(t,n,e,r){var i;return 32768&(i=d(t,n,e))?-1*(65535-i+1):i}function g(t,n,e,r){var i;return 2147483648&(i=p(t,n,e))?-1*(4294967295-i+1):i}function b(t,n,r,i){return e(487).readIEEE754(t,n,r,23,4)}function v(t,n,r,i){return e(487).readIEEE754(t,n,r,52,8)}function y(t,n,e,r,i){for(var s=0;s<Math.min(t.length-e,2);s++)t[e+s]=(n&255<<8*(r?1-s:s))>>>8*(r?1-s:s)}function E(t,n,e,r,i){for(var s=0;s<Math.min(t.length-e,4);s++)t[e+s]=n>>>8*(r?3-s:s)&255}function m(t,n,e,r,i){y(t,n>=0?n:65535+n+1,e,r)}function S(t,n,e,r,i){E(t,n>=0?n:4294967295+n+1,e,r)}function M(t,n,r,i,s){e(487).writeIEEE754(t,n,r,i,23,4)}function T(t,n,r,i,s){e(487).writeIEEE754(t,n,r,i,52,8)}r.prototype.readUInt8=function(t,n){if(!(t>=this.length))return this[t]},r.prototype.readUInt16LE=function(t,n){return d(this,t,!1)},r.prototype.readUInt16BE=function(t,n){return d(this,t,!0)},r.prototype.readUInt32LE=function(t,n){return p(this,t,!1)},r.prototype.readUInt32BE=function(t,n){return p(this,t,!0)},r.prototype.readInt8=function(t,n){var e=this;if(!(t>=e.length))return 128&e[t]?-1*(255-e[t]+1):e[t]},r.prototype.readInt16LE=function(t,n){return w(this,t,!1)},r.prototype.readInt16BE=function(t,n){return w(this,t,!0)},r.prototype.readInt32LE=function(t,n){return g(this,t,!1)},r.prototype.readInt32BE=function(t,n){return g(this,t,!0)},r.prototype.readFloatLE=function(t,n){return b(this,t,!1)},r.prototype.readFloatBE=function(t,n){return b(this,t,!0)},r.prototype.readDoubleLE=function(t,n){return v(this,t,!1)},r.prototype.readDoubleBE=function(t,n){return v(this,t,!0)},r.prototype.writeUInt8=function(t,n,e){n<this.length&&(this[n]=t)},r.prototype.writeUInt16LE=function(t,n,e){y(this,t,n,!1)},r.prototype.writeUInt16BE=function(t,n,e){y(this,t,n,!0)},r.prototype.writeUInt32LE=function(t,n,e){E(this,t,n,!1)},r.prototype.writeUInt32BE=function(t,n,e){E(this,t,n,!0)},r.prototype.writeInt8=function(t,n,e){t>=0?this.writeUInt8(t,n,e):this.writeUInt8(255+t+1,n,e)},r.prototype.writeInt16LE=function(t,n,e){m(this,t,n,!1)},r.prototype.writeInt16BE=function(t,n,e){m(this,t,n,!0)},r.prototype.writeInt32LE=function(t,n,e){S(this,t,n,!1)},r.prototype.writeInt32BE=function(t,n,e){S(this,t,n,!0)},r.prototype.writeFloatLE=function(t,n,e){M(this,t,n,!1)},r.prototype.writeFloatBE=function(t,n,e){M(this,t,n,!0)},r.prototype.writeDoubleLE=function(t,n,e){T(this,t,n,!1)},r.prototype.writeDoubleBE=function(t,n,e){T(this,t,n,!0)}},487:(t,n)=>{n.readIEEE754=function(t,n,e,r,i){var s,o,u=8*i-r-1,c=(1<<u)-1,a=c>>1,f=-7,h=e?0:i-1,l=e?1:-1,d=t[n+h];for(h+=l,s=d&(1<<-f)-1,d>>=-f,f+=u;f>0;s=256*s+t[n+h],h+=l,f-=8);for(o=s&(1<<-f)-1,s>>=-f,f+=r;f>0;o=256*o+t[n+h],h+=l,f-=8);if(0===s)s=1-a;else{if(s===c)return o?NaN:1/0*(d?-1:1);o+=Math.pow(2,r),s-=a}return(d?-1:1)*o*Math.pow(2,s-r)},n.writeIEEE754=function(t,n,e,r,i,s){var o,u,c,a=8*s-i-1,f=(1<<a)-1,h=f>>1,l=23===i?Math.pow(2,-24)-Math.pow(2,-77):0,d=r?s-1:0,p=r?-1:1,w=n<0||0===n&&1/n<0?1:0;for(n=Math.abs(n),isNaN(n)||n===1/0?(u=isNaN(n)?1:0,o=f):(o=Math.floor(Math.log(n)/Math.LN2),n*(c=Math.pow(2,-o))<1&&(o--,c*=2),(n+=o+h>=1?l/c:l*Math.pow(2,1-h))*c>=2&&(o++,c/=2),o+h>=f?(u=0,o=f):o+h>=1?(u=(n*c-1)*Math.pow(2,i),o+=h):(u=n*Math.pow(2,h-1)*Math.pow(2,i),o=0));i>=8;t[e+d]=255&u,d+=p,u/=256,i-=8);for(o=o<<i|u,a+=i;a>0;t[e+d]=255&o,d+=p,o/=256,a-=8);t[e+d-p]|=128*w}},290:(t,n,e)=>{var r=e(896).hp;const i=new TextEncoder,s=new TextDecoder;function o(t){const n={httpVersion:null,statusCode:null,statusMessage:null,method:null,url:null,headers:null,body:null,boundary:null,multipart:null};var e="",r=0,u=null;if(o._isBuffer(t))e=t.toString();else{if("string"!=typeof t)return n;e=t,t=o._createBuffer(e)}if(e=e.replace(/\r\n/gim,"\n"),function(){const n=e.search(/[\w-]+/gim);n>0&&(t=t.slice(n,t.length),e=t.toString())}(),function(){const t=e.split(/\n|\r\n/)[0],r=t.match(o._requestLineRegex);if(Array.isArray(r)&&r.length>1)n.httpVersion=parseFloat(r[1]),n.statusCode=parseInt(r[2]),n.statusMessage=r[3];else{const e=t.match(o._responseLineRegex);Array.isArray(e)&&e.length>1&&(n.method=e[1],n.url=e[2],n.httpVersion=parseFloat(e[3]))}}(),function(){(r=e.search(o._headerNewlineRegex))>-1?r+=1:n.httpVersion&&(r=e.length);const t=e.substr(0,r),i=o._parseHeaders(t);Object.keys(i).length>0&&(n.headers=i)}(),function(){if(!n.boundary){const t=e.match(o._boundaryRegex);if(Array.isArray(t)&&t.length){const e=(u=t[0].replace(/[\r\n]+/gi,"")).replace(/^--/,"");n.boundary=e}}}(),function(){var i=r,s=t.length;if(u){const t=e.indexOf(u);t>-1&&(i=r,s=t)}if(r>-1){const t=e.slice(i,s);t&&t.length&&(n.body=t)}}(),"chunked"===n.headers["Transfer-Encoding"]){const a=[],f=n.body,h=i.encode(f),l=h.byteLength;let d=0,p=0;function c(){let t=d,n=0;for(;d<l;){if(10==h[d]){n=s.decode(h.subarray(t,d)),d++;break}d++}return n=parseInt(n,16),n}for(;;){const b=c();if(0==b)break;a.push(h.subarray(d,d+b)),p+=b,d+=b+1}const w=new Uint8Array(p);let g=0;for(let v of a)w.set(v,g),g+=v.byteLength;n.body=s.decode(w)}return function(){if(n.boundary){const r=e.indexOf(u)+u.length,i=e.lastIndexOf(u),s=e.substr(r,i),c=new RegExp("^"+u+".*[\n\r]?$","gm"),a=s.split(c);n.multipart=a.filter(o._isTruthy).map((function(n,e){const r={headers:null,body:null,meta:{body:{byteOffset:{start:null,end:null}}}},i=/\n\n|\r\n\r\n/gim;var s=0,c=i.exec(n),a=null;c&&(s=c.index,c.index<=0&&(c=i.exec(n))&&(s=c.index));const f=n.substr(0,s);var h=null,l=null;if(s>-1){const i=o._parseHeaders(f);if(Object.keys(i).length>0){r.headers=i;for(var d=[],p=0;p>=0;)(p=t.indexOf(u,p))>=0&&(d.push(p),p+=u.length);var w=[];d.slice(0,d.length-1).forEach((function(n,e){var r=t.slice(d[e],d[e+1]).toString().search(/\n\n|\r\n\r\n/gim)+2;r=d[e]+r,w.push(r)})),h=w[e],l=d[e+1],a=t.slice(h,l)}else a=n}else a=n;return r.body=a,r.meta.body.byteOffset.start=h,r.meta.body.byteOffset.end=l,r}))}}(),n}o._isTruthy=function(t){return!!t},o._isNumeric=function(t){return"number"==typeof t&&!isNaN(t)||!!(t=(t||"").toString().trim())&&!isNaN(t)},o._isBuffer=function(t){return o._isNodeBufferSupported()&&"object"==typeof e.g&&e.g.Buffer.isBuffer(t)||t instanceof Object&&t._isBuffer},o._isNodeBufferSupported=function(){return"object"==typeof e.g&&"function"==typeof e.g.Buffer&&"function"==typeof e.g.Buffer.isBuffer},o._parseHeaders=function(t){const n={};return"string"!=typeof t||t.split(/[\r\n]/).forEach((function(t){const e=t.match(/([\w-]+):\s*(.*)/i);if(Array.isArray(e)&&3===e.length){const t=e[1],r=e[2];n[t]=o._isNumeric(r)?Number(r):r}})),n},o._requestLineRegex=/HTTP\/(1\.0|1\.1|2\.0)\s+(\d+)\s+([\w\s-_]+)/i,o._responseLineRegex=/(GET|POST|PUT|DELETE|PATCH|OPTIONS|HEAD|TRACE|CONNECT)\s+(.*)\s+HTTP\/(1\.0|1\.1|2\.0)/i,o._headerNewlineRegex=/^[\r\n]+/gim,o._boundaryRegex=/(\n|\r\n)+--[\w-]+(\n|\r\n)+/g,o._createBuffer=function(t){return new r(t)},o._isInBrowser=function(){return!0},t.exports=o}},n={};function e(r){var i=n[r];if(void 0!==i)return i.exports;var s=n[r]={exports:{}};return t[r](s,s.exports,e),s.exports}e.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(t){if("object"==typeof window)return window}}();const{SWActions:r,SWTopic:i,SWBroadcastEvents:s}=e(390),{extractLocalHttpUrl:o,getOriginUrlFromMockedUrl:u,constructUrlWithInstanceAndPort:c}=e(477),a=e(290),f=new TextDecoder,h=new BroadcastChannel(i);class l{bindingsMap=new Map;bypassRulesMap=new Map;addBinding(t,n){this.bindingsMap.set(t,n)}rmBinding(t){this.bindingsMap.delete(t),this.bypassRulesMap.delete(t)}getBinding(t){return this.bindingsMap.get(t)}isEmpty(){return 0===this.bindingsMap.size}setBypassRules(t,n){this.bypassRulesMap.set(t,n)}getBypassRules(t){return this.bypassRulesMap.get(t)}}const d=new class{instanceMap=new Map;documentCacheMap=new Map;addInsBinding(t,n,e){let r=this.instanceMap.get(t);r||(r=new l,this.instanceMap.set(t,r)),r.addBinding(n,e)}rmInsBinding(t,n){let e=this.instanceMap.get(t);e?(e.rmBinding(n),e.isEmpty()&&this.instanceMap.delete(t)):console.log(`[sw] rm binding failed, bindingMap not found id:${t} port:${n}`)}clearInsBinding(){this.instanceMap.clear()}getInsBinding(t,n){let e=this.instanceMap.get(t);if(e)return e.getBinding(n);console.log(`[sw] bindingMap not found id:${t} port:${n}`)}setBypassRules(t,n,e){let r=this.instanceMap.get(t);r?r.setBypassRules(n,e):console.log(`[sw] set bypass rules failed, bindingMap not found id:${t} port:${n}`)}getBypassRules(t,n){let e=this.instanceMap.get(t);return e?e.getBypassRules(n):null}addDocumentCache(t,n,e){this.documentCacheMap.set(t,[n,e])}getDocumentCache(t){return this.documentCacheMap.get(t)||[]}hasDocumentCache(t){return this.documentCacheMap.has(t)}};self.bindingManager=d,console.log("[SW]","sw init",new Date),h.postMessage({event:s.Bootstrap}),self.addEventListener("message",(t=>{const n=t.data;switch(n.event){case r.StartListen:console.log("[sw] Start listening",n),function(t,n,e,r,i,s){console.log("[sw] Set listening ",t,n,e,r,i,s),d.addInsBinding(n,e,r),s&&s.length>0&&d.setBypassRules(n,e,s);r.onmessageerror=function(t){console.error("on message error",e,t)},r.onmessage=function(t){const n=t.data,e=n.id;if(!e)return void console.warn("message channel data with no id",n);const r=g.get(e);if(!r)return void console.warn("resp handle of req id",e,"not found");let s=n.data;if(n.data.buffer&&(s=f.decode(n.data)),r.chunks.push(s),function(t){return""===t||"0\r\n"===t||"0\r\n\r\n"===t}(s)){const t=r.chunks.join(""),n=a(t),e=new Response(n.body,{status:n.status,statusText:n.statusMessage,headers:{...i?{"Cross-Origin-Embedder-Policy":"credentialless","Cross-Origin-Opener-Policy":"cross-origin"}:{},...n.headers}});r.resolve(e)}}}(n.protocol,n.instanceId,n.port,n.messagePort,n.coep,n.serviceWorkerBypassRules);break;case r.StopListen:console.log("[sw] Stop listening",n),d.rmInsBinding(n.instanceId,n.port);break;default:console.log(`[sw] ${n.event} not supported`)}}));const p=self.origin;self.addEventListener("install",(function(t){d.clearInsBinding(),t.waitUntil(self.skipWaiting()),console.log("[SW]","sw install")})),self.addEventListener("activate",(function(t){t.waitUntil(clients.claim()),console.log("[SW]","sw activate")})),self.addEventListener("fetch",(t=>{console.log("[SW]","sw fetch",t.request.url);const n=t.request.url;if(!n.startsWith("http://127.0.0.1")&&!n.startsWith("http://localhost")&&!n.startsWith(p))return;if("bypass"===t.request.headers.get("x-webc-request-handler"))return;let[e,r]=o(n);if(!r&&t.request.referrer){if([e,r]=o(t.request.referrer),r){const i=d.getBypassRules(e,r);if(i&&i.length>0){if(i.some((t=>"string"==typeof t?n===t:t instanceof RegExp&&t.test(n))))return void console.log("[SW]","Bypassing request due to matching rule:",n)}const s=new URL(n),o=s.pathname;return(o.endsWith(".html")||o.endsWith(".htm"))&&d.addDocumentCache(s.origin+s.pathname,[e,r]),t.respondWith(Response.redirect(c(n,e,r),302))}d.hasDocumentCache(t.request.referrer)&&([e,r]=d.getDocumentCache(t.request.referrer))}if(!r||!e)return;let i=d.getInsBinding(e,r);if(!i)return console.error("port ",r," message port disconnected"),t.respondWith(new Response("404 not found\n"+t.request.url));t.respondWith(async function(t,n,e,r){console.log("[sw] on request",t,n,e,r);const i=await async function(t,n,e){const r=new URL(t.url).host,i=function(t){const n={},e=t.headers;for(let t of e.entries())t&&(n[t[0]]=t[1]);return n}(t);return i.host=r,{id:w++,method:t.method,url:u(t.url,n,e),headers:i,bodyUsed:t.bodyUsed,referrer:t.referrer,keepalive:t.keepalive}}(r,t,n),s=i.id;if(!s)return;const o=g.build(s);r.body?(i.body=r.body,e.postMessage(i,[r.body])):e.postMessage(i);return o.promise}(e,r,i,t.request))}));let w=1;class g{static DeferredsMap=new Map;static build(t){const n=g.DeferredsMap.get(t);if(n)return n;const e=new g(t);return g.DeferredsMap.set(t,e),e}static get(t){return g.DeferredsMap.get(t)}constructor(t){this.promise=new Promise(((t,n)=>{this._resolve=t,this._reject=n})),this.chunks=[],this.id=t}resolve=(...t)=>{g.DeferredsMap.delete(this.id),this._resolve(...t)};reject=(...t)=>{g.DeferredsMap.delete(this.id),this._reject(...t)}}})();