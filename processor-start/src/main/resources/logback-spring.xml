<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <!-- https://github.com/spring-projects/spring-boot/blob/v2.5.12/spring-boot-project/spring-boot/src/main/resources/org/springframework/boot/logging/logback/defaults.xml -->
    <include resource="org/springframework/boot/logging/logback/defaults.xml" />
    <property name="APP_NAME" value="processor" />
    <property name="LOG_PATH" value="${user.home}/${APP_NAME}/logs" />
    <property name="LOG_FILE" value="${LOG_PATH}/application.log" />
    <property name="LOG_CHARSET" value="UTF-8"/>

    <appender name="APPLICATION"
        class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_FILE}</file>
        <encoder>
            <pattern>%-4relative,[%d{yyyy-MM-dd HH:mm:ss}],thread=%thread,traceId=%X{traceId},rpcId=%X{rpcId},%-5level,%logger{35},%msg%n
            </pattern>
            <charset class="java.nio.charset.Charset">${LOG_CHARSET}</charset>
        </encoder>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${LOG_FILE}.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <maxHistory>7</maxHistory>
            <maxFileSize>50MB</maxFileSize>
            <totalSizeCap>20GB</totalSizeCap>
        </rollingPolicy>
    </appender>

    <appender name="PROJECT_INFO" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_PATH}/all.log</file>
        <append>true</append>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <!-- daily rollover -->
            <fileNamePattern>${LOG_PATH}/all.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <!-- keep 1 days' worth of history -->
            <maxHistory>1</maxHistory>
            <maxFileSize>200MB</maxFileSize>
            <totalSizeCap>10GB</totalSizeCap>
        </rollingPolicy>
        <encoder>
            <pattern>%-4relative,[%d{yyyy-MM-dd HH:mm:ss}],[thread=%thread,traceId=%X{traceId},rpcId=%X{rpcId}],%-5level,%logger{35},%msg%n
            </pattern>
            <charset class="java.nio.charset.Charset">${LOG_CHARSET}</charset>
        </encoder>
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>INFO</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
    </appender>

    <appender name="PROJECT_ERROR" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_PATH}/error.log</file>
        <append>true</append>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${LOG_PATH}/error.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <maxHistory>30</maxHistory>
            <maxFileSize>200MB</maxFileSize>
            <totalSizeCap>5GB</totalSizeCap>
        </rollingPolicy>
        <encoder>
            <pattern>%-4relative,[%d{yyyy-MM-dd HH:mm:ss}],thread=%thread,traceId=%X{traceId},rpcId=%X{rpcId},%-5level,%logger{35},%msg%n
            </pattern>
            <charset class="java.nio.charset.Charset">${LOG_CHARSET}</charset>
        </encoder>
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>ERROR</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
    </appender>

    <!-- 业务日志打印 -->
    <appender name="BUSINESS_LOGGER_APPENDER" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${LOG_PATH}/business.log</file>
        <append>true</append>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <!-- daily rollover -->
            <fileNamePattern>${LOG_PATH}/business.%d{yyyy-MM-dd}.log</fileNamePattern>
            <!-- keep 30 days' worth of history -->
            <maxHistory>30</maxHistory>
        </rollingPolicy>
        <encoder>
            <pattern>%-4relative,[%d{yyyy-MM-dd HH:mm:ss}],thread=%thread,traceId=%X{traceId},%msg%n</pattern>
            <charset class="java.nio.charset.Charset">${LOG_CHARSET}</charset>
        </encoder>
    </appender>

    <appender name="ASYNC_PROJECT_INFO" class="ch.qos.logback.classic.AsyncAppender">
        <param name="BufferSize" value="500"/>
        <appender-ref ref="PROJECT_INFO"/>
    </appender>

    <appender name="ASYNC_PROJECT_ERROR" class="ch.qos.logback.classic.AsyncAppender">
        <param name="BufferSize" value="500"/>
        <appender-ref ref="PROJECT_ERROR"/>
    </appender>

    <appender name="ASYNC_BUSINESS_LOGGER_APPENDER" class="ch.qos.logback.classic.AsyncAppender">
        <param name="BufferSize" value="500"/>
        <appender-ref ref="BUSINESS_LOGGER_APPENDER"/>
    </appender>

    <logger name="BUSINESS_LOGGER" additivity="false">
        <level value="INFO"/>
        <appender-ref ref="ASYNC_BUSINESS_LOGGER_APPENDER"/>
    </logger>

    <root level="INFO">
        <appender-ref ref="APPLICATION" />
        <appender-ref ref="ASYNC_PROJECT_INFO"/>
        <appender-ref ref="ASYNC_PROJECT_ERROR"/>
    </root>
</configuration>
