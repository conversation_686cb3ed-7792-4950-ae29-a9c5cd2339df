<style type="text/css">
    * {
        box-sizing: border-box
    }

    html, body {
        padding: 0;
        margin: 0;
        height: 100%;
        touch-action: manipulation
    }

    html {
        font-size: 12px !important
    }

    .main {
        display: flex;
        flex-direction: column;
        height: 100%
    }

    #designerLoader {
        position: fixed;
        display: flex;
        width: 100vw;
        height: 100vh;
        align-items: center;
        justify-content: center
    }

    #aliyunPreview {
        flex: 1
    }

    #office-iframe {
        width: 100vw;
        height: 100vh
    }
</style>

<div id="designerLoader">
    <img width="160px" src="//img.alicdn.com/tfs/TB1CmVgayERMeJjy0FcXXc7opXa-308-200.gif" style="backface-visibility: hidden;"/>
</div>
<script src="//g.alicdn.com/platform/c/jquery/1.11.3/dist/jquery.min.js"></script>
<script src="//g.alicdn.com/IMM/office-js/1.1.19/aliyun-web-office-sdk.min.js"></script>
<script>
    /**
     * 调用 imm office SDK 执行编辑
     */
    var content = {};
    var instance = null;

    function officeCallback(office) {
        if (!office.success) {
            alert('服务器异常，请重试！');
        }
        content = office.content || {};
        // 配置编辑实例
        instance = aliyun.config({
            url: office.content.$!{accessUrl},
            wordOptions:{
                isShowDocMap: true,
                isAutoShowDocMap: false
            }
        });
        // 设置 token
        instance.setToken({
            token: office.content.accessToken
        });
        // 隐藏 loader
        let designerLoader = document.getElementById('designerLoader');
        if (designerLoader) {
            let status = designerLoader.style.display;
            if (status !== 'none') {
                designerLoader.style.display = 'none';
            }
        }
    }

    function refreshCallback(office) {
        if (!office.success) {
            return;
        }
        content = office.content || {};
        // 设置 token
        instance.setToken({
            token: office.content.accessToken
        });
    }

    function refreshToken() {
        debugger
        var searchParams = new URLSearchParams({
            refreshType: '$!{refreshType}',
            accessToken: content.accessToken || '',
            refreshToken: content.refreshToken || '',
        });
        console.log('refreshToken url:', '$!{refreshUrl}?' + searchParams.toString());
        return fetch('$!{refreshUrl}?' + searchParams.toString()).then(x => {
            return x.json();
        }).then(refreshCallback)
    }

    var time = setInterval(refreshToken, 10 * 60 * 1000);

    console.log('requestUrl:', '$!{requestUrl}');
    fetch('$!{requestUrl}',{
        method: 'post',
        headers: {
            'content-type': 'application/json'
        },
        body: JSON.stringify({"objectName":"$!{objectName}"})
    }).then(x => x.json()).then(officeCallback)

</script>