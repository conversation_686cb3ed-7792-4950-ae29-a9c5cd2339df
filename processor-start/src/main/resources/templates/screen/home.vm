<!DOCTYPE html>
<html lang="zh-cmn-<PERSON>">
<head>
    <script>
        if (window.performance && typeof performance.mark === 'function') {
            window.performance.mark('vm');
        }
    </script>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width,initial-scale=1.0,minimum-scale=1.0,maximum-scale=1.0,user-scalable=no,viewport-fit=cover">
    <meta name="format-detection" content="telephone=no">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="renderer" content="webkit">
    <title>PolyMind</title>
    <meta name="data-spm" content="a2q5p">
    <meta name="title" content="CodeGen" />
    <meta name="description" content="PolyMind" />
    <meta name="keywords" content="PolyMind" />
    <link rel="shortcut icon" href="https://tianshu-vpc.oss-cn-shanghai.aliyuncs.com/f58ab647-cd44-46a0-a7a6-a2dc6481c6e9.png">
    <!-- Open Graph data -->

    <meta property="og:type" content="website" />
    <meta property="og:site_name" content="PolyMind" />
    <meta property="og:title" content="PolyMind" />
    <meta property="og:description" content="PolyMind" />
    <meta property="og:image" content="https://i01.lw.aliimg.com/media/lALPDe7syPSpsiPM8Mzw_240_240.png" />

    <!-- 通用包样式设置 -->
    <link rel="stylesheet" href="${smart_processor_cdn_domain}/code/npm/@ali/yc-common/${smart_processor_ali_yc_version}/pc.css?dd_cache=true"/>
    <link rel="stylesheet" href="${smart_processor_cdn_domain}/code/npm/@ali/yc-deep/${smart_processor_ali_yc_version}/pc.css?dd_cache=true"/>
    <link rel="stylesheet" href="${smart_processor_cdn_domain}/yida-platform/yida-manus/${smart_processor_polymind_version}/polymind.css"/>

    <style id="yida-global-theme">
        :root {
            --color-brand1-6: rgb(0, 137, 255);
            --color-brand-4: rgb(0, 109, 204);
            --color-brand1-2: rgb(242, 249, 255);
            --color-brand-3: rgb(0, 137, 255);
            --color-brand1-1: rgb(51, 160, 255);
            --color-brand-2: rgb(51, 160, 255);
            --color-brand-1: rgb(178, 219, 255);
            --color-brand1-3: rgba(0, 137, 255, 0.2);
            --color-brand1-10: rgba(0, 137, 255, 0.3);
            --color-brand1-9: rgb(0, 109, 204);
        }
    </style>
</head>

<body>
<div class="yida-manus-container">
    <div style="height: calc(100vh - 154px);">
        <img class="yida-common-loading-img" src="//img.alicdn.com/tfs/TB1S3fieubviK0jSZFNXXaApXXa-144-144.gif" alt="loading" style="width: 48px; position: fixed; top: calc(50vh - 80px); left: 50%; margin-top: -24px; margin-left: -24px;" />
    </div>
</div>

<!-- 系统配置 -->
<script>
    window.pageConfig = {
        corpId: "$!corpId"
    };
</script>
<!-- 登录信息 -->
<!--用户信息，统一放在这里，前端使用取这里的变量-->
<script>
    window.loginUser = {
        userId : "$!userId",
        userName : "$!userName",
        avatar : "$!avatar"
    }
</script>

<script crossOrigin="anonymous" src="//g.alicdn.com/code/lib/react/18.3.1/umd/react.production.min.js"></script>
<script crossOrigin="anonymous" src="//g.alicdn.com/code/lib/react-dom/18.3.1/umd/react-dom.production.min.js"></script>
<script crossOrigin="anonymous" src="//g.alicdn.com/code/lib/prop-types/15.7.2/prop-types.js"></script>
<script crossOrigin="anonymous" src="${smart_processor_cdn_domain}/yida-platform/react-polyfill/${smart_processor_react_polyfill_version}/index.js"></script>
<script crossOrigin="anonymous" src="//g.alicdn.com/code/lib/react-router-dom/5.1.2/react-router-dom.min.js"></script>
<script crossorigin="anonymous" src="//g.alicdn.com/code/lib/moment.js/2.29.1/moment.min.js?dd_cache=true"></script>
<script crossorigin="anonymous" src="//g.alicdn.com/code/lib/moment.js/2.29.1/locale/zh-cn.min.js"></script>

<script crossorigin="anonymous" src="//g.alicdn.com/code/lib/moment.js/2.29.1/locale/zh-cn.min.js?dd_cache=true"></script>
<script crossorigin="anonymous" src="//g.alicdn.com/platform/c/lodash/4.6.1/lodash.min.js?dd_cache=true"></script>

<script crossorigin="anonymous" src="//g.alicdn.com/platform/c/natty-storage/2.0.2/dist/natty-storage.min.js?dd_cache=true"></script>
<script crossorigin="anonymous" src="//g.alicdn.com/platform/c/natty-fetch/2.6.0/dist/natty-fetch.pc.min.js?dd_cache=true"></script>

<!-- 通用包js设置 -->
<script src="${smart_processor_cdn_domain}/code/npm/@ali/yc-common/${smart_processor_ali_yc_version}/pc.js?dd_cache=true"></script>
<script src="${smart_processor_cdn_domain}/code/npm/@ali/yc-deep/${smart_processor_ali_yc_version}/pc.js?dd_cache=true"></script>
<script crossorigin="anonymous" src="${smart_processor_cdn_domain}/yida-platform/yida-manus/${smart_processor_manus_version}/polymind.js"></script>
</body>
</html>