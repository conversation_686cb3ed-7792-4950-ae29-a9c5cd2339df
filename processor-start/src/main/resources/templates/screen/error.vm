<style type="text/css">
    .exception-container {
        padding: 150px;
        min-height: 450px;
    }

    .exception-img {
        text-align: center;
    }

    .exception-img > img {
        width: 12em;
    }

    .exception-detail {
        text-align: center;
        padding: 0.8em;
        font-size: 1.15em;
        color: #666666;
    }

    @media only screen and (max-width: 26.786em) {
        .exception-container {
            margin-left: -12.5em;
            width: 25em;
        }

        .exception-img > img {
            width: 11em;
        }

        .exception-detail {
            font-size: 1.05em;
        }
    }

    @media only screen and (max-width: 22.858em) {
        .exception-container {
            margin-left: -10.5em;
            width: 21em;
        }

        .exception-img > img {
            width: 10em;
        }

        .exception-detail {
            font-size: 0.95em;
        }
    }
</style>

#set ($errorImg = "//img.alicdn.com/tfs/TB14fJMEvb2gK0jSZK9XXaEgFXa-450-450.png")

<body>
<div class="exception-container">
    <div class="exception-img">
        <img src="$!{errorImg}"/>
    </div>
    #if(${errorMsg})
        <div class="exception-detail">${errorMsg}</div>
    #else
        <div class="exception-detail">系统开小差了，请稍后重试!</div>
        <div class="exception-detail">something wrong with the system, please retry later!</div>
    #end
</div>
</body>