package com.alibaba.smart.processor.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.smart.processor.context.UserContextUtil;
import com.alibaba.smart.processor.diamond.DiamondEnums;
import com.alibaba.smart.processor.permission.BizPermissionService;
import com.dingtalk.dbase.trial.TrialManager;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.ui.Model;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;
import java.util.List;

public abstract class AbstractController {
    protected Logger logger = LoggerFactory.getLogger(this.getClass());

    @Resource
    private TrialManager dbaseTrialManager;

    @Resource
    protected BizPermissionService bizPermissionService;

    @Resource
    protected HttpServletResponse response;

    public String execute(Model context){

        context.addAttribute("smart_processor_cdn_domain", DiamondEnums.smart_processor_cdn_domain.getValue());
        context.addAttribute("smart_processor_manus_version", DiamondEnums.smart_processor_manus_version.getValue());
        context.addAttribute("smart_processor_ali_yc_version", DiamondEnums.smart_processor_ali_yc_version.getValue());
        context.addAttribute("smart_processor_react_polyfill_version", DiamondEnums.smart_processor_react_polyfill_version.getValue());

        String diamondKey = "polymind-fe";
        String polymindVersionConfig = DiamondEnums.polymind_version_config.getValue();
        if(StringUtils.isNotBlank(polymindVersionConfig)){
            JSONObject polymindVersionConfigJson = JSON.parseObject(polymindVersionConfig);
            String originVersion = polymindVersionConfigJson.getString(diamondKey);
            String version = dbaseTrialManager.getTrialVersion(diamondKey, UserContextUtil.uid(), UserContextUtil.corpId(), null, originVersion);
            context.addAttribute("polymind_fe_version", StringUtils.defaultIfBlank(version, originVersion));
        }

        return null;
    }

    protected boolean checkAccess(){
        List<String> corpIdList = Arrays.asList(DiamondEnums.polymind_visit_corp_white_list.getValue().split(","));
        return corpIdList.contains(UserContextUtil.corpId());
    }
}
