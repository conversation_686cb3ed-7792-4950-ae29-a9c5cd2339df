package com.alibaba.smart.processor.rpc;

import com.alibaba.smart.processor.model.SessionPluginBindingDO;
import com.alibaba.smart.processor.model.dto.PageDto;
import com.alibaba.smart.processor.model.dto.PluginsDTO;
import com.alibaba.smart.processor.model.dto.SessionPluginsBindingDTO;
import com.alibaba.smart.processor.model.vo.PluginsVO;
import com.alibaba.smart.processor.model.vo.SessionPluginsBindingVO;
import com.alibaba.smart.processor.mvc.annotation.JsonParam;
import com.alibaba.smart.processor.plugins.PluginsFacade;
import com.alibaba.smart.processor.plugins.execute.model.ApiCallRequest;
import com.alibaba.smart.processor.plugins.execute.model.ApiCallResponse;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@RequestMapping("/api/plugin")
@Controller
@ResponseBody
public class PluginsRpc {

    @Resource
    private PluginsFacade pluginsFacade;

    @RequestMapping(value = "/create", method = RequestMethod.POST)
    public void createPlugin(@RequestBody PluginsDTO pluginsDTO) {
        pluginsFacade.createPlugins(pluginsDTO);
    }

    @RequestMapping(value = "/list", method = RequestMethod.GET)
    public PageDto<PluginsVO> listPlugins(
            @RequestParam(value = "key", required = false) String key,
            @RequestParam(value = "page", defaultValue = "1") Integer page,
            @RequestParam(value = "pageSize", defaultValue = "12") Integer pageSize) {
        return pluginsFacade.listPlugins(key, page, pageSize);
    }

    @RequestMapping(value = "/listSystemPlugin", method = RequestMethod.GET)
    public List<PluginsVO> listSystemPlugins() {
        return pluginsFacade.listSystemPlugins();
    }

    @RequestMapping(value = "/del", method = RequestMethod.GET)
    public void listPlugin(@RequestParam("pluginId") String pluginId) {
        pluginsFacade.deletePlugin(pluginId);
    }

    @RequestMapping(value = "/update", method = RequestMethod.POST)
    public void updatePlugin(@RequestBody PluginsDTO pluginsDTO) {
        pluginsFacade.updatePlugin(pluginsDTO);
    }

    @RequestMapping(value = "/bind/create", method = RequestMethod.POST)
    public void createPluginBinding(@RequestBody SessionPluginsBindingDTO sessionPluginsBindingDTO) {
        pluginsFacade.bindingCreate(sessionPluginsBindingDTO);
    }

    @RequestMapping(value = "/bind/delete", method = RequestMethod.POST)
    public void deletePluginBinding(@RequestBody SessionPluginsBindingDTO sessionPluginsBindingDTO) {
        pluginsFacade.deletePluginBind(sessionPluginsBindingDTO.getSessionId(), sessionPluginsBindingDTO.getPluginIds());
    }

    @RequestMapping(value = "/bind/list", method = RequestMethod.GET)
    public List<PluginsVO> listPluginBindings(@RequestParam("sessionId") String sessionId) {
        SessionPluginsBindingVO bindResult = pluginsFacade.bindingList(sessionId);
        return bindResult == null ? null : bindResult.getPluginsVOS();
    }

    @RequestMapping(value = "/execute", method = RequestMethod.POST)
    public void executePlugin(@JsonParam(name = "pluginId") String pluginId,
                              @JsonParam(name = "params") String params) {
        pluginsFacade.executePlugin(pluginId, params);
    }

    @RequestMapping(value = "/call", method = RequestMethod.POST)
    public ApiCallResponse callPluginApi(@RequestBody ApiCallRequest apiCallRequest) {
        return pluginsFacade.callPlugin(apiCallRequest);
    }
}
