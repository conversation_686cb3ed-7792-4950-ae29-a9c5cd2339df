package com.alibaba.smart.processor.mvc.response;

import com.alibaba.fastjson.JSON;
import com.alibaba.smart.processor.mvc.annotation.JsonpResponseBody;
import com.alibaba.smart.processor.mvc.annotation.SkipResponseWrapping;
import com.alibaba.smart.processor.mvc.result.PojoResult;
import com.alibaba.smart.processor.mvc.utils.HttpRequestUtils;
import com.google.common.collect.Maps;
import org.apache.catalina.connector.ClientAbortException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.core.MethodParameter;
import org.springframework.web.context.request.NativeWebRequest;
import org.springframework.web.method.support.HandlerMethodReturnValueHandler;
import org.springframework.web.method.support.ModelAndViewContainer;
import org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter;
import org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 */
public class ResponseBodyWrapHandler extends RequestResponseBodyMethodProcessor implements HandlerMethodReturnValueHandler {
    private Logger logger = LoggerFactory.getLogger(ResponseBodyWrapHandler.class);

    private final HandlerMethodReturnValueHandler delegate;

    public ResponseBodyWrapHandler(RequestMappingHandlerAdapter adapter, HandlerMethodReturnValueHandler delegate) {
        super(adapter.getMessageConverters());
        this.delegate = delegate;
    }

    @Override
    public boolean supportsReturnType(MethodParameter returnType) {
        return delegate.supportsReturnType(returnType);
    }

    @Override
    public void handleReturnValue(Object returnValue, MethodParameter returnType, ModelAndViewContainer mavContainer,
                                  NativeWebRequest webRequest) {
        HttpServletRequest request = webRequest.getNativeRequest(HttpServletRequest.class);

        // 检查是否有 @SkipResponseWrapping 注解
        boolean skipWrapping = returnType.hasMethodAnnotation(SkipResponseWrapping.class);
        Object result = returnValue;
        if(!skipWrapping){
            if (HttpRequestUtils.isJsonRequest(request)) {
                result = convertToPojoResult(returnValue);
            } else if (HttpRequestUtils.isJsonpRequest(request)) {
                if (returnType.hasMethodAnnotation(JsonpResponseBody.class)) {
                    result = webRequest.getParameter("callback") + "(" + JSON.toJSONString(convertToPojoResult(returnValue)) + ")";
                } else {
                    throw new RuntimeException("当前接口不支持JSONP请求");
                }
            }
        }

        try {
            delegate.handleReturnValue(result, returnType, mavContainer, webRequest);
        } catch (ClientAbortException clientAbortException) {
            logger.info("clientAbortException .. do nothing." + clientAbortException.getMessage());
        } catch (Throwable e) {
            logger.error("handle request return value error : " + JSON.toJSONString(e));
            throw new RuntimeException("请求结果处理失败");
        }
    }

    private Object convertToPojoResult(Object object) {
        if (object instanceof PojoResult) {
            return object;
        }

        PojoResult<Object> pojoResult = new PojoResult<>();
        if (object == null) {
            object = Maps.newHashMap();
        }
        pojoResult.setContent(object);
        pojoResult.setSuccess(true);
        return pojoResult;
    }
}
