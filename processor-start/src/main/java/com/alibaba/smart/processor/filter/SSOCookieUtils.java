package com.alibaba.smart.processor.filter;

import com.alibaba.smart.processor.exception.BusinessException;
import com.alibaba.smart.processor.exception.ExceptionContext;
import org.apache.commons.lang3.StringUtils;

import javax.crypto.BadPaddingException;
import javax.crypto.IllegalBlockSizeException;
import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.security.InvalidKeyException;
import java.text.FieldPosition;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;
import java.util.TimeZone;

public class SSOCookieUtils {
    protected static final String PATH        = "/";
    protected static final String ACCOUNT_DOMAIN        = ".dingding.xin";
    private static final String TSPECIALS = "()<>@,;:\\\"/[]?={} \t";
    private static boolean CHECK_FLAGS[] = new boolean[127];
    static {
        for (int i = 0; i < TSPECIALS.length(); i++) {
            CHECK_FLAGS[TSPECIALS.charAt(i)] = true;
        }
    }
    /**
     * US locale - all HTTP dates are in english
     */
    public final static Locale LOCALE_US          = Locale.US;

    /**
     * Pattern used for old cookies
     */
    public final static String OLD_COOKIE_PATTERN = "EEE, dd-MMM-yyyy HH:mm:ss z";

    public static String getCookieValue(Cookie cookie) throws Exception {
        if (cookie == null) {
            return null;
        }
        return CipherUtil.decodeText(cookie.getValue(), null);
    }


    public static String getCookieValue(String key, HttpServletRequest request) throws Exception {
        Cookie cookie = getCookie(key, request);
        if (cookie == null) {
            return null;
        }
        return CipherUtil.decodeText(cookie.getValue(), null);
    }

    public static Cookie getCookie(String key, HttpServletRequest request) {
        if (request == null || StringUtils.isBlank(key)) {
            return null;
        }
        Cookie[] cookies = request.getCookies();
        if (cookies == null) {
            return null;
        }
        Cookie value = null;
        for (Cookie c : cookies) {
            if (key.equals(c.getName())) {
                value = c;
                break;
            }
        }
        return value;
    }

    public static void addCookie(String key, String value, HttpServletResponse response) throws Exception{
        setCookie(key, value, -1, null, null, response);
    }

    public static void addCookie(String key, String value, final boolean httpOnly, HttpServletResponse response) throws Exception{
        setCookie(key, value, -1, null, null, httpOnly, response);
    }

    public static void addCookie(String key, String value, final boolean httpOnly, final boolean secure, HttpServletResponse response) throws Exception{
        setCookie(key, value, -1, null, null, httpOnly, secure, response);
    }

    public static void addCookie(String key, String value, final boolean httpOnly, final boolean secure,
        HttpServletResponse response, HttpServletRequest request) throws Exception{
        setCookieWithSameSite(key, value, -1, null, null, httpOnly, secure, response, request);
    }

    public static void addCookie(String key, String value, int maxAge, HttpServletResponse response) throws Exception{
        setCookie(key, value, maxAge, null, null, response);
    }

    public static void addCookie(String key, String value, int maxAge, final boolean httpOnly, HttpServletResponse response) throws Exception{
        setCookie(key, value, maxAge, null, null, httpOnly, response);
    }

    public static void addCookie(String key, String value, int maxAge, final boolean httpOnly, final boolean secure, final String priority, HttpServletResponse response) throws Exception{
        setCookie(key, value, maxAge, null, null, httpOnly, secure, priority, response);
    }

    public static void addCookie(String key, String value, int maxAge, String path, String domainName, HttpServletResponse response) throws Exception{
        setCookie(key, value, maxAge, path, domainName, response);
    }

    public static void addCookie(String key, String value, int maxAge, String path, String domainName, final boolean httpOnly, HttpServletResponse response) throws Exception{
        setCookie(key, value, maxAge, path, domainName, httpOnly, response);
    }

    public static void addCookie(String key, String value, int maxAge, String path, String domainName, final boolean httpOnly, final boolean secure, HttpServletResponse response) throws Exception{
        setCookie(key, value, maxAge, path, domainName, httpOnly, secure, response);
    }

    public static void addCookie(String key, String value, int maxAge, String path, String domainName,
        final boolean httpOnly, final boolean secure, HttpServletResponse response, HttpServletRequest request) throws Exception{
        setCookieWithSameSite(key, value, maxAge, path, domainName, httpOnly, secure, response, request);
    }

    public static void removeCookie(String key, HttpServletResponse response) throws Exception{
        removeCookie(key, null, null,false, response);
    }

    public static void removeCookie(String key, String path, String domainName,boolean httpOnly, HttpServletResponse response) throws Exception{
        setCookie(key, StringUtils.EMPTY, 0, path, domainName, httpOnly, response);
    }

    private static void setCookie(String key, String value, int maxAge, String path, String domainName, HttpServletResponse response) throws Exception{
        setCookie(key, value, maxAge, path, domainName, false, false, response);
    }

    private static void setCookie(String key, String value, int maxAge, String path, String domainName, final boolean httpOnly, HttpServletResponse response) throws Exception{
        setCookie(key, value, maxAge, path, domainName, httpOnly, true, response);
    }

    private static void setCookie(String key, String value, int maxAge, String path, String domainName, final boolean httpOnly, final boolean secure, HttpServletResponse response) throws Exception{
        setCookie(key, value, maxAge, path, domainName, httpOnly, secure, null, response);
    }

    private static void setCookieWithSameSite(String key, String value, int maxAge, String path, String domainName,
        final boolean httpOnly, final boolean secure, HttpServletResponse response, HttpServletRequest httpServletRequest) throws Exception{
        setCookieWithSameSite(key, value, maxAge, path, domainName, httpOnly, secure, null, response, httpServletRequest);
    }


    private static void setCookie(String key, String value, int maxAge, String path, String domainName,
                                  final boolean httpOnly, final boolean secure, final String priority,
                                  HttpServletResponse response) throws Exception{
        if (response != null) {
            Cookie cookie = new Cookie(key, encodeClientText(value, null));
            cookie.setMaxAge(maxAge);
            if (StringUtils.isNotBlank(path)) {
                cookie.setPath(path);
            } else {
                cookie.setPath(PATH);
            }
            if (StringUtils.isNotBlank(domainName)) {
                cookie.setDomain(domainName);
            }
            cookie.setVersion(0);
            cookie.setSecure(secure);
            if (httpOnly || StringUtils.isNotBlank(priority)) {
                final StringBuffer buf = new StringBuffer();
                getCookieHeaderValue(cookie, buf, httpOnly, priority);
                response.addHeader(getCookieHeaderName(cookie), buf.toString());
            }
            else {
                response.addCookie(cookie);
            }
        }
    }

    private static void setCookieWithSameSite(String key, String value, int maxAge, String path, String domainName,
        final boolean httpOnly, final boolean secure, final String priority,
        HttpServletResponse response, HttpServletRequest request) throws Exception{
        if (response != null) {
            Cookie cookie = new Cookie(key, encodeClientText(value, null));
            cookie.setMaxAge(maxAge);
            if (StringUtils.isNotBlank(path)) {
                cookie.setPath(path);
            } else {
                cookie.setPath(PATH);
            }
            if (StringUtils.isNotBlank(domainName)) {
                cookie.setDomain(domainName);
            }
            cookie.setVersion(0);
            cookie.setSecure(secure);
            if (httpOnly || StringUtils.isNotBlank(priority)) {
                final StringBuffer buf = new StringBuffer();
                final boolean addSameSite = addSameSite(request);
                loadCookieHeaderToBufNew(cookie, buf, httpOnly, addSameSite, true, priority);
                response.addHeader(getCookieHeaderName(cookie), buf.toString());
                // 兼容逻辑，增加一个非Partitioned的空cookie,解决Partitioned被原浏览器非Partitioned cookie覆盖问题
                if(addSameSite){
                    final StringBuffer nullCookiebuf = new StringBuffer();
                    cookie.setMaxAge(0);
                    cookie.setValue("");
                    loadCookieHeaderToBufNew(cookie, nullCookiebuf, httpOnly, true, false, priority);
                    response.addHeader(getCookieHeaderName(cookie), nullCookiebuf.toString());
                }
            }
            else {
                response.addCookie(cookie);
            }
        }
    }

    public static void addCookieWithNoSe(String key, String value, HttpServletResponse response, HttpServletRequest request) throws Exception {
        if (response != null) {
            setCookieWithSameSite(key, value, -1, null, null,  true,  true, null, false, response, request);
        }
    }

    private static void setCookieWithSameSite(String key, String value, int maxAge, String path, String domainName,
                                              final boolean httpOnly, final boolean secure, final String priority, Boolean encrypt,
                                              HttpServletResponse response, HttpServletRequest request) throws Exception {
        if (response != null) {
            Cookie cookie = new Cookie(key, encodeClientText(value, encrypt));
            cookie.setMaxAge(maxAge);
            if (StringUtils.isNotBlank(path)) {
                cookie.setPath(path);
            } else {
                cookie.setPath(PATH);
            }
            if (StringUtils.isNotBlank(domainName)) {
                cookie.setDomain(domainName);
            }
            cookie.setVersion(0);
            cookie.setSecure(secure);
            if (httpOnly || StringUtils.isNotBlank(priority)) {
                final StringBuffer buf = new StringBuffer();
                final boolean addSameSite = addSameSite(request);
                loadCookieHeaderToBufNew(cookie, buf, httpOnly, addSameSite, true, priority);
                response.addHeader(getCookieHeaderName(cookie), buf.toString());
                if(addSameSite){
                    final StringBuffer nullCookiebuf = new StringBuffer();
                    cookie.setMaxAge(0);
                    cookie.setValue("");
                    loadCookieHeaderToBufNew(cookie, nullCookiebuf, httpOnly, true, false, priority);
                    response.addHeader(getCookieHeaderName(cookie), nullCookiebuf.toString());
                }
            }
            else {
                response.addCookie(cookie);
            }
        }
    }

    /**
     * 明文输出cookie
     * @param key
     * @param value
     * @param maxAge
     * @param path
     * @param domainName
     * @param httpOnly
     * @param secure
     * @param response
     */
    private static void setCookieWithPlainText(String key, String value, int maxAge, String path,
                                               String domainName, final boolean httpOnly, final boolean secure,
                                               HttpServletResponse response) {
        if (response != null) {

            Cookie cookie = null;

            try {
                cookie = new Cookie(key, URLEncoder.encode(value, "UTF-8"));
            } catch (UnsupportedEncodingException e) {
                throw new BusinessException(new ExceptionContext("编码的时候出错").putContext("value",
                        value), "000000", "cookie数据编码失败");
            }

            cookie.setMaxAge(maxAge);
            if (org.apache.commons.lang.StringUtils.isNotBlank(path)) {
                cookie.setPath(path);
            } else {
                cookie.setPath(PATH);
            }
            if (org.apache.commons.lang.StringUtils.isNotBlank(domainName)) {
                cookie.setDomain(domainName);
            }
            cookie.setVersion(0);
            cookie.setSecure(secure);
            if (httpOnly) {
                final StringBuffer buf = new StringBuffer();
                getCookieHeaderValue(cookie, buf, httpOnly, null);
                response.addHeader(getCookieHeaderName(cookie), buf.toString());
            } else {
                response.addCookie(cookie);
            }
        }
    }

    private static String getCookieHeaderName(final Cookie cookie) {
        final int version = cookie.getVersion();
        if (version == 1) {
            return "Set-Cookie2";
        } else {
            return "Set-Cookie";
        }
    }

    private static void getCookieHeaderValue(final Cookie cookie, final StringBuffer buf,
                                             final boolean httpOnly, final String priority) {
        final int version = cookie.getVersion();

        // this part is the same for all cookies

        String name = cookie.getName(); // Avoid NPE on malformed cookies
        if (name == null) {
            name = "";
        }
        String value = cookie.getValue();
        if (value == null) {
            value = "";
        }

        buf.append(name);
        buf.append("=");

        maybeQuote(version, buf, value);

        // add version 1 specific information
        if (version == 1) {
            // Version=1 ... required
            buf.append("; Version=1");

            // Comment=comment
            if (cookie.getComment() != null) {
                buf.append("; Comment=");
                maybeQuote(version, buf, cookie.getComment());
            }
        }

        // add domain information, if present

        if (cookie.getDomain() != null) {
            buf.append("; Domain=");
            maybeQuote(version, buf, cookie.getDomain());
        }

        // Max-Age=secs/Discard ... or use old "Expires" format
        if (cookie.getMaxAge() >= 0) {
            if (version == 0) {
                buf.append("; Expires=");
                SimpleDateFormat dateFormat = new SimpleDateFormat(OLD_COOKIE_PATTERN, LOCALE_US);
                dateFormat.setTimeZone(TimeZone.getTimeZone("GMT")); //必须使用GMT模式
                if (cookie.getMaxAge() == 0) {
                    dateFormat.format(new Date(10000),
                            buf, new FieldPosition(0));
                } else {
                    dateFormat.format(new Date(System
                            .currentTimeMillis()
                            + cookie.getMaxAge() * 1000L), buf, new FieldPosition(0));
                }
            } else {
                buf.append("; Max-Age=");
                buf.append(cookie.getMaxAge());
            }
        } else if (version == 1) {
            buf.append("; Discard");
        }

        // Path=path
        if (cookie.getPath() != null) {
            buf.append("; Path=");
            maybeQuote(version, buf, cookie.getPath());
        }

        // Secure
        if (cookie.getSecure()) {
            buf.append("; Secure").append("; SameSite=None").append("; Partitioned");
        }

        // HttpOnly
        if (httpOnly) {
            buf.append("; HttpOnly");
        }

        // Priority
        if (StringUtils.isNotBlank(priority)) {
            buf.append("; Priority=" + priority);
        }
    }


    private static void loadCookieHeaderToBuf(final Cookie cookie, final StringBuffer buf,
        final boolean httpOnly, final String priority, HttpServletRequest request) {
        final int version = cookie.getVersion();

        // this part is the same for all cookies

        String name = cookie.getName(); // Avoid NPE on malformed cookies
        if (name == null) {
            name = "";
        }
        String value = cookie.getValue();
        if (value == null) {
            value = "";
        }

        buf.append(name);
        buf.append("=");

        maybeQuote(version, buf, value);

        // add version 1 specific information
        if (version == 1) {
            // Version=1 ... required
            buf.append("; Version=1");

            // Comment=comment
            if (cookie.getComment() != null) {
                buf.append("; Comment=");
                maybeQuote(version, buf, cookie.getComment());
            }
        }

        // add domain information, if present

        if (cookie.getDomain() != null) {
            buf.append("; Domain=");
            maybeQuote(version, buf, cookie.getDomain());
        }

        // Max-Age=secs/Discard ... or use old "Expires" format
        if (cookie.getMaxAge() >= 0) {
            if (version == 0) {
                buf.append("; Expires=");
                SimpleDateFormat dateFormat = new SimpleDateFormat(OLD_COOKIE_PATTERN, LOCALE_US);
                dateFormat.setTimeZone(TimeZone.getTimeZone("GMT")); //必须使用GMT模式
                if (cookie.getMaxAge() == 0) {
                    dateFormat.format(new Date(10000),
                        buf, new FieldPosition(0));
                } else {
                    dateFormat.format(new Date(System
                        .currentTimeMillis()
                        + cookie.getMaxAge() * 1000L), buf, new FieldPosition(0));
                }
            } else {
                buf.append("; Max-Age=");
                buf.append(cookie.getMaxAge());
            }
        } else if (version == 1) {
            buf.append("; Discard");
        }

        // Path=path
        if (cookie.getPath() != null) {
            buf.append("; Path=");
            maybeQuote(version, buf, cookie.getPath());
        }

        if (addSameSite(request)){
            buf.append("; Secure").append("; SameSite=None");
        }else {
            // Secure
            buf.append("; Secure");
        }

        // HttpOnly
        if (httpOnly) {
            buf.append("; HttpOnly");
        }

        // Priority
        if (StringUtils.isNotBlank(priority)) {
            buf.append("; Priority=").append(priority);
        }
    }

    private static void loadCookieHeaderToBufNew(final Cookie cookie, final StringBuffer buf,
                                              final boolean httpOnly, final boolean sameSite, boolean Partitioned, final String priority) {
        final int version = cookie.getVersion();

        // this part is the same for all cookies

        String name = cookie.getName(); // Avoid NPE on malformed cookies
        if (name == null) {
            name = "";
        }
        String value = cookie.getValue();
        if (value == null) {
            value = "";
        }

        buf.append(name);
        buf.append("=");

        maybeQuote(version, buf, value);

        // add version 1 specific information
        if (version == 1) {
            // Version=1 ... required
            buf.append("; Version=1");

            // Comment=comment
            if (cookie.getComment() != null) {
                buf.append("; Comment=");
                maybeQuote(version, buf, cookie.getComment());
            }
        }

        // add domain information, if present

        if (cookie.getDomain() != null) {
            buf.append("; Domain=");
            maybeQuote(version, buf, cookie.getDomain());
        }

        // Max-Age=secs/Discard ... or use old "Expires" format
        if (cookie.getMaxAge() >= 0) {
            if (version == 0) {
                buf.append("; Expires=");
                SimpleDateFormat dateFormat = new SimpleDateFormat(OLD_COOKIE_PATTERN, LOCALE_US);
                dateFormat.setTimeZone(TimeZone.getTimeZone("GMT")); //必须使用GMT模式
                if (cookie.getMaxAge() == 0) {
                    dateFormat.format(new Date(10000),
                            buf, new FieldPosition(0));
                } else {
                    dateFormat.format(new Date(System
                            .currentTimeMillis()
                            + cookie.getMaxAge() * 1000L), buf, new FieldPosition(0));
                }
            } else {
                buf.append("; Max-Age=");
                buf.append(cookie.getMaxAge());
            }
        } else if (version == 1) {
            buf.append("; Discard");
        }

        // Path=path
        if (cookie.getPath() != null) {
            buf.append("; Path=");
            maybeQuote(version, buf, cookie.getPath());
        }

        if (sameSite){
            buf.append("; Secure").append("; SameSite=None");
            if(Partitioned){
                buf.append("; Partitioned");
            }
        }else {
            buf.append("; Secure");
        }

        // HttpOnly
        if (httpOnly) {
            buf.append("; HttpOnly");
        }

        // Priority
        if (StringUtils.isNotBlank(priority)) {
            buf.append("; Priority=").append(priority);
        }
    }


    private static boolean addSameSite(HttpServletRequest request){
        return true;
    }

    private static void maybeQuote(final int version, final StringBuffer buf, final String value) {
        if (version == 0 || isToken(value)) {
            buf.append(value);
        } else {
            buf.append('"');
            buf.append(value);
            buf.append('"');
        }
    }

    /*
     * Return true iff the string counts as an HTTP/1.1 "token".
     */
    private static boolean isToken(final String value) {
        final int len = value.length();
        char c;
        final char[] charArray = value.toCharArray();
        for (int i = 0; i < len; i++) {
            c = charArray[i];
            if (c < 0x20 || c >= 0x7f) {
                return false;
            } else {
                if (CHECK_FLAGS[c]) {
                    return false;
                }
            }
        }
        return true;
    }

    private static String encodeClientText(String text, Boolean encrypt) throws InvalidKeyException, IllegalBlockSizeException, BadPaddingException, UnsupportedEncodingException {
        // 默认加密
        if (encrypt == null || encrypt) {
            return CipherUtil.encodeText(text, null);
        } else {
            return URLEncoder.encode(text, "UTF-8");
        }

    }
}
