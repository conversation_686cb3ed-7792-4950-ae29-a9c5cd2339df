package com.alibaba.smart.processor.rpc;

import com.alibaba.smart.processor.diamond.DiamondEnums;
import com.alibaba.smart.processor.prompt.PromptFacade;
import com.alibaba.smart.processor.model.dto.PromptDTO;

import javax.annotation.Resource;

import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

@RequestMapping("/query/prompt")
@Controller
@ResponseBody
public class PromptRpc {

    @Resource
    private PromptFacade promptFacade;
     @RequestMapping("listScene.json")
     public String listScene(){
         return DiamondEnums.smart_processor_prompt_template.getValue();
     }

    // @RequestMapping(value = "/optimize")
    @RequestMapping(value = {"/optimize"}, method = RequestMethod.POST)
    public String ask(@RequestBody PromptDTO promptDTO) throws Exception {
        return promptFacade.ask(promptDTO);
    }
    @RequestMapping(value = {"/getSystemPrompt"}, method = RequestMethod.GET)
    public String getSystemPrompt(@RequestParam("key") String key){
        return promptFacade.getSystemPrompt(key);
    }
}
