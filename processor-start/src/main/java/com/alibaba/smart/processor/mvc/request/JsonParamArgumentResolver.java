package com.alibaba.smart.processor.mvc.request;

import com.alibaba.smart.processor.mvc.annotation.JsonParam;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.core.MethodParameter;
import org.springframework.util.StreamUtils;
import org.springframework.web.bind.support.WebDataBinderFactory;
import org.springframework.web.context.request.NativeWebRequest;
import org.springframework.web.method.support.HandlerMethodArgumentResolver;
import org.springframework.web.method.support.ModelAndViewContainer;
import org.springframework.web.util.ContentCachingRequestWrapper;

import javax.servlet.ServletInputStream;
import javax.servlet.http.HttpServletRequest;
import java.nio.charset.StandardCharsets;

/**
 * <AUTHOR>
 * 对于简单类型的入参，可以通过JsonParam注解接收Content-type为application/json的数据，无需通过RequestBody定义Map或者Object对象处理
 */
public class JsonParamArgumentResolver implements HandlerMethodArgumentResolver {
    private final ObjectMapper objectMapper = new ObjectMapper();

    @Override
    public boolean supportsParameter(MethodParameter parameter) {
        return parameter.hasParameterAnnotation(JsonParam.class);
    }

    @Override
    public Object resolveArgument(MethodParameter parameter, ModelAndViewContainer mavContainer, NativeWebRequest webRequest, WebDataBinderFactory binderFactory) throws Exception {
        // 获取请求对象
        HttpServletRequest request = webRequest.getNativeRequest(HttpServletRequest.class);
        if (!(request instanceof ContentCachingRequestWrapper)) {
            throw new IllegalStateException("请求错误，请检查配置");
        }

        // 从缓存中读取请求体
        ContentCachingRequestWrapper wrappedRequest = (ContentCachingRequestWrapper) request;
        byte[] content = wrappedRequest.getContentAsByteArray();
        if (content.length == 0) {
            // 触发缓存填充（如果尚未读取）
            ServletInputStream inputStream = wrappedRequest.getInputStream();
            byte[] buffer = new byte[4096];
            while (inputStream.read(buffer) != -1) {
                /* 读取所有数据到缓存 */
            }
            content = wrappedRequest.getContentAsByteArray();
        }
        // 解析 JSON
        JsonNode rootNode = objectMapper.readTree(content);

        String paramName = parameter.getParameterName();
        JsonParam jsonParam = parameter.getParameterAnnotation(JsonParam.class);
        if(jsonParam != null && StringUtils.isNotBlank(jsonParam.name())){
            paramName = jsonParam.name();
        }

        JsonNode valueNode = rootNode.get(paramName);
        boolean isRequired = jsonParam.required();
        if (valueNode == null && isRequired) {
            throw new IllegalArgumentException("缺少参数: " + paramName);
        }
        return objectMapper.treeToValue(valueNode, parameter.getParameterType());
    }
}
