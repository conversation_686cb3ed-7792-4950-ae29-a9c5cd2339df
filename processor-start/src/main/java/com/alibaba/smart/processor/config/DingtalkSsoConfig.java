package com.alibaba.smart.processor.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

@Configuration
public class DingtalkSsoConfig {

    public static String DINGTALK_SSO_ENVIRONMENT = "DINGTALK_SSO_ENVIRONMENT";

    public static String DINGTALK_SSO_APP_KEY = "DINGTALK_SSO_APP_KEY";

    public static String DINGTALK_SSO_TENANT_ID = "DINGTALK_SSO_TENANT_ID";

    public static String DINGTALK_SSO_REQUIRE_ORG = "DINGTALK_SSO_REQUIRE_ORG";

    public static String DINGTALK_SSO_HTTP_HEADER_CORP_ID = "DINGTALK_SSO_HTTP_HEADER_CORP_ID";

    public static String DINGTALK_SSO_GRAY = "DINGTALK_SSO_GRAY";

    public static String DINGTALK_SSO_CALL_BACK_PATH = "DINGTALK_SSO_CALL_BACK_PATH";

    public static String DINGTALK_SSO_CLIENT_ID = "DINGTALK_SSO_CLIENT_ID";

    public static String DINGTALK_SSO_EXCLUDES = "DINGTALK_SSO_EXCLUDES";

    public static String DINGTALK_SSO_CALL_BACK_PATH_VALUE = "/dingtalk_sso_call_back";


    @Value("${dingtalk.sso.appkey:0c2c750e00bec8bb07bf78c8242f8d50}")
    String dingtalkSsoAppKey;

    @Value("${dingtalk.sso.tenantId:smart-processor}")
    String dingtalkSsoTenantId;

    @Value("${dingtalk.sso.callback.path:/dingtalk_sso_call_back}")
    String dingtalkSsoCallbackPath = DINGTALK_SSO_CALL_BACK_PATH_VALUE;

    @Value("${dingtalk.sso.clientId:suiteoqltfgehr4ztwkfh}")
    String dingtalkSsoClientId;

    @Value("${dingtalk.sso.exclude:/checkpreload.htm}")
    private String dingtalkSsoExcludes;

    @Value("dingtalk.sso.env:pre")
    private String ssoEnv;

    public String getDingtalkSsoAppKey() {
        return dingtalkSsoAppKey;
    }

    public String getDingtalkSsoTenantId() {
        return dingtalkSsoTenantId;
    }

    public String getDingtalkSsoCallbackPath() {
        return dingtalkSsoCallbackPath;
    }

    public String getDingtalkSsoClientId() {
        return dingtalkSsoClientId;
    }

    public String getDingtalkSsoExcludes() {
        return dingtalkSsoExcludes;
    }

    public String getSsoEnv() {
        return ssoEnv;
    }
}

