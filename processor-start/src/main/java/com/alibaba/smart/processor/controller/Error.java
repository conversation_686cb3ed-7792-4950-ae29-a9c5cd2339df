package com.alibaba.smart.processor.controller;

import com.alibaba.boot.velocity.annotation.VelocityLayout;
import com.alibaba.security.SecurityUtil;
import com.alibaba.smart.processor.exception.util.ExceptionPropertiesUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.Locale;

@Controller
public class Error extends UserController{
    @RequestMapping("/errorPage")
    @VelocityLayout("layout/default.vm")
    public String execute(Model model,
                          @RequestParam(value = "errorCode", required = false) String errorCode) {
        if(StringUtils.isNotBlank(errorCode)){
            String errorMsg = ExceptionPropertiesUtil.getString(errorCode, Locale.forLanguageTag("zh-CN"));
            model.addAttribute("errorMsg", SecurityUtil.escapeHtml(errorMsg));
        }
        return "error";
    }
}
