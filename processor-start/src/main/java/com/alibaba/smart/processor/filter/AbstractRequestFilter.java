package com.alibaba.smart.processor.filter;

import com.dingtalk.common.sso.util.PathNameWildcardCompiler;
import org.apache.commons.lang.StringUtils;

import javax.servlet.Filter;
import javax.servlet.FilterConfig;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import java.util.LinkedList;
import java.util.List;
import java.util.regex.Pattern;

public abstract class AbstractRequestFilter implements Filter {
    /**
     * 系统健康检查地址
     */
    public static String[] HEALTH_CHECK_PATHS = new String[] {"/status.taobao", "/checkpreload.htm"};

    /**
     * filter过滤请求路径参数
     */
    private static final String EXCLUSIONS = "excludes";
    /**
     * 根据EXCLUSIONS生成的正则表达式
     */
    private Pattern[] excludesList;

    @Override
    public void init(FilterConfig filterConfig) throws ServletException {
        String excludes = filterConfig.getInitParameter(EXCLUSIONS);
        List<Pattern> excludePatterns = new LinkedList<Pattern>();

        if (StringUtils.isNotBlank(excludes)) {
            for (String exclude : excludes.split(",")) {
                if (exclude != null) {
                    excludePatterns.add(PathNameWildcardCompiler.compilePathName(exclude));
                }
            }
        }

        if (!excludePatterns.isEmpty()) {
            this.excludesList = excludePatterns
                    .toArray(new Pattern[excludePatterns.size()]);
        }
    }

    /**
     * 过滤不需要拦截的请求路径
     */
    protected boolean isExcluded(HttpServletRequest request) {
        String url = request.getRequestURI();
        if (StringUtils.startsWithAny(url, HEALTH_CHECK_PATHS)) {
            return true;
        }
        if (excludesList != null) {
            String requestURI = request.getRequestURI();
            for (Pattern exclude : excludesList) {
                if (exclude.matcher(requestURI).find()) {
                    return true;
                }
            }
        }
        return false;
    }

    @Override
    public void destroy() {

    }

}
