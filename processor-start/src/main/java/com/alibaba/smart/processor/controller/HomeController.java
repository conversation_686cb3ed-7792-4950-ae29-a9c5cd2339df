package com.alibaba.smart.processor.controller;

import com.alibaba.boot.velocity.annotation.VelocityLayout;
import com.alibaba.smart.processor.corp.CorpConfigFacade;
import com.alibaba.smart.processor.diamond.DiamondEnums;
import com.alibaba.smart.processor.model.vo.CorpConfigVO;
import com.taobao.wsgfstjson.JSON;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestMapping;

import javax.annotation.Resource;

@Controller
public class HomeController extends UserController{
    @Resource
    private CorpConfigFacade corpConfigFacade;

    @RequestMapping(value = {"/{router:(?!.*\\.(?:js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)).*}", "/"})
    @VelocityLayout("layout/home.vm")
    @Override
    public String execute(Model model) {
        super.execute(model);

        setModeConfig(model);
        return "index";
    }

    @RequestMapping("/index")
    @VelocityLayout("layout/home.vm")
    public String index(Model model) {
        super.execute(model);

        setModeConfig(model);
        return "index";
    }

    private void setModeConfig(Model model){
        CorpConfigVO corpConfigVO = corpConfigFacade.getCorpConfigNoPermission();
        if(corpConfigVO != null){
            String reasoningModelName = corpConfigVO.getReasoningModelName();
            String reasoningModelConfig = DiamondEnums.polymind_reasoning_model_config.getValue();
            model.addAttribute("reasoningModel", StringUtils.defaultIfBlank(reasoningModelConfig, reasoningModelName));
            model.addAttribute("coderModelType", corpConfigVO.getCoderModelType());
            model.addAttribute("coderModel", corpConfigVO.getCoderModelName());
        }
    }
}
