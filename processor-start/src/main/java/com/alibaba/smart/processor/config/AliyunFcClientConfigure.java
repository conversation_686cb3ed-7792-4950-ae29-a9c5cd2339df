package com.alibaba.smart.processor.config;

import com.alibaba.normandy.credential.Credential;
import com.alibaba.normandy.credential.CredentialProvider;
import com.alibaba.normandy.credential.ResourceNames;
import com.aliyun.fc20230330.Client;
import com.aliyun.oss.ClientBuilderConfiguration;
import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import com.aliyun.oss.common.auth.CredentialsProviderFactory;
import com.aliyun.oss.common.auth.DefaultCredentialProvider;
import com.aliyun.oss.common.auth.EnvironmentVariableCredentialsProvider;
import com.aliyun.oss.common.comm.SignVersion;
import com.aliyun.teaopenapi.models.Config;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class AliyunFcClientConfigure {
    private static final Logger logger = LoggerFactory.getLogger(AliyunFcClientConfigure.class);
    @Value("${fc.endpoint}")
    private String endpoint;
    @Value("${normandy.account.id}")
    private String accountId;
    @Value("${normandy.ram.usename}")
    private String ramUserName;

    @Bean
    public com.aliyun.fc20230330.Client createClient(CredentialProvider credentialProvider) throws Exception {
        String resourceName = ResourceNames.ofInternalAliyunAccessPackage(accountId, ramUserName);
        Credential credential = credentialProvider.getCredential(resourceName);
        if(credential != null){
            Config config = new Config()
                    .setAccessKeyId(credential.getAccessKeyId())
                    .setAccessKeySecret(credential.getAccessKeySecret())
                    .setEndpoint(endpoint);
            return new Client(config);
        }
        return null;
    }

    @Bean(value = "ossClientNew")
    public OSS createOssClient(CredentialProvider credentialProvider) throws Exception {
        String resourceName = ResourceNames.ofInternalAliyunAccessPackage(accountId, ramUserName);
        Credential credential = credentialProvider.getCredential(resourceName);
        if(credential != null){
            DefaultCredentialProvider credentialsProvider = CredentialsProviderFactory.newDefaultCredentialProvider(
                    credential.getAccessKeyId(),
                    credential.getAccessKeySecret()
            );

            ClientBuilderConfiguration clientBuilderConfiguration = new ClientBuilderConfiguration();
            clientBuilderConfiguration.setSignatureVersion(SignVersion.V4);

            return OSSClientBuilder.create()
                    .endpoint("https://oss-cn-shanghai.aliyuncs.com")
                    .credentialsProvider(credentialsProvider)
                    .clientConfiguration(clientBuilderConfiguration)
                    .region("cn-shanghai")
                    .build();
        }
        return null;
    }
}
