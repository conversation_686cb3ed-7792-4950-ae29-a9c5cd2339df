package com.alibaba.smart.processor.filter;

import com.alibaba.smart.processor.context.LoginUser;
import com.alibaba.smart.processor.context.UserContextUtil;
import com.alibaba.smart.processor.context.UserHolderContext;

import javax.servlet.*;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

public class MockUserFilter extends AbstractRequestFilter {

    private String envFlag = null;

    @Override
    public void init(FilterConfig filterConfig) throws ServletException {
        super.init(filterConfig);
        envFlag = filterConfig.getInitParameter("envFlag");
    }

    @Override
    public void doFilter(ServletRequest servletRequest, ServletResponse servletResponse, FilterChain filterChain)
            throws IOException, ServletException {
        HttpServletRequest httpRequest = (HttpServletRequest) servletRequest;
        HttpServletResponse httpResponse = (HttpServletResponse) servletResponse;

        boolean isExcluded = isExcluded(httpRequest);
        if(isExcluded){
           filterChain.doFilter(httpRequest, httpResponse);
           return;
        }

        LoginUser loginUser = UserHolderContext.getLoginUser();
        if(!"true".equals(envFlag) && (loginUser == null || loginUser.getUserId() == null)) {
            if(loginUser == null) {
                loginUser = new LoginUser();
                UserHolderContext.setLoginUser(loginUser);
            }
            loginUser.setUserId("<EMAIL>");
            loginUser.setUid(123L);
            loginUser.setCorpId("ding5d17e3add038d44535c2f4657eb6378f");
        }
        filterChain.doFilter(httpRequest, httpResponse);
    }
}
