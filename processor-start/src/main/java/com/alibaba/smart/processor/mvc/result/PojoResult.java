package com.alibaba.smart.processor.mvc.result;
import java.io.Serializable;
import java.util.Map;

/**
 * <AUTHOR>
 * @param <T>
 */
public class PojoResult<T> implements Serializable {

    private T                 content;

    /**
     * 标识本次调用是否执行成功
     */
    private boolean             success;

    /**
     * 本次调用返回errorCode，一般为错误代码
     */
    private String              errorCode;

    /**
     * 本次调用返回的消息，一般为错误消息
     */
    private String              errorMsg;

    /**
     * 扩展字段
     */
    private Map<String, String> errorExtInfo;

    private String              errorLevel;

    private String throwable;
    /**
     * 设置错误信息
     *
     * @param code
     * @param message
     */
    @SuppressWarnings("unchecked")
    public <R> R setErrorMessage(String code, String message) {
        setErrorCode(code);
        setErrorMsg(message);
        this.success = false;
        return (R)this;
    }

    public static <T> PojoResult<T> newSuccessResult(T content) {
        PojoResult<T> result = new PojoResult<>();
        result.success = true;
        result.content = content;
        return result;
    }


    public boolean isSuccess() {
        return success;
    }

    public void setSuccess(boolean success) {
        this.success = success;
    }

    public Map<String, String> getErrorExtInfo() {
        return errorExtInfo;
    }

    public void setErrorExtInfo(Map<String, String> errorExtInfo) {
        this.errorExtInfo = errorExtInfo;
    }

    /**
     * 返回错误码
     */
    public String getErrorCode() {
        return errorCode;
    }

    /**
     * 设置错误码
     *
     * @param errorCode the errorCode to set
     */
    public void setErrorCode(String errorCode) {
        this.errorCode = errorCode;
    }

    /**
     * @return the errorMsg
     */
    public String getErrorMsg() {
        return errorMsg;
    }

    /**
     * 设置错误消息
     *
     * @param errorMsg the errorMsg to set
     */
    public void setErrorMsg(String errorMsg) {
        this.errorMsg = errorMsg;
    }

    /**
     * @return the errorLevel
     */
    public String getErrorLevel() {
        return errorLevel;
    }

    /**
     * @param errorLevel the errorLevel to set
     */
    public void setErrorLevel(String errorLevel) {
        this.errorLevel = errorLevel;
    }

    /**
     * @return the content
     */
    public T getContent() {
        return content;
    }

    /**
     * @param content the content to set
     */
    public void setContent(T content) {
        this.content = content;
    }

    public String getThrowable() {
        return throwable;
    }

    public void setThrowable(String throwable) {
        this.throwable = throwable;
    }
}
