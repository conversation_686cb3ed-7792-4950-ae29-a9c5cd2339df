package com.alibaba.smart.processor.rpc;

import com.alibaba.smart.processor.model.dto.ToolInfoDTO;
import com.alibaba.smart.processor.model.vo.ToolInfoVO;
import com.alibaba.smart.processor.mvc.annotation.JsonParam;
import com.alibaba.smart.processor.tool.ToolFacade;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Map;

@Controller
@RequestMapping("/api/tool")
@ResponseBody
public class ToolRpc {
    @Resource
    private ToolFacade toolFacade;
    @RequestMapping("/execute")
    public Object executeTool(@JsonParam(name = "toolId") String toolId,
                              @JsonParam(name = "param") Map<String, Object> param) {
        return toolFacade.execute(toolId, param);
    }

    @RequestMapping("/get")
    public ToolInfoVO getToolInfo(@JsonParam(name = "toolId") String toolId) {
        return toolFacade.getToolInfo(toolId);
    }

    @RequestMapping(value = "/update" , method = RequestMethod.POST)
    public void updateToolInfo(@RequestBody ToolInfoDTO toolInfoDTO) {
        toolFacade.updateToolContent(toolInfoDTO.getToolId(), toolInfoDTO.getContent());
    }

    @RequestMapping(value = {"/design"}, method = RequestMethod.POST)
    public Object design(@RequestParam(name = "prompt") String prompt,
                         @RequestParam(name = "ossKey") String ossKey) {
        return toolFacade.designTool(prompt, ossKey);
    }

    @RequestMapping(value = {"/apply"}, method = RequestMethod.POST)
    public Object apply(@RequestParam(name = "ossKey") String ossKey,
                        @RequestParam(name = "toolId") String toolId) {
        return toolFacade.applyTool(ossKey, toolId);
    }


    @RequestMapping(value = {"/save"}, method = RequestMethod.POST)
    public String save(@RequestBody ToolInfoDTO toolInfoDTO) {
        return toolFacade.saveTool(toolInfoDTO);
    }

    @RequestMapping(value = {"/delete"}, method = RequestMethod.POST)
    public void toolDelete(@RequestParam(name = "toolId") String toolId) {
        toolFacade.deleteTool(toolId);
    }
}
