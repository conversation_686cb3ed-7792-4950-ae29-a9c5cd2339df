package com.alibaba.smart.processor.rpc;

import com.alibaba.smart.processor.app.AppFacade;
import com.alibaba.smart.processor.model.dto.AppInfoDTO;
import com.alibaba.smart.processor.model.dto.AppVersionDTO;
import com.alibaba.smart.processor.model.dto.AppVersionRevertDTO;
import com.alibaba.smart.processor.model.dto.PageDto;
import com.alibaba.smart.processor.model.vo.AppInfoVO;
import com.alibaba.smart.processor.model.vo.AppVersionVO;
import com.alibaba.smart.processor.mvc.annotation.JsonParam;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@RequestMapping("/api/app")
@Controller
@ResponseBody
public class AppRpc {
    @Resource
    private AppFacade appFacade;

    @RequestMapping(value = "/create", method = RequestMethod.POST)
    public AppInfoVO createApp(@RequestBody AppInfoDTO appInfoDTO) {
        return appFacade.createApp(appInfoDTO);
    }

    @RequestMapping(value = "/release", method = RequestMethod.POST)
    public void releaseApp(@RequestBody AppInfoDTO appInfoDTO) {
        appFacade.updateApp(appInfoDTO);
    }

    @RequestMapping(value = "/del", method = RequestMethod.POST)
    public void deleteApp(@JsonParam(name = "appId") String appId) {
        appFacade.deleteApp(appId);
    }

    @RequestMapping(value = "/update", method = RequestMethod.POST)
    public void deleteApp(@JsonParam(name = "appId") String appId,
                          @JsonParam(name = "name") String name,
                          @JsonParam(name = "desc") String desc) {
        appFacade.updateApp(appId, name, desc);
    }

    @RequestMapping(value = "/bySession", method = RequestMethod.GET)
    public AppInfoVO getAppBySessionId(@RequestParam String sessionId) {
        return appFacade.getBySessionId(sessionId);
    }
    @RequestMapping(value = "/byAppId", method = RequestMethod.GET)
    public AppInfoVO getAppByAppId(@RequestParam String appId) {
        return appFacade.getByAppId(appId);
    }

    @RequestMapping(value = "/all", method = RequestMethod.GET)
    public PageDto<AppInfoVO> pageApp(@RequestParam("page") Integer page,
                                      @RequestParam("pageSize") Integer pageSize) {
        return appFacade.pageApp(page, pageSize);
    }

    @RequestMapping(value = "/deploy", method = RequestMethod.POST)
    public void deployApp(@RequestBody AppInfoDTO appInfoDTO) {
        appFacade.deployApp(appInfoDTO.getAppId(), appInfoDTO.getSourceCompiledKey(), appInfoDTO.getUpdateNote());
    }
}
