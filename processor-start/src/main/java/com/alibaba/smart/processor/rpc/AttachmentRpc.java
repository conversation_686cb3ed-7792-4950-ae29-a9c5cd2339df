package com.alibaba.smart.processor.rpc;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.smart.processor.attach.AttachmentFacade;
import com.alibaba.smart.processor.attach.ImmOfficeFacade;
import com.alibaba.smart.processor.attach.OssService;
import com.alibaba.smart.processor.attach.model.OssToken;
import com.alibaba.smart.processor.exception.BusinessException;
import com.alibaba.smart.processor.exception.ExceptionContext;
import com.alibaba.smart.processor.model.dto.FileCompleteDTO;
import com.alibaba.smart.processor.utils.UUIDUtils;
import com.aliyun.oss.OSSClient;
import com.aliyun.oss.common.utils.BinaryUtil;
import com.aliyun.oss.model.*;
import lombok.ToString;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URL;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.sql.Date;
import java.util.Map;

@ToString
@Controller
@RequestMapping("/api/attach")
@ResponseBody
public class AttachmentRpc{

    @Value("${oss_endpoint_private}")
    private String endpoint;

    @Value("${oss_key_private}")
    private String accessKeyId;

    @Value("${oss_default_bucket_name_private}")
    private String bucket;

    @Value("${oss_default_bucket_dir:smart_processor}")
    private String dir;

    @Value("${appcode.oss.bucket}")
    private String codeBucketName;

    @Resource
    private AttachmentFacade attachmentFacade;
    @Resource
    private OssService ossService;
    @Resource
    private ImmOfficeFacade immOfficeFacade;
    @Resource
    private OSSClient ossClient;
    @Resource
    @Qualifier(value = "ossClientNew")
    private OSSClient ossClientNew;

    @Autowired
    private HttpServletResponse response;

    @RequestMapping(value = {"/ossSign", "/ossSign.json"})
    public JSONObject getSignedURL(@RequestParam("fileName") String fileName) {
        if(StringUtils.isBlank(fileName)){
            throw new RuntimeException("fileName is empty");
        }

        fileName = fileName.replace(",", "");
        try {
            long expireTime = 30;
            long expireEndTime = System.currentTimeMillis() + expireTime * 1000;
            Date expiration = new Date(expireEndTime);

            PolicyConditions policyConditions = new PolicyConditions();
            policyConditions.addConditionItem(PolicyConditions.COND_CONTENT_LENGTH_RANGE, 0, 10 * 1024 * 1024);
            policyConditions.addConditionItem(MatchMode.StartWith, PolicyConditions.COND_KEY, "smart_processor");

            String postPolicy = ossClient.generatePostPolicy(expiration, policyConditions);

            byte[] binaryData = postPolicy.getBytes(StandardCharsets.UTF_8);
            String encodedPolicy = BinaryUtil.toBase64String(binaryData);
            String postSignature = ossClient.calculatePostSignature(postPolicy);

            JSONObject result = new JSONObject();
            result.put("ossAccessKeyId", accessKeyId);
            result.put("policy", encodedPolicy);

            String fileType = fileName.substring(fileName.lastIndexOf(".") + 1);
            String randomFileName = UUIDUtils.randomUUID().toLowerCase().replaceAll("-","") + "." + fileType;
            result.put("key", randomFileName);
            result.put("dir", dir);
            result.put("signature", postSignature);

            String host = "https://" + bucket + "." + endpoint.replaceFirst("https://","");
            result.put("host", host);
            return result;
        } catch (Exception e) {
            throw new RuntimeException("generate sign fail", e);
        }
    }

    @RequestMapping(value = { "/ossComplete","/ossComplete.json"})
    public JSONObject ossComplete(@RequestBody FileCompleteDTO fileCompleteDTO) {
        String objectName = fileCompleteDTO.getObjectName();
        if(StringUtils.isBlank(objectName)){
            throw new RuntimeException("objectName is empty");
        }
        attachmentFacade.saveFileInfo(fileCompleteDTO);

        long expireTime = 60 * 60 * 1000;
        URL url = ossClient.generatePresignedUrl(bucket, dir + "/" + objectName, new Date(System.currentTimeMillis() + expireTime));
        JSONObject result = new JSONObject();
        result.put("objectName",objectName);

        String fileType = objectName.substring(objectName.lastIndexOf(".") + 1);
        result.put("fileType",fileType);
        result.put("previewUrl","/filePreview?objectName=" + objectName);
        result.put("downloadUrl",url.toString());
        return result;
    }

    @PostMapping(value = {"filePreview", "filePreview.json"})
    public Map<String, String> filePreview(@RequestBody String fileJsonStr) {
        JSONObject fileJson = JSONObject.parseObject(fileJsonStr);
        String objectName = (String) fileJson.get("objectName");
        return immOfficeFacade.previewFile(objectName).toResponse();
    }

    @GetMapping(value = {"getOpenUrl"})
    public String getOpenUrl(@RequestParam("path") String path) {
        long expireTime = 100 * 60 * 1000;
        URL url = ossClient.generatePresignedUrl(bucket, path, new Date(System.currentTimeMillis() + expireTime));
        return url.toString();
    }

    @GetMapping(value = {"getUserDownloadUrl"})
    public String getUserDownloadUrl(@RequestParam("path") String path) {
        long expireTime = 10 * 60 * 1000;
        ObjectMetadata metadata = null;
        try {
            metadata = ossService.getObjectMetadata(path);
        } catch (Exception e) {
            throw new BusinessException(new ExceptionContext("附件不存在或附件名不合法"),"000000","附件不存在或附件名不合法");
        }
        if(metadata == null || metadata.getContentLength() == 0){
            throw new BusinessException(new ExceptionContext("附件不存在或附件名不合法"),"000000","附件不存在或附件名不合法");
        }
        String objectName = "user/" + path;
        URL url = ossClientNew.generatePresignedUrl(codeBucketName, objectName, new Date(System.currentTimeMillis() + expireTime));
        return url.toString();
    }

    @GetMapping(value = {"getOssToken"})
    public OssToken getOssToken(@RequestParam(value = "type",defaultValue = "preview") String type) {
        return ossService.getOssToken(type);
    }

    /**
     * FIXME 通过服务端下载有问题，获取的文件流长度和metadata中返回的长度不一致，导致下载失败，时间原因，先通过oss的地址直接下载
     * @param path
     */
    @GetMapping(value = {"download"})
    public void downloadByPath(@RequestParam("path") String path) {
        // 设置响应头
        response.setContentType("application/octet-stream");
        response.setHeader("Content-Disposition",
                "attachment; filename=\"" + URLEncoder.encode(path, StandardCharsets.UTF_8) + "\"");
        OutputStream os = null;
        InputStream is = null;
        try {
            is = ossService.getFileInputStreamDirectly(path);
            os = response.getOutputStream();
            if (is != null) {
                int data;
                while ((data = is.read()) != -1) {
                    os.write(data);
                }
                os.flush();
            }
        } catch (IOException e) {
            response.setStatus(HttpStatus.INTERNAL_SERVER_ERROR.value());
            throw new BusinessException(new ExceptionContext("文件下载异常，请稍后重试"),"000000","文件下载异常，请稍后重试");
        }finally {
            IOUtils.closeQuietly(os);
            IOUtils.closeQuietly(is);
        }
    }
}
