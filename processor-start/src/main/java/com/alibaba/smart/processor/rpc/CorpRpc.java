package com.alibaba.smart.processor.rpc;

import com.alibaba.smart.processor.corp.CorpConfigFacade;
import com.alibaba.smart.processor.model.dto.CorpConfigDTO;
import com.alibaba.smart.processor.model.vo.CorpConfigVO;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;

@RequestMapping("/api/corp")
@Controller
@ResponseBody
public class CorpRpc {
    @Resource
    private CorpConfigFacade corpConfigFacade;
    @RequestMapping(value = "/saveConfig", method = RequestMethod.POST)
    public void save(@RequestBody CorpConfigDTO corpConfigDTO) {
        corpConfigFacade.saveOrUpdateCorpConfig(corpConfigDTO);
    }

    @RequestMapping(value = "/getConfig", method = RequestMethod.GET)
    public CorpConfigVO getConfig() {
        return corpConfigFacade.getCorpConfig();
    }
}
