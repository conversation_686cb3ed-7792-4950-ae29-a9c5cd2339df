package com.alibaba.smart.processor.filter;

import org.apache.commons.lang3.StringUtils;

import javax.crypto.*;
import javax.crypto.spec.SecretKeySpec;
import java.io.UnsupportedEncodingException;
import java.nio.charset.StandardCharsets;
import java.security.SecureRandom;

public class CipherUtil {
    private static Cipher clientEncryptCipher;
    private static SecretKeySpec clientKeySpec;
    private static Cipher        clientDecryptCipher;
    private static final String  AES       = "AES";
    private static final String  SHA1PRNG  = "SHA1PRNG";
    private static String        clientKey = "24680VALPO)($~`;";
    static {
        KeyGenerator clientkgen;
        try {
            clientkgen = KeyGenerator.getInstance(AES);
            SecureRandom clientSecureRandom = SecureRandom.getInstance(SHA1PRNG);
            clientSecureRandom.setSeed(clientKey.getBytes());
            clientkgen.init(128, clientSecureRandom);

            SecretKey clientSecretKey = clientkgen.generateKey();
            byte[] clientEnCodeFormat = clientSecretKey.getEncoded();
            clientKeySpec = new SecretKeySpec(clientEnCodeFormat, AES);
            clientDecryptCipher = Cipher.getInstance(AES);
            clientDecryptCipher.init(Cipher.DECRYPT_MODE, clientKeySpec);
            clientEncryptCipher = Cipher.getInstance(AES);
            clientEncryptCipher.init(Cipher.ENCRYPT_MODE, clientKeySpec);
        } catch (Exception e) {

        }
    }

    public static String encodeText(String text, String charset) throws IllegalBlockSizeException, BadPaddingException, UnsupportedEncodingException {
        if (text == null) {
            return null;
        }
        byte[] resultBytes = null;
        String result = null;
        byte[] byteContent = null;
        if (StringUtils.isBlank(charset)) {
            byteContent = text.getBytes(StandardCharsets.UTF_8);
        }
        else {
            byteContent = text.getBytes(charset);
        }
        resultBytes = clientEncryptCipher.doFinal(byteContent);
        result = bytes2Hex(resultBytes);
        return result;
    }

    private static String bytes2Hex(byte[] bytes) {
        if (bytes == null) {
            return null;
        }
        StringBuilder result = new StringBuilder();
        String tmp = "";
        for (int n = 0; n < bytes.length; n++) {
            tmp = (Integer.toHexString(bytes[n] & 0XFF));
            if (tmp.length() == 1) {
                result.append("0").append(tmp);
            } else {
                result.append(tmp);
            }
        }
        return result.toString().toUpperCase();
    }

    public static String decodeText(String cipherText, String charset) throws IllegalBlockSizeException, BadPaddingException, UnsupportedEncodingException {
        if (cipherText == null) {
            return null;
        }
        byte[] resultBytes = null;
        byte[] byteContent = hex2Bytes(cipherText);
        resultBytes = clientDecryptCipher.doFinal(byteContent);
        if (StringUtils.isBlank(charset)) {
            return new String(resultBytes, StandardCharsets.UTF_8);
        }
        else {
            return new String(resultBytes, charset);
        }

    }

    private static byte[] hex2Bytes(String hexStr) {
        if (StringUtils.isBlank(hexStr)) {
            return new byte[0];
        }
        byte[] b = hexStr.getBytes();
        if ((b.length % 2) != 0) {
            throw new IllegalArgumentException("length need to be even");
        }
        byte[] b2 = new byte[b.length / 2];
        for (int n = 0; n < b.length; n += 2) {
            String item = new String(b, n, 2);
            b2[n / 2] = (byte) Integer.parseInt(item, 16);
        }
        return b2;
    }
}
