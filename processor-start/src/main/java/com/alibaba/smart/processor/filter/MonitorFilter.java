package com.alibaba.smart.processor.filter;

import com.alibaba.smart.processor.context.UserContextUtil;
import com.alibaba.smart.processor.context.UserHolderContext;
import com.alibaba.smart.processor.exception.BusinessException;
import com.alibaba.smart.processor.monitor.MonitorData;
import com.alibaba.smart.processor.monitor.MonitorLoggerUtils;
import com.alibaba.smart.processor.utils.HostUtil;
import com.alibaba.smart.processor.utils.YNConstant;
import com.taobao.eagleeye.EagleEye;
import org.apache.catalina.connector.ClientAbortException;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import javax.servlet.*;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;

public class MonitorFilter extends AbstractRequestFilter {
    @Override
    public void doFilter(ServletRequest servletRequest, ServletResponse servletResponse, FilterChain filterChain)
            throws IOException, ServletException {
        HttpServletRequest httpRequest = (HttpServletRequest) servletRequest;
        HttpServletResponse httpResponse = (HttpServletResponse) servletResponse;
        // 排除请求
        if (isExcluded(httpRequest)) {
            filterChain.doFilter(httpRequest, httpResponse);
            return;
        }

        long startTime = System.currentTimeMillis();
        String successMark = YNConstant.Y.getKey();
        String businessSuccessMark = YNConstant.Y.getKey();
        Throwable exception = null;
        try {
            filterChain.doFilter(httpRequest, httpResponse);
        } catch (Throwable e) {
            exception = e;
            successMark = YNConstant.N.getKey();

            List<Class> excludeExceptions = Arrays.asList(BusinessException.class, ClientAbortException.class);
            if(CollectionUtils.isNotEmpty(excludeExceptions)){
                boolean isExcludeException = false;
                for(Class excludeException : excludeExceptions){
                    if(excludeException.isAssignableFrom(exception.getClass())){
                        isExcludeException = true;
                        break;
                    }
                }
                if(!isExcludeException){
                    businessSuccessMark = YNConstant.N.getKey();
                }
            }
            throw e;
        }finally {
            buildInfoAndLog(httpRequest,httpResponse,exception,startTime,successMark,businessSuccessMark);
        }
    }


    public void buildInfoAndLog(ServletRequest request, ServletResponse response,Throwable exception, long startTime, String successMark,String businessSuccessMark){
        HttpServletRequest httpRequest = (HttpServletRequest) request;
        MonitorData monitorData = new MonitorData();
        monitorData.setUserId(UserContextUtil.userId());
        monitorData.setDomain("http_all");
        monitorData.setChildrenType(httpRequest.getRequestURI());
        monitorData.setName(httpRequest.getRequestURI());
        monitorData.setSuccessMark(successMark);
        monitorData.setBusinessSuccessMark(businessSuccessMark);
        monitorData.setElapseTime(System.currentTimeMillis() - startTime);
        monitorData.setException(exception);
        monitorData.setTraceId(EagleEye.getTraceId());
        monitorData.setLocalIPAddr(HostUtil.getLocalIP());
        monitorData.setBizExtFields(Collections.singletonList(UserContextUtil.corpId()));
        monitorData.setAppName("simple-processor");

        MonitorLoggerUtils.log(monitorData, transParameterMap(request), null);
    }

    public Map<String, String> transParameterMap(ServletRequest request) {
        Map<String, String[]> parameterMap = request.getParameterMap();
        Map<String, String> paramMap = new HashMap<>();
        if (parameterMap.isEmpty()) {
            return paramMap;
        }
        for (Map.Entry<String, String[]> entry : parameterMap.entrySet()) {
            if (entry.getValue() != null && entry.getValue().length > 0) {
                paramMap.put(entry.getKey(), StringUtils.isNotEmpty(entry.getValue()[0]) ? entry.getValue()[0] : "");
            }
        }

        return paramMap;
    }

}
