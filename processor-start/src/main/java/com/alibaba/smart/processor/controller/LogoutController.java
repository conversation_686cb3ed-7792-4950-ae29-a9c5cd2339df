package com.alibaba.smart.processor.controller;

import com.alibaba.smart.processor.context.UserHolderContext;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;

@RestController
public class LogoutController {

    @Value("${dingtalk.sso.logout-url:https://pre-polymind.dingding.xin/logout}")
    private String dingtalkLogoutUrl;

    @GetMapping("/logout")
    public void logout(HttpServletRequest request, HttpServletResponse response) throws IOException {
        // 1. 删除本地 Cookie
        Cookie userCookie = new <PERSON>ie("dingtalk_user", null);
        userCookie.setPath("/");
        userCookie.setMaxAge(0); // 设置为 0 表示删除 Cookie
        response.addCookie(userCookie);

        // 2. 清除上下文中的用户信息（如有）
        UserHolderContext.removeThreadLocal();

        // 3. 构造钉钉 SSO 登出 URL
        String redirectUri = "https://pre-polymind.dingding.xin/home";
        String logoutUrl = String.format("%s?redirect_uri=%s",
                dingtalkLogoutUrl,
                URLEncoder.encode(redirectUri, StandardCharsets.UTF_8));

        // 4. 重定向用户到钉钉登出页面
        response.sendRedirect(logoutUrl);
    }

}
