package com.alibaba.smart.processor.rpc;

import com.alibaba.smart.processor.fc.AliyunFunctionComputeFacade;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;

@RequestMapping("/api/fc")
@Controller
@ResponseBody
public class FunctionComputeRpc {
    @Resource
    private AliyunFunctionComputeFacade aliyunFunctionComputeFacade;

    @RequestMapping(value = "/executeByKey", method = RequestMethod.POST)
    public Object executeByKey(@RequestParam(name = "ossKey") String ossKey) {
        return aliyunFunctionComputeFacade.executeWithOssKey("python_execute_event", ossKey);
    }
    @RequestMapping(value = "/executeByCode",method = RequestMethod.POST)
    public Object executeByCode(@RequestParam(name = "script") String script) {
        return aliyunFunctionComputeFacade.executeWithContent("python_execute_event", script, null);
    }
}
