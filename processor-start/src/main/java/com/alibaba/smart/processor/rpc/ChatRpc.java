package com.alibaba.smart.processor.rpc;

import com.alibaba.smart.processor.chat.ChatFacade;
import com.alibaba.smart.processor.history.ChatHistoryFacade;
import com.alibaba.smart.processor.model.dto.ChatMessageDTO;
import com.alibaba.smart.processor.model.dto.FileAskDTO;
import com.alibaba.smart.processor.model.dto.StreamHttpClientContext;
import com.alibaba.smart.processor.model.vo.ChatSessionVO;
import com.alibaba.smart.processor.model.vo.ChatModelVO;
import com.alibaba.smart.processor.mvc.annotation.JsonParam;
import com.alibaba.smart.processor.utils.StreamHttpClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Controller;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import javax.annotation.Resource;
import java.util.*;

@Controller
@RequestMapping("/api/chat")
@ResponseBody
@Slf4j
public class ChatRpc {
    @Resource
    private ChatFacade chatFacade;

    @Resource
    private ChatHistoryFacade chatHistoryFacade;


    @RequestMapping(value = {"/new"})
    public String createNewChat() {
        log.info("记录日志接入");
        return chatFacade.createSession();
    }

    @RequestMapping(value = {"/list"}, method = RequestMethod.GET)
    public List<ChatSessionVO> listSessions() {
        return chatFacade.listSessions();
    }

    @RequestMapping(value = {"/update"}, method = RequestMethod.POST)
    public void updateSession(@JsonParam(name = "sessionId") String sessionId,
                              @JsonParam(name = "title") String title) {
        chatFacade.updateSession(sessionId, title);
    }

    @RequestMapping(value = {"/del"}, method = RequestMethod.POST)
    public void removeChat(@JsonParam(name = "sessionId") String sessionId) {
        chatFacade.deleteSession(sessionId);
    }

    @RequestMapping(value = {"/completions"}, method = RequestMethod.POST)
    public SseEmitter ask(@RequestBody FileAskDTO fileAskDTO) throws Exception {
        return chatFacade.ask(fileAskDTO);
    }

    @RequestMapping(value = {"/async-stream"}, produces = "text/event-stream;charset=UTF-8")
    public SseEmitter asyncStream(@RequestParam(name = "apiName") String apiName) throws Exception {
        SseEmitter emitter = new SseEmitter(60*1000L);
        StreamHttpClientContext context = new StreamHttpClientContext();
        context.setEndpoint("https://polymind-portal.alibaba.net/");
        context.setApiName(apiName);
        context.setSseEmitter(emitter);
        StreamHttpClient.invoke(context);
        return emitter;
    }

    /**
     * 会话历史接口
     *
     * @param sessionId
     * @return
     */
    @RequestMapping(value = {"/get"}, method = RequestMethod.GET)
    public ChatModelVO listHistory(@RequestParam(name = "sessionId") String sessionId) {
        return chatHistoryFacade.listMessage(sessionId);
    }

}
