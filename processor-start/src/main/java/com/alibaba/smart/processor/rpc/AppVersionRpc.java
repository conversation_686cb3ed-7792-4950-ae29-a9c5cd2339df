package com.alibaba.smart.processor.rpc;

import com.alibaba.smart.processor.app.AppFacade;
import com.alibaba.smart.processor.exception.BusinessException;
import com.alibaba.smart.processor.exception.ExceptionContext;
import com.alibaba.smart.processor.model.dto.AppVersionDTO;
import com.alibaba.smart.processor.model.dto.AppVersionRevertDTO;
import com.alibaba.smart.processor.model.dto.PageDto;
import com.alibaba.smart.processor.model.vo.AppVersionVO;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@RequestMapping("/api/version")
@Controller
@ResponseBody
public class AppVersionRpc {

    @Resource
    private AppFacade appFacade;

    @RequestMapping(value = "/all", method = RequestMethod.GET)
    public PageDto<AppVersionVO> getAllAppVersions(@RequestParam("appId") String appId,
                                                   @RequestParam(value = "currentPage", defaultValue = "1") Integer currentPage,
                                                   @RequestParam(value = "pageSize", defaultValue = "10") Integer pageSize,
                                                   @RequestParam(value = "key", required = false) String key) {
        if(currentPage <= 0 || pageSize > 100){
            throw new BusinessException(new ExceptionContext("分页参数不合法"), "000000","分页参数不合法");
        }
        return appFacade.getAppVersions(appId, currentPage, pageSize, key);
    }

    @RequestMapping(value = "/update", method = RequestMethod.POST)
    public void updateVersionNode(@RequestBody AppVersionDTO appVersionDTO) {
        appFacade.updateVersionNode(appVersionDTO);
    }

    @RequestMapping(value = "/rollback", method = RequestMethod.POST)
    public void revertVersion(@RequestBody AppVersionRevertDTO appVersionRevertDTO) {
        appFacade.revertVersion(appVersionRevertDTO);
    }

}
