package com.alibaba.smart.processor.mvc.utils;

import org.apache.commons.lang3.StringUtils;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 */
public class HttpRequestUtils {

    public static boolean isRpcRequest(HttpServletRequest request){
        return isJsonRequest(request) || isJsonpRequest(request);
    }

    public static boolean isJsonRequest(HttpServletRequest request){
        if(request == null){
            return false;
        }
        if("json".equalsIgnoreCase(getURISuffix(request.getRequestURI()))){
            return true;
        }

        String contentType = request.getContentType();
        if(StringUtils.isNotBlank(contentType) && contentType.startsWith("application/json")){
            return true;
        }
        return false;
    }

    public static boolean isJsonpRequest(HttpServletRequest request){
        if(request == null){
            return false;
        }
        if("jsonp".equalsIgnoreCase(getURISuffix(request.getRequestURI()))){
            return true;
        }
        return false;
    }


    private static String getURISuffix(String uri){
        if(uri == null){
            return null;
        }
        String[] res = uri.split("\\.");
        if(res.length > 1){
            return res[res.length-1];
        }
        return null;
    }

}
