package com.alibaba.smart.processor.filter;

import com.alibaba.security.SecurityUtil;
import org.apache.commons.lang3.StringUtils;

import javax.servlet.*;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Pattern;

public class CrossOriginFilter implements Filter {
    private static final String OPTION_METHOD = "OPTIONS";

    private List<Pattern> whiteCrossOriginPatterns = new ArrayList<>();

    @Override
    public void init(FilterConfig filterConfig) throws ServletException {
        String whiteCrossOriginList = filterConfig.getInitParameter("whiteCrossOriginList");
        if (StringUtils.isNotBlank(whiteCrossOriginList)) {
            for (String exclude : whiteCrossOriginList.split(",")) {
                whiteCrossOriginPatterns.add(Pattern.compile(exclude));
            }
        }
    }

    @Override
    public void doFilter(ServletRequest servletRequest, ServletResponse servletResponse, FilterChain filterChain)
            throws IOException, ServletException {
        HttpServletRequest httpRequest = (HttpServletRequest) servletRequest;
        HttpServletResponse httpResponse = (HttpServletResponse) servletResponse;

        httpResponse.setHeader("Cross-Origin-Opener-Policy", "same-origin");
        httpResponse.setHeader("Cross-Origin-Embedder-Policy", "credentialless");

        if (checkOriginValid(httpRequest)) {
            setAllowCrossOrigin(httpRequest, httpResponse);
            if (OPTION_METHOD.equalsIgnoreCase(httpRequest.getMethod())) {
                return;
            }
        }
        filterChain.doFilter(httpRequest, httpResponse);
    }

    private boolean checkOriginValid(HttpServletRequest httpRequest) {
        String origin = httpRequest.getHeader("origin");
        if (StringUtils.isNotBlank(origin)) {
            for (Pattern whiteCrossOriginPattern : whiteCrossOriginPatterns) {
                if (whiteCrossOriginPattern.matcher(origin).find()) {
                    return true;
                }
            }
        }
        return false;
    }

    private void setAllowCrossOrigin(HttpServletRequest httpRequest, HttpServletResponse httpResponse) {
        String origin = httpRequest.getHeader("origin");
        if (StringUtils.isBlank(origin)) {
            origin = "*";
        }
        String safeOrigin = SecurityUtil.getSafeUrl(origin); // 重点是这句。
        httpResponse.setHeader("Access-Control-Allow-Origin", safeOrigin);
        httpResponse.setHeader("Access-Control-Allow-Methods", "POST, GET, OPTIONS, DELETE");
        httpResponse.setHeader("Access-Control-Allow-Headers", "Content-Type, Authorization, Content-Range");
        httpResponse.setHeader("Access-Control-Max-Age", "3600");
        httpResponse.setHeader("Access-Control-Allow-Credentials", "true");
    }

    @Override
    public void destroy() {

    }

}
