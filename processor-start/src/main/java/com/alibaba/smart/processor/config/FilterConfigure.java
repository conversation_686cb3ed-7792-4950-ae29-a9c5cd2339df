package com.alibaba.smart.processor.config;

import com.alibaba.smart.processor.filter.*;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.Ordered;

import static com.alibaba.smart.processor.config.DingtalkSsoConfig.*;

@Configuration
public class FilterConfigure {

    @Value("${dingtalk.sso.exclude:/checkpreload.htm}")
    private String dingtalkSsoExcludes;

    @Value("${smart.processor.enable.sso}")
    private String enableSso;

    @Bean
    public FilterRegistrationBean cachingRequestBodyFilterRegistrationBean() {
        FilterRegistrationBean registration = new FilterRegistrationBean(new CachingRequestBodyFilter());
        registration.setName("cachingRequestBodyFilter");
        registration.setOrder(Ordered.HIGHEST_PRECEDENCE);
        return registration;
    }

    @Bean
    @ConditionalOnProperty(name = "smart.processor.enable.sso", havingValue = "true")
    public FilterRegistrationBean dingtalkSsoFilterRegistrationBean(DingtalkSsoConfig dingtalkSsoConfig) {
        DingtalkSsoFilter dingtalkSsoFilter = new DingtalkSsoFilter();
        dingtalkSsoFilter.setSsoExcludes(dingtalkSsoConfig.getDingtalkSsoExcludes());
        FilterRegistrationBean registration = new FilterRegistrationBean(dingtalkSsoFilter);
        registration.addInitParameter(DINGTALK_SSO_ENVIRONMENT, dingtalkSsoConfig.getSsoEnv());
        registration.addInitParameter(DINGTALK_SSO_APP_KEY, dingtalkSsoConfig.getDingtalkSsoAppKey());
        registration.addInitParameter(DINGTALK_SSO_TENANT_ID, dingtalkSsoConfig.getDingtalkSsoTenantId());
        registration.addInitParameter(DINGTALK_SSO_REQUIRE_ORG, "true");
        registration.addInitParameter(DINGTALK_SSO_CALL_BACK_PATH, dingtalkSsoConfig.getDingtalkSsoCallbackPath());
        registration.addInitParameter(DINGTALK_SSO_CLIENT_ID, dingtalkSsoConfig.getDingtalkSsoClientId());
        registration.addInitParameter(DINGTALK_SSO_EXCLUDES, dingtalkSsoConfig.getDingtalkSsoExcludes());
        registration.addUrlPatterns("/*");
        registration.setName("dingtalkSsoFilter");
        registration.setOrder(-205);
        return registration;
    }

    @Bean
    public FilterRegistrationBean userContextFilterRegistrationBean() {
        FilterRegistrationBean registration = new FilterRegistrationBean(new UserContextFilter());
        registration.setName("userContextFilter");
        registration.addInitParameter("excludes", dingtalkSsoExcludes);
        registration.setOrder(-204);
        return registration;
    }

    @Bean
    public FilterRegistrationBean mockUserFilterRegistrationBean() {
        FilterRegistrationBean registration = new FilterRegistrationBean(new MockUserFilter());
        registration.addInitParameter("enableSso", enableSso);
        registration.addInitParameter("excludes", dingtalkSsoExcludes);
        registration.setName("mockUserFilter");
        registration.setOrder(-200);
        return registration;
    }

    @Bean
    public FilterRegistrationBean monitorFilterRegistrationBean() {
        FilterRegistrationBean registration = new FilterRegistrationBean(new MonitorFilter());
        registration.setName("monitorFilter");
        registration.addInitParameter("excludes", dingtalkSsoExcludes);
        registration.setOrder(-190);
        return registration;
    }

    @Bean
    public FilterRegistrationBean crossOriginFilterRegistrationBean() {
        FilterRegistrationBean registration = new FilterRegistrationBean(new CrossOriginFilter());
        registration.addInitParameter("whiteCrossOriginList", "localhost,.*\\.alibaba.net$,.*\\.alibaba-inc.com$,.*\\.aliwork.com$,.*\\.dingtalkapps.com$");
        registration.setName("crossOriginFilter");
        registration.setOrder(-15);
        return registration;
    }


}
