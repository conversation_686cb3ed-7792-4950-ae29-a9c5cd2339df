package com.alibaba.smart.processor.controller;

import com.alibaba.security.SecurityUtil;
import com.alibaba.smart.processor.context.UserContextUtil;
import com.alibaba.smart.processor.diamond.DiamondEnums;
import com.alibaba.smart.processor.user.UserService;
import com.alibaba.smart.processor.user.model.UserInfoModel;
import com.dingtalk.common.sso.filter.RpcContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.ui.Model;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.Arrays;
import java.util.List;

@Slf4j
public abstract class UserController extends AbstractController {

    @Resource
    private UserService userService;

    @Value("${smart.processor.enable.sso}")
    private String enableSso;

    @Override
    public String execute(Model context){
        super.execute(context);

        boolean hasAccess = checkAccess();
        if(!hasAccess){
            try {
                response.sendRedirect("/errorPage?errorCode=000001");
            } catch (Exception e) {
                logger.error("重定向到错误页面失败",e);
            }
        }

        context.addAttribute("userId",UserContextUtil.userId());
        context.addAttribute("corpId",UserContextUtil.corpId());
        if("true".equals(enableSso)){
            boolean isAdmin = bizPermissionService.isSuperOrCorpMainManager();
            UserInfoModel userInfoModel = userService.getUserInfo(UserContextUtil.uid());
            context.addAttribute("userName", SecurityUtil.escapeHtml(userInfoModel.getUserName()));
            context.addAttribute("avatar", userInfoModel.getAvatar());
            context.addAttribute("isAdmin", isAdmin);
        }
        return null;
    }
}
