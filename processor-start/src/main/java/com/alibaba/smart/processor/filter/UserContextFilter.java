package com.alibaba.smart.processor.filter;

import com.alibaba.smart.processor.context.LoginUser;
import com.alibaba.smart.processor.context.UserHolderContext;
import com.dingtalk.common.sso.filter.RpcContext;

import javax.servlet.*;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

public class UserContextFilter extends AbstractRequestFilter {

    @Override
    public void doFilter(ServletRequest servletRequest, ServletResponse servletResponse, FilterChain filterChain)
            throws IOException, ServletException {
        HttpServletRequest httpRequest = (HttpServletRequest) servletRequest;
        HttpServletResponse httpResponse = (HttpServletResponse) servletResponse;

        boolean isExcluded = isExcluded(httpRequest);
        if(isExcluded){
            filterChain.doFilter(httpRequest, httpResponse);
            return;
        }

        if(RpcContext.uid() != null && RpcContext.corpId() != null){
            LoginUser loginUser = new LoginUser();
            loginUser.setUserId(RpcContext.userId());
            loginUser.setUid(RpcContext.uid());
            loginUser.setCorpId(RpcContext.corpId());
            UserHolderContext.setLoginUser(loginUser);
        }
        filterChain.doFilter(httpRequest, httpResponse);
    }
}
