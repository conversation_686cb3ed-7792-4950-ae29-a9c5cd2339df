package com.alibaba.smart.processor.rpc;

import com.alibaba.smart.processor.history.ChatHistoryFacade;
import com.alibaba.smart.processor.model.dto.ChatMessageDTO;
import com.alibaba.smart.processor.model.vo.ChatModelVO;
import com.alibaba.smart.processor.model.vo.MessageVO;
import org.springframework.stereotype.Controller;
import org.springframework.util.Assert;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@Controller
@RequestMapping("/api/message")
@ResponseBody
public class MessageRpc {


    @Resource
    private ChatHistoryFacade chatHistoryFacade;

    /**
     * 新建消息接口
     *
     * @param chatMessageDTO
     * @return
     */
    @RequestMapping(value = {"/add"}, method = RequestMethod.POST)
    public MessageVO addMessage(@RequestBody ChatMessageDTO chatMessageDTO) {
        if (StringUtils.isEmpty(chatMessageDTO.getSessionId())) {
            throw new RuntimeException("sessionId不能为空");
        }
        return chatHistoryFacade.addMessage(
                chatMessageDTO.getSessionId(),
                chatMessageDTO.getMessageId(),
                chatMessageDTO.getType(),
                chatMessageDTO.getContent(),
                chatMessageDTO.getFiles(),
                chatMessageDTO.getHistoryResponses());
    }

    /**
     * 保存历史消息接口
     *
     * @param chatMessageDTO
     * @return
     */
    @RequestMapping(value = {"/update"}, method = RequestMethod.POST)
    public void updateMessage(@RequestBody ChatMessageDTO chatMessageDTO) {
        Assert.notNull(chatMessageDTO.getSessionId(), "sessionId不能为空");
        Assert.notNull(chatMessageDTO.getMessageId(), "messageId不能为空");
        chatHistoryFacade.updateMessage(
                chatMessageDTO.getSessionId(),
                chatMessageDTO.getMessageId(),
                chatMessageDTO.getContent());
    }

}
