package com.alibaba.smart.processor.controller;

import com.alibaba.smart.processor.app.AppFacade;
import com.alibaba.smart.processor.app.AppResourceService;
import com.alibaba.smart.processor.exception.BusinessException;
import com.alibaba.smart.processor.exception.ExceptionContext;
import com.alibaba.smart.processor.model.AppResourceDO;
import com.alibaba.smart.processor.model.vo.AppInfoVO;
import com.alibaba.smart.processor.model.vo.AppVersionVO;
import com.aliyun.oss.OSS;
import com.aliyun.oss.model.OSSObject;
import com.aliyun.oss.model.ObjectMetadata;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.Objects;

@Slf4j
@Controller
@ResponseBody
public class AppController {
    @Autowired
    private HttpServletResponse response;
    @Autowired
    private HttpServletRequest request;
    @Autowired
    @Qualifier("ossClientNew")
    private OSS ossClient;
    @Value("${appcode.oss.bucket}")
    private String smartProcessorBucket;
    @Resource
    private AppFacade appFacade;
    @Resource
    private AppResourceService appResourceService;

    @GetMapping("/preview/{uuid}/**")
    public void preview(@PathVariable String uuid){
        String fullPath = request.getRequestURI();
        String resourcePackage = "preview/target/" + uuid + "/";
        String resourcePath = fullPath.split("/preview/" + uuid + "/")[1];

        loadResources(resourcePackage, resourcePath);
    }

    @GetMapping("/app/{appId}/**")
    public void product(@PathVariable String appId){
        String sourceCompiledKey = null;
        String resourceId = null;
        try {
            AppInfoVO appInfoVO = appFacade.getByAppId(appId);
            if (Objects.isNull(appInfoVO)) {
                response.sendRedirect("/errorPage?errorCode=000100");
                return;
            }
            AppVersionVO appVersionVO = appFacade.getAppVersion(appId, appInfoVO.getVersion());
            if (Objects.isNull(appVersionVO) || StringUtils.isBlank(appVersionVO.getResourceId())) {
                response.sendRedirect("/errorPage?errorCode=000101");
                return;
            }
            resourceId = appVersionVO.getResourceId();
        } catch (IOException e) {
            log.error("重定向到错误页面失败",e);
            return;
        }
        AppResourceDO appResourceDO = appResourceService.getAppResource(resourceId);
        sourceCompiledKey = appResourceDO.getSourceCompiledKey();
        String fullPath = request.getRequestURI();
        String resourcePackage = "product/target/" + sourceCompiledKey + "/";
        String resourcePath = fullPath.split("/app/" + appId + "/")[1];

        loadResources(resourcePackage, resourcePath);
    }

    private void loadResources(String resourcePackage, String resourcePath){
        OSSObject result = ossClient.getObject(smartProcessorBucket, resourcePackage + resourcePath);
        ObjectMetadata metadata = result.getObjectMetadata();

        response.setContentType(metadata.getContentType());
        response.setCharacterEncoding("UTF-8");
        response.setContentLength((int) metadata.getContentLength());

        try (InputStream inputStream = result.getObjectContent();
             OutputStream outputStream = response.getOutputStream())
        {
            byte[] buffer = new byte[4096];
            int length;
            while ((length = inputStream.read(buffer)) != -1) {
                outputStream.write(buffer, 0, length);
            }
        } catch (IOException e) {
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
        }
    }
}

