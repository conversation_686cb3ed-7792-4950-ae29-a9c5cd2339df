package com.alibaba.smart.processor.mvc.exception;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.alibaba.smart.processor.exception.BusinessException;
import com.alibaba.smart.processor.mvc.result.PojoResult;
import com.alibaba.smart.processor.mvc.utils.HttpRequestUtils;
import com.google.common.collect.Sets;
import org.apache.catalina.connector.ClientAbortException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerExceptionResolver;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.PrintWriter;
import java.io.StringWriter;
import java.util.Set;

/**
 * <AUTHOR>
 */
@Component
public class RpcHandlerExceptionResolver implements HandlerExceptionResolver {

    private static final Logger logger = LoggerFactory.getLogger(RpcHandlerExceptionResolver.class);

    public static final String UTF_8 = "UTF-8";
    public static final String CACHE_CONTROL = "Cache-Control";
    public static final String NO_CACHE_MUST_REVALIDATE = "no-cache, must-revalidate";

    /**
     * 需要透出前端的code
     */
    private static final Set<String> NEED_RETURN_CODE = Sets.newHashSet();

    private static final String PUBLIC_ERROR_CODE = "000000";

    @Override
    public ModelAndView resolveException(HttpServletRequest request, HttpServletResponse response, Object handler,
                                         Exception ex) {
        if (ex instanceof ClientAbortException) {
            // 针对客户端提前关闭的异常，直接忽略不需要处理
            return new ModelAndView();
        }

        if(ex instanceof BusinessException
                && !(HttpRequestUtils.isJsonpRequest(request) || HttpRequestUtils.isJsonRequest(request))) {
            try {
                BusinessException businessException = (BusinessException) ex;
                String errorCode = businessException.getErrorCode();
                response.sendRedirect("/errorPage?errorCode=" + errorCode);
            } catch (IOException e) {
                logger.error("重定向请求到错误页面失败", ex);
            }
        }

        logger.error("request failed.", ex);
        // RPC请求，返回PojoResult对象
        ModelAndView mv = new ModelAndView();
        PojoResult pojoResult = new PojoResult();
        pojoResult.setErrorMessage("500", ex.getMessage());

        logger.error("RpcHandlerExceptionResolver all exception, errorMsg:{}", pojoResult.getErrorMsg(), ex);
        // response config
        response.setStatus(HttpStatus.OK.value());
        response.setContentType(MediaType.APPLICATION_JSON_VALUE);
        response.setCharacterEncoding(UTF_8);
        response.setHeader(CACHE_CONTROL, NO_CACHE_MUST_REVALIDATE);

        try {
            response.getWriter().write(JSON.toJSONString(pojoResult,
                    SerializerFeature.WRITE_MAP_NULL_FEATURES,
                    SerializerFeature.QuoteFieldNames,
                    SerializerFeature.DisableCircularReferenceDetect));
        } catch (IOException e) {
            logger.error("failed {}", e.getMessage(), e);
        }

        return mv;
    }

    private String convertStackToString(Exception e) {
        StringWriter sw = new StringWriter();
        PrintWriter pw = new PrintWriter(sw);
        e.printStackTrace(pw);
        String sStackTrace = sw.toString();
        return sStackTrace;
    }

}
