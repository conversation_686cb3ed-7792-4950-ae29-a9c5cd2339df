package com.alibaba.smart.processor.controller;

import com.alibaba.boot.velocity.annotation.VelocityLayout;
import com.alibaba.smart.processor.attach.AttachmentFacade;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

import javax.annotation.Resource;

@Controller
public class FilePreview {
    @Resource
    private AttachmentFacade attachmentFacade;

    @RequestMapping("/filePreview")
    @VelocityLayout("layout/filePreview.vm")
    public void execute(Model context, @RequestParam(name = "objectName") String objectName) {
        context.addAttribute("objectName", objectName);
        context.addAttribute("accessUrl", "previewURL");
        context.addAttribute("requestUrl", "/attach/filePreview.json");
        context.addAttribute("refreshUrl", "/attach/refreshToken.json");
        context.addAttribute("refreshType", "preview");
    }
}
