package com.alibaba.smart.processor.filter;


import com.alibaba.platform.buc.sso.common.tool.RegexUtil;
import com.alibaba.security.SecurityUtil;
import com.alibaba.smart.processor.config.DingtalkSsoConfig;
import com.dingtalk.common.sso.exception.SsoException;
import com.dingtalk.common.sso.filter.DtSsoFilter;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;

import javax.servlet.*;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.regex.Pattern;


/**
 * 钉钉统一登录
 *
 */
public class DingtalkSsoFilter extends DtSsoFilter {

    private static final Logger logger = LoggerFactory.getLogger(DingtalkSsoFilter.class);

    private static String LOGOUT_URL = "/logout";

    private static final String COOKIE_NAME_ACCOUNT = "account";

    public String ssoExcludes;
    Pattern[] compiledPatterns;

    @Value("${dingtalk.sso.exclude:/checkpreload.htm}")
    private String dingtalkSsoExcludes;

    @Override
    public void init(FilterConfig filterConfig) {
        super.init(filterConfig);
    }


    @Override
    public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain)
            throws IOException, ServletException {
        HttpServletRequest httpRequest = (HttpServletRequest) request;
        HttpServletResponse httpResponse = (HttpServletResponse) response;

        String requestUri = httpRequest.getRequestURI();
        boolean isLoginExcept = initParamContextIsExcluded(requestUri);
        if (isLoginExcept) {
            chain.doFilter(request, response);
            return;
        }

        String requestURI = httpRequest.getRequestURI();
        if (requestURI.startsWith(LOGOUT_URL)) {
            try {
                SSOCookieUtils.removeCookie(COOKIE_NAME_ACCOUNT,  SSOCookieUtils.PATH, SSOCookieUtils.ACCOUNT_DOMAIN, true, httpResponse);
            } catch (Exception e) {
                logger.error("remove cookie error:", e);
            }

            httpResponse.sendRedirect("/");
            return;
        }

        try {
            super.doFilter(request, response, chain);
        } catch (SsoException ssoException) {
            String defaultRedirectUrl = "/home";
            if (StringUtils.containsAny(requestUri, DingtalkSsoConfig.DINGTALK_SSO_CALL_BACK_PATH_VALUE)) {
                String redirectUrl = httpRequest.getParameter("continue");
                if (StringUtils.isBlank(redirectUrl)) {
                    httpResponse.sendRedirect(defaultRedirectUrl);
                    return;
                }
                String safeUrl = SecurityUtil.getSafeUrl(redirectUrl);
                if (StringUtils.isBlank(safeUrl)) {
                    httpResponse.sendRedirect(defaultRedirectUrl);
                    return;
                }
                httpResponse.sendRedirect(safeUrl);
            }
        }
    }

    /**
     * 判断当前登录uri是否在Excluded列表中
     *
     * @param requestUri
     * @return
     */
    @Override
    protected boolean initParamContextIsExcluded(String requestUri) {

        setSsoExcludes(dingtalkSsoExcludes);

        /**
         * 优先使用自定义的
         */
        if (compiledPatterns != null && compiledPatterns.length > 0) {
            boolean isLoginExcept =  RegexUtil.isMatched(compiledPatterns, requestUri);
            return isLoginExcept;
        }
        return super.initParamContextIsExcluded(requestUri);
    }

    public void setSsoExcludes(String ssoExcludes) {
        this.ssoExcludes = ssoExcludes;
        if (StringUtils.isNotBlank(this.ssoExcludes)) {
            compiledPatterns = RegexUtil.compilePatterns(this.ssoExcludes.split(","));
        }
    }

    @Override
    public void destroy() {
        // 清理资源
    }

    @Override
    protected boolean afterLogin(HttpServletRequest httpRequest, HttpServletResponse httpResponse, FilterChain filterChain) throws IOException, ServletException {
        return true;
    }
}
