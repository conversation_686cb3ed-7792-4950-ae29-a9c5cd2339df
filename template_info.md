# 源码自动生成模板 pandora-boot-initializr

### 概述

* 模板: pandora-boot-initializr
* 模板使用时间: 2025-05-08 17:47:06

### Docker
* Image: reg.docker.alibaba-inc.com/bootstrap2/pandora-boot
* Tag: 0.2
* SHA256: eb7881697d25310df646b4a2a4cf9bc53392274e2812eca4a8dffa289654c95c

### 用户输入参数
* bootVersion: "2.7.18" 
* date: "Thu 
* May: May 
* 8: 8 
* 2025": 2025" 
* repoUrl: "**************************:ai-innovation/smart-processor.git" 
* ownerFullName: "沉寒(86172)" 
* javaVersion: "11" 
* appName: "smart-processor" 
* groupId: "com.alibaba.smart.processor" 
* dockerfile: "dockerfile" 
* sarVersion: "2024-12-release-fix2" 
* operator: "86172" 
* objectType: "Application" 
* ownerEmail: "<EMAIL>" 
* appId: "279826" 
* objectSubtype: "NORMAL" 
* artifactId: "processor" 
* style: "hsf,tddl,diamond,eagleeye" 
* applicationName: "smart-processor" 

### 上下文参数
* appName: smart-processor
* operator: 86172
* gitUrl: **************************:ai-innovation/smart-processor.git
* branch: master


### 命令行
	sudo docker run --rm -v /home/<USER>/5_20250508174657227_558042892_code/smart-processor/1746697616851_smart-processor:/workspace -e bootVersion="2.7.18" -e date="Thu May 8 2025" -e repoUrl="**************************:ai-innovation/smart-processor.git" -e ownerFullName="沉寒(86172)" -e javaVersion="11" -e appName="smart-processor" -e groupId="com.alibaba.smart.processor" -e dockerfile="dockerfile" -e sarVersion="2024-12-release-fix2" -e operator="86172" -e objectType="Application" -e ownerEmail="<EMAIL>" -e appId="279826" -e objectSubtype="NORMAL" -e artifactId="processor" -e style="hsf,tddl,diamond,eagleeye" -e applicationName="smart-processor"  reg.docker.alibaba-inc.com/bootstrap2/pandora-boot:0.2

