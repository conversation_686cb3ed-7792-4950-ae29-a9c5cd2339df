<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.alibaba.smart.processor</groupId>
		<artifactId>processor</artifactId>
		<version>1.0.0</version>
		<relativePath>../pom.xml</relativePath>
	</parent>
	<artifactId>processor-framework</artifactId>
	<packaging>jar</packaging>
	<name>smart-processor-framework</name>

	<dependencies>
		<dependency>
			<groupId>com.alibaba.boot</groupId>
			<artifactId>pandora-diamond-spring-boot-starter</artifactId>
		</dependency>
		<dependency>
			<groupId>org.apache.commons</groupId>
			<artifactId>commons-lang3</artifactId>
		</dependency>
		<dependency>
			<groupId>com.alibaba</groupId>
			<artifactId>fastjson</artifactId>
		</dependency>
		<dependency>
			<groupId>com.aliyun.oss</groupId>
			<artifactId>aliyun-sdk-oss</artifactId>
		</dependency>

		<dependency>
			<groupId>com.google.guava</groupId>
			<artifactId>guava</artifactId>
		</dependency>
		<dependency>
			<groupId>com.alibaba.security</groupId>
			<artifactId>security-all</artifactId>
		</dependency>

		<dependency>
			<groupId>com.laiwang</groupId>
			<artifactId>lippi-org.api</artifactId>
		</dependency>

		<dependency>
			<groupId>org.apache.httpcomponents.client5</groupId>
			<artifactId>httpclient5</artifactId>
			<version>5.2.1</version>
		</dependency>
		<dependency>
			<groupId>com.github.ben-manes.caffeine</groupId>
			<artifactId>caffeine</artifactId>
		</dependency>
	</dependencies>

</project>
