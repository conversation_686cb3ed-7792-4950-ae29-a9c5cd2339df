package com.alibaba.smart.processor.diamond;

import com.taobao.diamond.client.Diamond;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Service("diamondListener")
public class DiamondListener implements InitializingBean {
    @Value("${spring.diamond.group-id:smart-processor}")
    private String diamondGroupId;

    @Override
    public void afterPropertiesSet() throws Exception {
        DiamondEnums[] allConfigs = DiamondEnums.values();
        for (DiamondEnums config : allConfigs) {
            Diamond.addListener(config.getDataId(), diamondGroupId, config);
        }
    }
}
