package com.alibaba.smart.processor.exception;

import com.alibaba.smart.processor.exception.util.ExceptionPropertiesUtil;

import java.text.MessageFormat;
import java.util.Locale;

public class AppException extends RuntimeException {
    private String errorCode;
    private String errorMessage;
    private ExceptionContext exceptionContext;

    protected AppException() {
    }

    protected AppException(ExceptionContext exceptionContext, String errorCode, Object... args) {
        super(exceptionContext.getExceptionMessage(), exceptionContext.getCause());
        this.initErrorMsg(errorCode, exceptionContext, args);
    }

    protected AppException(ExceptionContext exceptionContext, Locale locale, String errorCode, Object... args) {
        super(exceptionContext.getExceptionMessage(), exceptionContext.getCause());
        this.initErrorMsg(errorCode, locale, exceptionContext, args);
    }

    protected AppException(ExceptionContext exceptionContext, String errorCode, Throwable e, Locale locale, Object... args) {
        super(exceptionContext.getExceptionMessage(), e);
        this.initErrorMsg(errorCode, locale, exceptionContext, args);
    }

    private void initErrorMsg(String errorCode, ExceptionContext exceptionContext, Object... args) {
        this.errorCode = errorCode;
        this.exceptionContext = exceptionContext;
        this.errorMessage = ExceptionPropertiesUtil.getString(errorCode);
        if (null != args && args.length > 0) {
            this.errorMessage = MessageFormat.format(this.errorMessage, args);
        }

    }

    private void initErrorMsg(String errorCode, Locale locale, ExceptionContext exceptionContext, Object... args) {
        this.errorCode = errorCode;
        this.exceptionContext = exceptionContext;
        this.errorMessage = ExceptionPropertiesUtil.getString(errorCode, locale);
        if (null != args && args.length > 0) {
            this.errorMessage = MessageFormat.format(this.errorMessage, args);
        }

    }

    public String getErrorCode() {
        return this.errorCode;
    }

    public String getErrorMessage() {
        return this.errorMessage;
    }

    public ExceptionContext getExceptionContext() {
        return this.exceptionContext;
    }
}
