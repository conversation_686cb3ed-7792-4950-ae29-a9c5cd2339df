package com.alibaba.smart.processor.exception;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

public class ExceptionContext implements Serializable {
    private String exceptionMessage;
    private Throwable cause;
    private Map<Object, Object> context = new HashMap();

    public ExceptionContext() {
    }

    public ExceptionContext(String exceptionMessage) {
        this.exceptionMessage = exceptionMessage;
    }

    public ExceptionContext(String exceptionMessage, Throwable throwable) {
        this.exceptionMessage = exceptionMessage;
        this.cause = throwable;
    }

    public ExceptionContext(Throwable throwable) {
        this.cause = throwable;
    }

    public ExceptionContext putContext(Object key, Object value) {
        this.context.put(key, value);
        return this;
    }

    public Map<Object, Object> getContext() {
        return this.context;
    }

    public String getExceptionMessage() {
        return this.exceptionMessage;
    }

    public Throwable getCause() {
        return this.cause;
    }
}
