package com.alibaba.smart.processor.diamond;

import com.taobao.diamond.manager.ManagerListener;
import org.apache.commons.lang3.StringUtils;

import java.util.concurrent.Executor;

/**
 * <AUTHOR>
 */
public enum DiamondEnums implements ManagerListener {

    smart_processor_prompt_template("smart_processor_prompt_template", ""),

    model_app_key("model_app_key", ""),

    smart_processor_cdn_domain("smart_processor_cdn_domain","//dev.g.alicdn.com"),

    polymind_version_config("polymind_version_config","0.1.0"),

    smart_processor_manus_version("smart_processor_manus_version","0.0.3"),

    smart_processor_ali_yc_version("smart_processor_ali_yc_version","0.0.1"),

    smart_processor_react_polyfill_version("smart_processor_react_polyfill_version","0.0.1"),

    polymind_super_manager("polymind_super_manager",""),

    polymind_visit_corp_white_list("polymind_visit_corp_white_list",""),

    polymind_reasoning_model_config("polymind_reasoning_model_config",""),

    polymind_system_prompt_config("polymind_system_prompt_config",""),

    /**
     * 系统插件的配置
     */
    polymind_system_plugin_config("polymind_system_plugin_config",""),

    /**
     * http请求白名单，不在白名单中的url需要经过安全包检查ssrf
     */
    polymind_http_url_white_list("polymind_http_url_white_list","[]"),
    ;

    /***
     * 配置项key
     */
    private String dataId;

    /**
     * 配置项value
     */
    private String value;

    DiamondEnums(String dataId, String value) {
        this.dataId = dataId;
        this.value = value;
    }

    public String getDataId() {
        return dataId;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    @Override
    public Executor getExecutor() {
        return null;
    }

    @Override
    public void receiveConfigInfo(String configInfo) {
        if (StringUtils.isNotBlank(configInfo)) {
            this.value = configInfo;
        }
    }
}
