package com.alibaba.smart.processor.configuration;

import com.alibaba.smart.processor.context.SpringMasterdataContext;
import com.dingtalk.org.service.OrgEmpService;
import com.taobao.hsf.app.spring.util.annotation.HSFConsumer;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Component;

public class CorpUserHsfConfig {

    private OrgEmpService orgEmpService;

    public OrgEmpService getOrgEmpService() {
        if (orgEmpService == null) {
            orgEmpService = SpringMasterdataContext.getBean("dingOrgEmpService", OrgEmpService.class);
        }
        return orgEmpService;
    }
}
