package com.alibaba.smart.processor.exception.util;

import org.springframework.core.io.Resource;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.util.StringUtils;

import java.io.IOException;
import java.io.InputStream;
import java.io.UnsupportedEncodingException;
import java.util.*;

public class ResourceUtil {
    private static final String EXCEPTION_PROPERTIES = "classpath*:eframework/%s/eframework-%s-*%s.properties";
    private static final String EXCEPTION = "exception";
    private static final String MESSAGE = "message";
    private static PathMatchingResourcePatternResolver resolver = new PathMatchingResourcePatternResolver();
    private static final Locale defaultLanguage;
    private static Map<String, List<ResourceBundle>> pathPatternKeyMap;

    public ResourceUtil() {
    }

    public static void buildResourceBundle(String pathKey, String language) {
        try {
            String mapKey = pathKey + language;
            if (!pathPatternKeyMap.containsKey(mapKey)) {
                List<InputStream> resourceFiles = findClassPathExceptionResources(pathKey, language);
                List<ResourceBundle> resourceBundles = new ArrayList();

                for(InputStream is : resourceFiles) {
                    ResourceBundle res = new PropertyResourceBundle(is);
                    resourceBundles.add(res);
                }

                pathPatternKeyMap.put(mapKey, resourceBundles);
            }

        } catch (Exception e) {
            throw new RuntimeException("The properties-file<" + String.format("classpath*:eframework/%s/eframework-%s-*%s.properties", pathKey, pathKey, language) + "> load error!", e);
        }
    }

    private static List<InputStream> findClassPathExceptionResources(String pathKey, String language) throws IOException {
        List<InputStream> resourceFiles = new ArrayList();
        String locationPattern = String.format("classpath*:eframework/%s/eframework-%s-*%s.properties", pathKey, pathKey, getLocationPatternSuffix(language));
        Resource[] resources = resolver.getResources(locationPattern);
        if (null == resources || resources.length == 0) {
            resources = resolver.getResources(String.format("classpath*:eframework/%s/eframework-%s-*%s.properties", pathKey, pathKey, ""));
        }

        if (null != resources && resources.length > 0) {
            for(Resource resource : resources) {
                resourceFiles.add(resource.getInputStream());
            }
        }

        return resourceFiles;
    }

    public static String getExceptionString(String key, Locale locale) {
        String language = getLocaleLanguage(locale);
        buildResourceBundle("exception", language);

        for(ResourceBundle res : pathPatternKeyMap.get("exception" + language)) {
            return res.getString(key);
        }

        return String.format("在%s中找不到%s对应的值", String.format("classpath*:eframework/%s/eframework-%s-*%s.properties", "exception", "exception", getLocationPatternSuffix(getLocaleLanguage(locale))), key);
    }

    public static String getMessageString(String key, Locale locale) {
        String language = getLocaleLanguage(locale);
        buildResourceBundle("message", language);

        for(ResourceBundle res : pathPatternKeyMap.get("message" + language)) {
            try {
                String val = res.getString(key);
                val = new String(val.getBytes("ISO-8859-1"), "utf8");
                return val;
            } catch (MissingResourceException var6) {
            } catch (UnsupportedEncodingException var7) {
            }
        }

        return key;
    }

    private static String getLocaleLanguage(Locale locale) {
        return null != locale ? locale.toLanguageTag() : defaultLanguage.toLanguageTag();
    }

    private static String getLocationPatternSuffix(String language) {
        return !StringUtils.isEmpty(language) ? "-" + language : "";
    }

    public static void main(String[] arg) {
    }

    static {
        defaultLanguage = Locale.CHINA;
        pathPatternKeyMap = new HashMap();
    }
}
