package com.alibaba.smart.processor.monitor;



import java.io.Serializable;
import java.util.Map;

/**
 * 操作日志
 */
public class LogInfo implements Serializable {
    private String logType;

    private String operationType;
    private String operationName;
    private Map<String,Object> param;

    private String operationObjectId;

    private String invokeIp;

    private String referer;

    private String userAgent;

    private String errorMsg;

    public String getInvokeIp() {
        return invokeIp;
    }

    public void setInvokeIp(String invokeIp) {
        this.invokeIp = invokeIp;
    }

    public String getReferer() {
        return referer;
    }

    public void setReferer(String referer) {
        this.referer = referer;
    }

    public String getUserAgent() {
        return userAgent;
    }

    public void setUserAgent(String userAgent) {
        this.userAgent = userAgent;
    }

    public String getOperationObjectId() {
        return operationObjectId;
    }

    public void setOperationObjectId(String operationObject) {
        this.operationObjectId = operationObject;
    }

    public String getLogType() {
        return logType;
    }

    public void setLogType(String logType) {
        this.logType = logType;
    }

    public String getOperationType() {
        return operationType;
    }

    public void setOperationType(String operationType) {
        this.operationType = operationType;
    }

    public String getOperationName() {
        return operationName;
    }

    public void setOperationName(String operationName) {
        this.operationName = operationName;
    }

    public Map<String,Object> getParam() {
        return param;
    }

    public void setParam(Map<String,Object> param) {
        this.param = param;
    }

    public String getErrorMsg() {
        return errorMsg;
    }

    public void setErrorMsg(String errorMsg) {
        this.errorMsg = errorMsg;
    }
}
