package com.alibaba.smart.processor.exception;

import org.apache.commons.lang3.StringUtils;

import java.util.Locale;

public class BusinessException extends AppException {
    protected BusinessException() {
    }

    public BusinessException(ExceptionContext exceptionContext, String errorCode, Object... args) {
        super(exceptionContext,Locale.forLanguageTag("zh-CN"), errorCode, args);
    }

    public BusinessException(ExceptionContext exceptionContext, String errorCode, Throwable e, String locale, Object... args) {
        super(exceptionContext, errorCode, e, Locale.forLanguageTag(StringUtils.defaultString(locale,"zh-CN")), args);
    }
}
