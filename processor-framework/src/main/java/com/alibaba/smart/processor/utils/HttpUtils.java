package com.alibaba.smart.processor.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.security.SecurityRedirectStrategies;
import com.alibaba.security.SecurityUtil;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.http.Header;
import org.apache.http.HttpEntity;
import org.apache.http.NameValuePair;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.methods.HttpUriRequest;
import org.apache.http.conn.ssl.TrustStrategy;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.message.BasicHeader;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.ssl.SSLContextBuilder;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.net.ssl.SSLContext;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.alibaba.security.url.ssrf.http.SecurityRedirectStrategy.CheckMethod.CHECK_URL_WITHOUT_CONNECTION;

/**
 * <AUTHOR>
 */
public class HttpUtils {
    private static final Logger logger = LoggerFactory.getLogger(HttpUtils.class);

    private static CloseableHttpClient client = null;

    private static RequestConfig requestConfig = null;


    static {
        try {
            //设置http的状态参数
            requestConfig = RequestConfig.custom()
                    .setSocketTimeout(5000)
                    .setConnectTimeout(5000)
                    .setConnectionRequestTimeout(5000)
                    .build();


            HttpClientBuilder builder = HttpClients.custom().setRedirectStrategy(SecurityRedirectStrategies.getInstance(CHECK_URL_WITHOUT_CONNECTION));
            // 信任所有
            SSLContext sc = SSLContextBuilder.create().loadTrustMaterial((TrustStrategy) (chain, authType) -> true).build();
            builder.setSSLContext(sc);// 忽略证书验证
            builder.evictExpiredConnections();// 自动释放一段时间不活跃的连接，默认10秒
            client = builder.build();// 默认每个主机2个连接，最多20个连接
        } catch (Exception e) {
            logger.error("init HttpUtils properties exception", e);
        }
    }


    /**
     * post请求
     *
     * @param url
     * @param params
     * @param charset
     * @return
     */
    public static String post(String url, Map<String, String> params, Map<String, String> header, String charset) {
        String result = "";
        if (!SecurityUtil.checkSSRFWithoutConnection(url, true)) {
            logger.error("存在SSRF风险, url = {}", url);
            return result;
        }
        CloseableHttpResponse response = null;
        try {
            HttpPost httpPost = new HttpPost(url);
            httpPost.setEntity(new UrlEncodedFormEntity(buildNameValuePairs(params), charset));
            httpPost.setHeaders(buildHeaders(header).toArray(new Header[0]));
            httpPost.setConfig(requestConfig);
            response = client.execute(httpPost);
            if (response.getStatusLine().getStatusCode() != 200) {
                logger.error("http post fail, url={}, params={}, header={}, charset={}, response={}", url, JSON.toJSONString(params), JSON.toJSONString(header), charset, JSON.toJSONString(response));
                return result;
            }
            HttpEntity entity = response.getEntity();
            result = EntityUtils.toString(entity, charset);
        } catch (Exception e) {
            logger.error("post exception, url={}, params={}, header={}, charset={}", url, JSON.toJSONString(params), JSON.toJSONString(header), charset, e);
        } finally {
            try {
                if (response != null) {
                    response.close();
                }
            } catch (IOException e) {
                logger.error("response close exception, url={}, params={}, header={}, charset={}", url, JSON.toJSONString(params), JSON.toJSONString(header), charset, e);
            }
        }
        return result;
    }


    /**
     * post json请求
     *
     * @param url
     * @param json
     * @param charset
     * @return
     */
    public static String postJson(String url, String json, Map<String, String> header, String charset) {
        String result = "";
        if (!SecurityUtil.checkSSRFWithoutConnection(url, true)) {
            logger.error("存在SSRF风险, url = {}", url);
            return result;
        }
        CloseableHttpResponse response = null;
        try {
            HttpPost httpPost = new HttpPost(url);
            httpPost.setEntity(new StringEntity(json, ContentType.APPLICATION_JSON));
            httpPost.setHeaders(buildHeaders(header).toArray(new Header[0]));
            httpPost.setConfig(requestConfig);
            response = client.execute(httpPost);
            if (response.getStatusLine().getStatusCode() != 200) {
                logger.error("http post fail, url={}, json={}, header={}, charset={}, response={}", url, json, JSON.toJSONString(header), charset, JSON.toJSONString(response));
                return result;
            }
            HttpEntity entity = response.getEntity();
            result = EntityUtils.toString(entity, charset);
        } catch (Exception e) {
            logger.error("postJson exception, url={}, json={}, charset={}", url, json, charset, e);
        } finally {
            try {
                if (response != null) {
                    response.close();
                }
            } catch (IOException e) {
                logger.error("response close exception, url={}, json={}, header={}, charset={}, response={}", url, json, JSON.toJSONString(header), charset, JSON.toJSONString(response));
            }
        }
        return result;
    }

    /**
     * 使用GET方法向指定的URL传送数据
     *
     * @param url
     * @return
     */
    public static String get(String url, Map<String, String> header) {
        String result = "";
        if (!SecurityUtil.checkSSRFWithoutConnection(url, true)) {
            logger.error("存在SSRF风险, url = {}", url);
            return result;
        }
        try {
            HttpGet httpGet = new HttpGet(url);
            httpGet.setConfig(requestConfig);
            httpGet.setHeaders(buildHeaders(header).toArray(new Header[0]));
            CloseableHttpResponse response = client.execute(httpGet);
            if (response.getStatusLine().getStatusCode() != 200) {
                logger.error("http get fail, url={}, header={}, response={}", url, JSON.toJSONString(header), JSON.toJSONString(response));
                return result;
            }
            HttpEntity entity = response.getEntity();
            result = EntityUtils.toString(entity, "utf-8");
        } catch (Exception e) {
            logger.error("get exception, url={}", url, e);
        }
        return result;
    }

    public static List<Header> buildHeaders(Map<String, String> headerMap) {
        if (MapUtils.isEmpty(headerMap)) {
            return new ArrayList<>();
        }
        return headerMap.entrySet().stream().filter(entry -> StringUtils.isNotBlank(entry.getKey()) && StringUtils.isNotBlank(entry.getValue())).map(entry -> new BasicHeader(entry.getKey(), entry.getValue())).collect(Collectors.toList());
    }

    public static List<NameValuePair> buildNameValuePairs(Map<String, String> params) {
        if (MapUtils.isEmpty(params)) {
            return new ArrayList<>();
        }
        return params.entrySet().stream().filter(entry -> StringUtils.isNotBlank(entry.getKey()) && StringUtils.isNotBlank(entry.getValue())).map(entry -> new BasicNameValuePair(entry.getKey(), entry.getValue())).collect(Collectors.toList());
    }

    public static boolean isURLReachable(String urlString, String method) {
        if (StringUtils.isBlank(urlString) || StringUtils.isBlank(method)) {
            return true;
        }
        if (!SecurityUtil.checkSSRFWithoutConnection(urlString, true)) {
            logger.error("存在SSRF风险, url = {}", urlString);
            return false;
        }
        boolean reachable = false;

        try {
            HttpUriRequest httpUriRequest = null;
            if (method.equalsIgnoreCase("get")) {
                httpUriRequest = new HttpGet(urlString);

            } else {
                httpUriRequest = new HttpPost(urlString);
            }
            try (CloseableHttpResponse response = client.execute(httpUriRequest)) {
                int statusCode = response.getStatusLine().getStatusCode();
                if (statusCode >= 200 && statusCode < 300) {
                    reachable = true;
                }
            }
        } catch (Exception e) {
            logger.error("isURLReachable Exception urlString = {}, method = {}", urlString, method, e);
            return true;
        }
        return reachable;
    }
}



