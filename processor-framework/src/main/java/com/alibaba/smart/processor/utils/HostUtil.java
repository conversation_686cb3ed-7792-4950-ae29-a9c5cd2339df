package com.alibaba.smart.processor.utils;

import java.net.Inet4Address;
import java.net.InetAddress;
import java.net.NetworkInterface;
import java.net.SocketException;
import java.util.Enumeration;
import java.util.Properties;

public class HostUtil {
    public static String getLocalIP(){
        if (isWinOS()) {
            try {
                String hostName = InetAddress.getLocalHost().getHostName();
                return InetAddress.getByName(hostName).getHostAddress();
            } catch (Exception e) {
                return null;
            }
        } else {
            try {
                Enumeration<NetworkInterface> allNetInterfaces;
                allNetInterfaces = NetworkInterface.getNetworkInterfaces();
                InetAddress ip = null;
                while (allNetInterfaces.hasMoreElements()) {
                    NetworkInterface netInterface = allNetInterfaces.nextElement();
                    Enumeration<InetAddress> addresses = netInterface.getInetAddresses();
                    while (addresses.hasMoreElements()) {
                        ip = addresses.nextElement();
                        if (ip != null && ip instanceof Inet4Address) {
                            return ip.getHostAddress();
                        }
                    }
                }
            } catch (SocketException e) {
                return null;
            }
        }
        return null;
    }


    private static String getOS() {
        Properties prop = System.getProperties();
        String os = prop.getProperty("os.name");
        return os;
    }

    private static boolean isWinOS() {
        return getOS().toLowerCase().contains("win");
    }
}
