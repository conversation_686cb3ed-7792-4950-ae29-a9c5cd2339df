package com.alibaba.smart.processor.monitor;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSONPath;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.alibaba.smart.processor.exception.BusinessException;
import com.alibaba.smart.processor.utils.YNConstant;
import com.taobao.hsf.exception.HSFTimeOutException;
import com.taobao.middleware.logger.Level;
import com.taobao.middleware.logger.Logger;
import com.taobao.middleware.logger.LoggerFactory;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import java.util.*;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

public class MonitorLoggerUtils {
    private static String SEPARATOR = "ξξ";
    private volatile static Logger monitorLogger;
    private volatile static Logger errorLogger;
    private volatile static Logger contextLogger;
    private volatile static Logger businessLogger;

    /**
     * 编码字符集
     */
    private static final String ENCODE = "UTF-8";

    /**
     * 分片的日志文件大小
     * 目前monitor.log、error.log、argument.log的参数均设置为该参数值
     */
    private static final String LOG_FILE_SIZE = "500MB";

    /**
     * 日志分片时间格式
     * <AUTHOR>
     */
    private static final String DATE_PATTERN = "yyyy-MM-dd";

    /**
     * 最大备份文件的序号
     * 0时表示直接截断，不备份；如果为2，则备份文件名依次为：notify.log.1， notify.log.2
     */
    private static final int MAX_BACKUP_INDEX = 3;

    private static Logger getMonitorLogger() {
        if (monitorLogger == null) {
            synchronized (MonitorLoggerUtils.class) {
                if (monitorLogger == null) {
                    monitorLogger = LoggerFactory.getLogger("monitorLogger");
                    monitorLogger.setLevel(Level.INFO);
                    monitorLogger.activateAppenderWithTimeAndSizeRolling("business-monitor", "monitor.log",
                            ENCODE, LOG_FILE_SIZE, DATE_PATTERN, MAX_BACKUP_INDEX);
                    monitorLogger.setAdditivity(false);
                }
            }
        }
        return monitorLogger;
    }

    private static Logger getErrorLogger() {
        if (errorLogger == null) {
            synchronized (MonitorLoggerUtils.class) {
                if (errorLogger == null) {
                    errorLogger = LoggerFactory.getLogger("errorLogger");
                    errorLogger.setLevel(Level.ERROR);
                    errorLogger.activateAppenderWithTimeAndSizeRolling("business-monitor", "error.log",
                            ENCODE, LOG_FILE_SIZE, DATE_PATTERN, MAX_BACKUP_INDEX);
                    errorLogger.setAdditivity(false);
                }
            }
        }
        return errorLogger;
    }

    private static Logger getContextLogger() {
        if (contextLogger == null) {
            synchronized (MonitorLoggerUtils.class) {
                if (contextLogger == null) {
                    contextLogger = LoggerFactory.getLogger("contextLogger");
                    contextLogger.setLevel(Level.INFO);
                    contextLogger.activateAppenderWithSizeRolling("business-monitor", "context.log",
                            ENCODE, LOG_FILE_SIZE, 0);
                    contextLogger.setAdditivity(false);
                }
            }
        }
        return contextLogger;
    }

    private static Logger getBusinessLogger() {
        if (businessLogger == null) {
            synchronized (MonitorLoggerUtils.class) {
                if (businessLogger == null) {
                    businessLogger = LoggerFactory.getLogger("businessLogger");
                    businessLogger.setLevel(Level.INFO);
                    businessLogger.activateAppenderWithSizeRolling("business-monitor", "business.log",
                            ENCODE, LOG_FILE_SIZE, 0);
                    businessLogger.setAdditivity(false);
                }
            }
        }
        return businessLogger;
    }

    private static final int CORE_POLL_SIZE = 5;
    private static final int MAX_POLL_SIZE = 20;
    private static final int KEEP_ALIVE = 5;

    // 日志处理线程池
    private static ThreadPoolExecutor threadPool = new ThreadPoolExecutor(
            CORE_POLL_SIZE,
            MAX_POLL_SIZE,
            KEEP_ALIVE,
            TimeUnit.SECONDS,
            new LinkedBlockingQueue<>(1024),
            new ThreadPoolExecutor.DiscardPolicy());


    public static MonitorData buildMonitorData(MonitorData monitorData,LogInfo logInfo){
        if(logInfo != null){
            monitorData.setLogType(logInfo.getLogType());
            monitorData.setOperationType(logInfo.getOperationType());
            monitorData.setOperationName(logInfo.getOperationName());
            monitorData.setJsonContext(JSONObject.toJSONString(logInfo.getParam()));
            monitorData.setOperationObjectId(logInfo.getOperationObjectId());
            monitorData.setSummary("");
        }
        return monitorData;
    }

    public static MonitorData buildMonitorDataWithRequestInfo(MonitorData monitorData,LogInfo logInfo){
        if(logInfo != null){
            monitorData.setInvokeIp(logInfo.getInvokeIp());
            monitorData.setErrorMsg(logInfo.getErrorMsg());
            monitorData.setReferer(logInfo.getReferer());
            monitorData.setUserAgent(logInfo.getUserAgent());
            monitorData.setLogType(logInfo.getLogType());
            monitorData.setOperationType(logInfo.getOperationType());
            monitorData.setOperationName(logInfo.getOperationName());
            monitorData.setJsonContext(JSONObject.toJSONString(logInfo.getParam()));
            monitorData.setOperationObjectId(logInfo.getOperationObjectId());
            monitorData.setSummary("");
        }
        return monitorData;
    }
    public static void log(MonitorData monitorModel, Object argument, Object result){
        threadPool.submit(new AsyncLogMonitorInfoTask(monitorModel, argument, result));
    }

    public static void logErrorOnly(MonitorData monitorModel, Object argument, Object result) {
        threadPool.submit(new LogMonitorErrorOnlyTask(monitorModel, argument, result));
    }

    /**
     * 只输出error日志的task
     */
    private static class LogMonitorErrorOnlyTask extends AsyncLogMonitorInfoTask{

        public LogMonitorErrorOnlyTask(MonitorData monitorData, Object argument, Object result) {
            super(monitorData, argument, result);
        }

        @Override
        public void run() {
            try{
                super.writeAllException();
            }catch (Exception ignored){
            }
        }
    }

    private static class AsyncLogMonitorInfoTask implements Runnable{
        private MonitorData monitorModel;
        private Object argument;
        private Object result;

        public AsyncLogMonitorInfoTask(MonitorData monitorData, Object argument, Object result) {
            this.monitorModel = monitorData;
            this.argument = argument;
            this.result = result;
        }

        @Override
        public void run() {
            try{
                writeMonitor();
                writeAllException();
                writeContext();
            }catch (Exception ignored){

            }
        }

        /**
         * 记录异常日志，根据是否业务异常，分别写入 monitor-error 或 monitor-business 中
         */
        private void writeAllException() {
            if (monitorModel.getException() != null) {
                // 记录异常日志，包括错误码、错误信息和堆栈
                writeException();
            }
        }

        private void writeException() {
            if(monitorModel.getException() != null){
                Throwable exception = monitorModel.getException();
                String exceptionName = exception.getClass().getSimpleName();
                String errorCode = "";
                if(exception instanceof BusinessException){
                    errorCode = ((BusinessException)exception).getErrorCode();
                } else if (exception instanceof HSFTimeOutException) {
                    errorCode = "400004";
                } else if (exception.getClass().getSimpleName().equals("MyBatisSystemException")) {
                    errorCode = "400005";
                }else if (exception instanceof NullPointerException) {
                    errorCode = "400007";
                }

                getErrorLogger().error(monitorModel.getTraceId(),  errorCode+ SEPARATOR + exceptionName, getMonitorInfo(), monitorModel.getException());
            }
        }

        private void writeContext(){
            getContextLogger().info(monitorModel.getTraceId(), getMonitorInfo() + JSON.toJSONString(argument, SerializerFeature.IgnoreErrorGetter) + SEPARATOR + JSON.toJSONString(result, SerializerFeature.IgnoreErrorGetter));
        }

        private void writeMonitor() {
            getMonitorLogger().info(getMonitorInfoWithCorpLog());
        }

        private String getMonitorInfo(){
            String exceptionName = "";
            String errorCode = "";
            if(monitorModel.getException() != null){
                Throwable exception = monitorModel.getException();
                exceptionName = exception.getClass().getSimpleName();
                if(exception instanceof BusinessException){
                    errorCode = ((BusinessException)exception).getErrorCode();
                }
            }
            StringBuilder builder = new StringBuilder();
            builder.append(SEPARATOR);

            if (StringUtils.isNotBlank(monitorModel.getUserId())){
                builder.append(monitorModel.getUserId());
            }
            builder.append(SEPARATOR);

            builder.append(monitorModel.getDomain());
            builder.append(SEPARATOR);

            builder.append(monitorModel.getChildrenType());
            builder.append(SEPARATOR);

            builder.append(monitorModel.getName());
            builder.append(SEPARATOR);

            builder.append(monitorModel.getBusinessSuccessMark());
            builder.append(SEPARATOR);

            builder.append(monitorModel.getSuccessMark());
            builder.append(SEPARATOR);

            builder.append(monitorModel.getElapseTime());
            builder.append(SEPARATOR);

            builder.append(exceptionName);
            builder.append(SEPARATOR);

            builder.append(errorCode);
            builder.append(SEPARATOR);

            builder.append(monitorModel.getTraceId());
            builder.append(SEPARATOR);

            builder.append(monitorModel.getLocalIPAddr());
            builder.append(SEPARATOR);

            builder.append(monitorModel.getAppName());
            builder.append(SEPARATOR);

            if(CollectionUtils.isNotEmpty(monitorModel.getBizExtFields())){
                for(String bizExtField : monitorModel.getBizExtFields()){
                    builder.append(bizExtField);
                    builder.append(SEPARATOR);
                }
            }
            builder.append(monitorModel.getPaid());
            builder.append(SEPARATOR);

            builder.append(monitorModel.getEnv());
            builder.append(SEPARATOR);
            return builder.toString();
        }

        private String getMonitorInfoWithCorpLog(){
            String monitorInfo = getMonitorInfo();
            LogInfo logInfo = buildLogInfoForCorpLog();
            if (logInfo != null){
                buildMonitorData(monitorModel,logInfo);
            }

            if (StringUtils.equalsIgnoreCase(monitorModel.getSuccessMark(), YNConstant.Y.getKey())
                    && StringUtils.equalsIgnoreCase(monitorModel.getBusinessSuccessMark(),YNConstant.Y.getKey())
                    && StringUtils.isNotBlank(monitorModel.getLogType())
                    && StringUtils.isNotBlank(monitorModel.getOperationType())){
                StringBuilder builder = new StringBuilder(monitorInfo);
                builder.append(monitorModel.getInvokeIp());
                builder.append(SEPARATOR);
                builder.append(monitorModel.getReferer());
                builder.append(SEPARATOR);
                builder.append(monitorModel.getOperationName());
                builder.append(SEPARATOR);
                builder.append(monitorModel.getLogType());
                builder.append(SEPARATOR);
                builder.append(monitorModel.getOperationType());
                builder.append(SEPARATOR);
                if (StringUtils.isNotBlank(monitorModel.getUserAgent())){
                    builder.append(monitorModel.getUserAgent());
                }
                builder.append(SEPARATOR);
                builder.append(monitorModel.getErrorMsg());
                builder.append(SEPARATOR);
                builder.append(monitorModel.getSummary());
                builder.append(SEPARATOR);
                builder.append(monitorModel.getJsonContext());
                builder.append(SEPARATOR);
                if (StringUtils.isNotBlank(monitorModel.getOperationObjectId())){
                    builder.append(monitorModel.getOperationObjectId());
                }
                builder.append(SEPARATOR);
                monitorModel.setLogConfig(null);
                return builder.toString();
            }else{
                return monitorInfo;
            }
        }
        private LogInfo buildLogInfoForCorpLog(){
            String value = monitorModel.getLogConfig();
            if (StringUtils.isNotBlank(value)) {
                JSONObject logConfigs = JSONObject.parseObject(value);
                for (String key : logConfigs.keySet()) {
                    if (monitorModel.getChildrenType().contains(key)) {
                        JSONObject logConfig = logConfigs.getJSONObject(key);
                        String operationName = logConfig.getString("operationName");
                        if (operationName == null) {
                            return null;
                        }
                        String jsonStr = JSONObject.toJSONString(argument);

                        String logType = logConfig.getString("logType");
                        String operationType = logConfig.getString("operationType");
                        if (StringUtils.isBlank(logType) || StringUtils.isBlank(operationType)) {
                            return null;
                        }
                        String paramStr = logConfig.getString("paramName");
                        List<String> paramNamePaths = new ArrayList<>();
                        if (StringUtils.isNotBlank(paramStr)) {
                            paramNamePaths = Arrays.asList(paramStr.split(","));
                        }
                        List<ParamObject> paramObjects = new ArrayList<>();
                        for (String paramNamePath : paramNamePaths) {
                            String[] NamePath = paramNamePath.split(":");
                            if (NamePath != null && NamePath.length == 2){
                                ParamObject paramObject = new ParamObject(NamePath[0], NamePath[1]);
                                paramObjects.add(paramObject);
                            }
                        }
                        String operationObjectPath = logConfig.getString("operationObjectId");
                        Map<String, Object> param = null;
                        String operationObject = "";
                        if (paramObjects != null && paramObjects.size() > 0) {
                            param = new HashMap<>();
                            for (ParamObject paramObject : paramObjects) {
                                Object paramValue = JSONPath.read(jsonStr, paramObject.getParamPath());
                                param.put(paramObject.getParamName(), paramValue);
                            }
                        }
                        if (StringUtils.isNotBlank(operationObjectPath)) {
                            operationObject = (String) JSONPath.read(jsonStr, operationObjectPath);
                        }
                        LogInfo logInfo = new LogInfo();
                        logInfo.setLogType(logType);
                        logInfo.setOperationType(operationType);
                        logInfo.setOperationName(operationName);
                        logInfo.setParam(param);
                        logInfo.setOperationObjectId(operationObject);
                        return logInfo;
                    }
                }
            }
            return null;
        }

        static class ParamObject{
            private String paramName;
            private String paramPath;

            public ParamObject(){}
            public ParamObject(String paramName, String paramPath) {
                this.paramName = paramName;
                this.paramPath = paramPath;
            }

            public String getParamName() {
                return paramName;
            }

            public void setParamName(String paramName) {
                this.paramName = paramName;
            }

            public String getParamPath() {
                return paramPath;
            }

            public void setParamPath(String paramPath) {
                this.paramPath = paramPath;
            }
        }
    }
}
