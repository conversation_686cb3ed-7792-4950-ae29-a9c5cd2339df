package com.alibaba.smart.processor.context;

/**
 * <AUTHOR>
 */
public class UserContextUtil {
    public static Long uid() {
        LoginUser loginUser = UserHolderContext.getLoginUser();
        if (loginUser != null) {
            return loginUser.getUid();
        }
        return null;
    }

    public static String userId() {
        LoginUser loginUser = UserHolderContext.getLoginUser();
        if (loginUser != null) {
            return loginUser.getUserId();
        }
        return null;
    }
    public static String corpId() {
        LoginUser loginUser = UserHolderContext.getLoginUser();
        if (loginUser != null) {
            return loginUser.getCorpId();
        }
        return null;
    }
}
