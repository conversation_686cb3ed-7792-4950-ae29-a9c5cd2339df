package com.alibaba.smart.processor.utils;

import java.io.ByteArrayOutputStream;
import java.io.DataOutputStream;
import java.lang.reflect.Method;
import java.net.InetAddress;
import java.net.NetworkInterface;
import java.security.SecureRandom;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.regex.Pattern;

public class UUID {
    private static final char[] DIGITS = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz".toCharArray();
    private static final char[] DIGITS_NOCASE = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ".toCharArray();
    private static final Pattern CRLF_PATTERN = Pattern.compile("\\r|\\n|\\r\\n");
    private boolean noCase;
    private String instanceId;
    private AtomicInteger counter;

    public UUID() {
        this(true);
    }

    public UUID(boolean noCase) {
        byte[] machineId = getLocalHostAddress();
        byte[] jvmId = this.getRandomizedTime();
        this.instanceId = bytesToString(machineId, noCase) + "-" + bytesToString(jvmId, noCase);
        this.counter = new AtomicInteger();
        this.noCase = noCase;
    }

    private static byte[] getLocalHostAddress() {
        Method getHardwareAddress;
        try {
            getHardwareAddress = NetworkInterface.class.getMethod("getHardwareAddress");
        } catch (Exception var4) {
            getHardwareAddress = null;
        }

        byte[] addr;
        try {
            InetAddress localHost = InetAddress.getLocalHost();
            if (getHardwareAddress != null) {
                addr = (byte[])((byte[])getHardwareAddress.invoke(NetworkInterface.getByInetAddress(localHost)));
            } else {
                addr = localHost.getAddress();
            }
        } catch (Exception var3) {
            addr = null;
        }

        if (addr == null) {
            addr = new byte[]{127, 0, 0, 1};
        }

        return addr;
    }

    private byte[] getRandomizedTime() {
        long jvmId = System.currentTimeMillis();
        long random = (new SecureRandom()).nextLong();
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        DataOutputStream dos = new DataOutputStream(baos);

        try {
            dos.writeLong(jvmId);
            dos.writeLong(random);
        } catch (Exception var8) {
        }

        return baos.toByteArray();
    }

    public String nextID() {
        return this.instanceId + "-" + longToString(System.currentTimeMillis(), this.noCase) + "-" + longToString((long)this.counter.getAndIncrement(), this.noCase);
    }

    public static String longToString(long longValue, boolean noCase) {
        char[] digits = noCase ? DIGITS_NOCASE : DIGITS;
        int digitsLength = digits.length;
        if (longValue == 0L) {
            return String.valueOf(digits[0]);
        } else {
            if (longValue < 0L) {
                longValue = -longValue;
            }

            StringBuilder strValue = new StringBuilder();

            while(longValue != 0L) {
                int digit = (int)(longValue % (long)digitsLength);
                longValue /= (long)digitsLength;
                strValue.append(digits[digit]);
            }

            return strValue.toString();
        }
    }

    public static String bytesToString(byte[] bytes, boolean noCase) {
        char[] digits = noCase ? DIGITS_NOCASE : DIGITS;
        int digitsLength = digits.length;
        if (Objects.isNull(bytes) || bytes.length ==0) {
            return String.valueOf(digits[0]);
        } else {
            StringBuilder strValue = new StringBuilder();
            int value = 0;
            int limit = 8388607;
            int i = 0;

            while(true) {
                while(i >= bytes.length || value >= limit) {
                    while(value >= digitsLength) {
                        strValue.append(digits[value % digitsLength]);
                        value /= digitsLength;
                    }

                    if (i >= bytes.length) {
                        if (value != 0 || strValue.length() == 0) {
                            strValue.append(digits[value]);
                        }

                        return strValue.toString();
                    }
                }

                value = (value << 8) + (255 & bytes[i++]);
            }
        }
    }
}
