package com.alibaba.smart.processor.monitor;

import java.util.List;

public class MonitorData {
    /**
     * 用户ID
     */
    private String userId;

    /**
     * 业务模块
     */
    private String domain;

    /**
     * 业务子分类
     */
    private String childrenType;

    /**
     *  方法名
     */
    private String name;

    /**
     * 业务上是否成功
     */
    private String businessSuccessMark;

    /**
     * 是否成功
     */
    private String successMark;

    /**
     * 耗时
     */
    private long elapseTime;

    /**
     * 异常类
     */
    private Throwable exception;

    /**
     * 鹰眼ID
     */
    private String traceId;

    /**
     * 本地IP地址
     */
    private String localIPAddr;

    /**
     * 业务扩展字段
     */
    private List<String> bizExtFields;

    /**
     * 应用名
     */
    private String appName;

    /**
     * 环境标
     *
     */
    private String env;

    /**
     * 是否付费
     * @return
     */
    private String paid;

    private String invokeIp;

    private String referer;

    private String userAgent;

    private String operationName;

    private String operationType;

    private String logType;

    private String errorMsg;

    private String summary;

    private String jsonContext;

    private String operationObjectId;

    private String logConfig;

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getDomain() {
        return domain;
    }

    public void setDomain(String domain) {
        this.domain = domain;
    }

    public String getChildrenType() {
        return childrenType;
    }

    public void setChildrenType(String childrenType) {
        this.childrenType = childrenType;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getBusinessSuccessMark() {
        return businessSuccessMark;
    }

    public void setBusinessSuccessMark(String businessSuccessMark) {
        this.businessSuccessMark = businessSuccessMark;
    }

    public String getSuccessMark() {
        return successMark;
    }

    public void setSuccessMark(String successMark) {
        this.successMark = successMark;
    }

    public long getElapseTime() {
        return elapseTime;
    }

    public void setElapseTime(long elapseTime) {
        this.elapseTime = elapseTime;
    }

    public Throwable getException() {
        return exception;
    }

    public void setException(Throwable exception) {
        this.exception = exception;
    }

    public String getTraceId() {
        return traceId;
    }

    public void setTraceId(String traceId) {
        this.traceId = traceId;
    }

    public String getLocalIPAddr() {
        return localIPAddr;
    }

    public void setLocalIPAddr(String localIPAddr) {
        this.localIPAddr = localIPAddr;
    }

    public List<String> getBizExtFields() {
        return bizExtFields;
    }

    public void setBizExtFields(List<String> bizExtFields) {
        this.bizExtFields = bizExtFields;
    }

    public String getAppName() {
        return appName;
    }

    public void setAppName(String appName) {
        this.appName = appName;
    }

    public String getEnv() {
        return env;
    }

    public void setEnv(String env) {
        this.env = env;
    }

    public String getPaid() {
        return paid;
    }

    public void setPaid(String paid) {
        this.paid = paid;
    }

    public String getInvokeIp() {
        return invokeIp;
    }

    public void setInvokeIp(String invokeIp) {
        this.invokeIp = invokeIp;
    }

    public String getReferer() {
        return referer;
    }

    public void setReferer(String referer) {
        this.referer = referer;
    }

    public String getUserAgent() {
        return userAgent;
    }

    public void setUserAgent(String userAgent) {
        this.userAgent = userAgent;
    }

    public String getOperationName() {
        return operationName;
    }

    public void setOperationName(String operationName) {
        this.operationName = operationName;
    }

    public String getOperationType() {
        return operationType;
    }

    public void setOperationType(String operationType) {
        this.operationType = operationType;
    }

    public String getLogType() {
        return logType;
    }

    public void setLogType(String logType) {
        this.logType = logType;
    }

    public String getErrorMsg() {
        return errorMsg;
    }

    public void setErrorMsg(String errorMsg) {
        this.errorMsg = errorMsg;
    }

    public String getSummary() {
        return summary;
    }

    public void setSummary(String summary) {
        this.summary = summary;
    }

    public String getJsonContext() {
        return jsonContext;
    }

    public void setJsonContext(String jsonContext) {
        this.jsonContext = jsonContext;
    }

    public String getOperationObjectId() {
        return operationObjectId;
    }

    public void setOperationObjectId(String operationObjectId) {
        this.operationObjectId = operationObjectId;
    }

    public String getLogConfig() {
        return logConfig;
    }

    public void setLogConfig(String logConfig) {
        this.logConfig = logConfig;
    }
}
