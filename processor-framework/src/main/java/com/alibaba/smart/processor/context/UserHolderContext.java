package com.alibaba.smart.processor.context;

public class UserHolderContext {
    private static ThreadLocal<LoginUser> userHolder = new ThreadLocal<LoginUser>();

    public static void setLoginUser(LoginUser loginUser) {
        userHolder.set(loginUser);
    }

    public static LoginUser getLoginUser() {
        return userHolder.get();
    }

    public static void removeThreadLocal() {
        userHolder.remove();
    }
}
