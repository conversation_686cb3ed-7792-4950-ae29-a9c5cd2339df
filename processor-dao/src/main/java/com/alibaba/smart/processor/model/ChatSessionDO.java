package com.alibaba.smart.processor.model;

import java.util.Date;

public class ChatSessionDO {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column smart_processor_chat_session.id
     *
     * @mbg.generated Tue May 20 15:31:42 CST 2025
     */
    private Long id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column smart_processor_chat_session.gmt_create
     *
     * @mbg.generated Tue May 20 15:31:42 CST 2025
     */
    private Date gmtCreate;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column smart_processor_chat_session.gmt_modified
     *
     * @mbg.generated Tue May 20 15:31:42 CST 2025
     */
    private Date gmtModified;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column smart_processor_chat_session.creator
     *
     * @mbg.generated Tue May 20 15:31:42 CST 2025
     */
    private String creator;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column smart_processor_chat_session.modifier
     *
     * @mbg.generated Tue May 20 15:31:42 CST 2025
     */
    private String modifier;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column smart_processor_chat_session.title
     *
     * @mbg.generated Tue May 20 15:31:42 CST 2025
     */
    private String title;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column smart_processor_chat_session.session_id
     *
     * @mbg.generated Tue May 20 15:31:42 CST 2025
     */
    private String sessionId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column smart_processor_chat_session.status
     *
     * @mbg.generated Tue May 20 15:31:42 CST 2025
     */
    private String status;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column smart_processor_chat_session.agent_id
     *
     * @mbg.generated Tue May 20 15:31:42 CST 2025
     */
    private String agentId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column smart_processor_chat_session.corp_id
     *
     * @mbg.generated Tue May 20 15:31:42 CST 2025
     */
    private String corpId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column smart_processor_chat_session.is_deleted
     *
     * @mbg.generated Tue May 20 15:31:42 CST 2025
     */
    private String isDeleted;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column smart_processor_chat_session.id
     *
     * @return the value of smart_processor_chat_session.id
     *
     * @mbg.generated Tue May 20 15:31:42 CST 2025
     */
    public Long getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column smart_processor_chat_session.id
     *
     * @param id the value for smart_processor_chat_session.id
     *
     * @mbg.generated Tue May 20 15:31:42 CST 2025
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column smart_processor_chat_session.gmt_create
     *
     * @return the value of smart_processor_chat_session.gmt_create
     *
     * @mbg.generated Tue May 20 15:31:42 CST 2025
     */
    public Date getGmtCreate() {
        return gmtCreate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column smart_processor_chat_session.gmt_create
     *
     * @param gmtCreate the value for smart_processor_chat_session.gmt_create
     *
     * @mbg.generated Tue May 20 15:31:42 CST 2025
     */
    public void setGmtCreate(Date gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column smart_processor_chat_session.gmt_modified
     *
     * @return the value of smart_processor_chat_session.gmt_modified
     *
     * @mbg.generated Tue May 20 15:31:42 CST 2025
     */
    public Date getGmtModified() {
        return gmtModified;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column smart_processor_chat_session.gmt_modified
     *
     * @param gmtModified the value for smart_processor_chat_session.gmt_modified
     *
     * @mbg.generated Tue May 20 15:31:42 CST 2025
     */
    public void setGmtModified(Date gmtModified) {
        this.gmtModified = gmtModified;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column smart_processor_chat_session.creator
     *
     * @return the value of smart_processor_chat_session.creator
     *
     * @mbg.generated Tue May 20 15:31:42 CST 2025
     */
    public String getCreator() {
        return creator;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column smart_processor_chat_session.creator
     *
     * @param creator the value for smart_processor_chat_session.creator
     *
     * @mbg.generated Tue May 20 15:31:42 CST 2025
     */
    public void setCreator(String creator) {
        this.creator = creator == null ? null : creator.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column smart_processor_chat_session.modifier
     *
     * @return the value of smart_processor_chat_session.modifier
     *
     * @mbg.generated Tue May 20 15:31:42 CST 2025
     */
    public String getModifier() {
        return modifier;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column smart_processor_chat_session.modifier
     *
     * @param modifier the value for smart_processor_chat_session.modifier
     *
     * @mbg.generated Tue May 20 15:31:42 CST 2025
     */
    public void setModifier(String modifier) {
        this.modifier = modifier == null ? null : modifier.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column smart_processor_chat_session.title
     *
     * @return the value of smart_processor_chat_session.title
     *
     * @mbg.generated Tue May 20 15:31:42 CST 2025
     */
    public String getTitle() {
        return title;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column smart_processor_chat_session.title
     *
     * @param title the value for smart_processor_chat_session.title
     *
     * @mbg.generated Tue May 20 15:31:42 CST 2025
     */
    public void setTitle(String title) {
        this.title = title == null ? null : title.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column smart_processor_chat_session.session_id
     *
     * @return the value of smart_processor_chat_session.session_id
     *
     * @mbg.generated Tue May 20 15:31:42 CST 2025
     */
    public String getSessionId() {
        return sessionId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column smart_processor_chat_session.session_id
     *
     * @param sessionId the value for smart_processor_chat_session.session_id
     *
     * @mbg.generated Tue May 20 15:31:42 CST 2025
     */
    public void setSessionId(String sessionId) {
        this.sessionId = sessionId == null ? null : sessionId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column smart_processor_chat_session.status
     *
     * @return the value of smart_processor_chat_session.status
     *
     * @mbg.generated Tue May 20 15:31:42 CST 2025
     */
    public String getStatus() {
        return status;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column smart_processor_chat_session.status
     *
     * @param status the value for smart_processor_chat_session.status
     *
     * @mbg.generated Tue May 20 15:31:42 CST 2025
     */
    public void setStatus(String status) {
        this.status = status == null ? null : status.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column smart_processor_chat_session.agent_id
     *
     * @return the value of smart_processor_chat_session.agent_id
     *
     * @mbg.generated Tue May 20 15:31:42 CST 2025
     */
    public String getAgentId() {
        return agentId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column smart_processor_chat_session.agent_id
     *
     * @param agentId the value for smart_processor_chat_session.agent_id
     *
     * @mbg.generated Tue May 20 15:31:42 CST 2025
     */
    public void setAgentId(String agentId) {
        this.agentId = agentId == null ? null : agentId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column smart_processor_chat_session.corp_id
     *
     * @return the value of smart_processor_chat_session.corp_id
     *
     * @mbg.generated Tue May 20 15:31:42 CST 2025
     */
    public String getCorpId() {
        return corpId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column smart_processor_chat_session.corp_id
     *
     * @param corpId the value for smart_processor_chat_session.corp_id
     *
     * @mbg.generated Tue May 20 15:31:42 CST 2025
     */
    public void setCorpId(String corpId) {
        this.corpId = corpId == null ? null : corpId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column smart_processor_chat_session.is_deleted
     *
     * @return the value of smart_processor_chat_session.is_deleted
     *
     * @mbg.generated Tue May 20 15:31:42 CST 2025
     */
    public String getIsDeleted() {
        return isDeleted;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column smart_processor_chat_session.is_deleted
     *
     * @param isDeleted the value for smart_processor_chat_session.is_deleted
     *
     * @mbg.generated Tue May 20 15:31:42 CST 2025
     */
    public void setIsDeleted(String isDeleted) {
        this.isDeleted = isDeleted == null ? null : isDeleted.trim();
    }
}