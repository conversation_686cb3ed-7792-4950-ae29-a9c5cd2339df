package com.alibaba.smart.processor.mapper;

import com.alibaba.smart.processor.model.ChatMessageDO;
import com.alibaba.smart.processor.model.ChatMessageDOExample;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.session.RowBounds;

import java.util.List;
@Mapper
public interface ChatMessageMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table smart_processor_chat_message
     *
     * @mbg.generated Tue May 20 15:31:42 CST 2025
     */
    long countByExample(ChatMessageDOExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table smart_processor_chat_message
     *
     * @mbg.generated Tue May 20 15:31:42 CST 2025
     */
    int insert(ChatMessageDO row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table smart_processor_chat_message
     *
     * @mbg.generated Tue May 20 15:31:42 CST 2025
     */
    int insertSelective(ChatMessageDO row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table smart_processor_chat_message
     *
     * @mbg.generated Tue May 20 15:31:42 CST 2025
     */
    List<ChatMessageDO> selectByExampleWithBLOBsWithRowbounds(ChatMessageDOExample example, RowBounds rowBounds);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table smart_processor_chat_message
     *
     * @mbg.generated Tue May 20 15:31:42 CST 2025
     */
    List<ChatMessageDO> selectByExampleWithBLOBs(ChatMessageDOExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table smart_processor_chat_message
     *
     * @mbg.generated Tue May 20 15:31:42 CST 2025
     */
    List<ChatMessageDO> selectByExampleWithRowbounds(ChatMessageDOExample example, RowBounds rowBounds);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table smart_processor_chat_message
     *
     * @mbg.generated Tue May 20 15:31:42 CST 2025
     */
    List<ChatMessageDO> selectByExample(ChatMessageDOExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table smart_processor_chat_message
     *
     * @mbg.generated Tue May 20 15:31:42 CST 2025
     */
    ChatMessageDO selectByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table smart_processor_chat_message
     *
     * @mbg.generated Tue May 20 15:31:42 CST 2025
     */
    int updateByExampleSelective(@Param("row") ChatMessageDO row, @Param("example") ChatMessageDOExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table smart_processor_chat_message
     *
     * @mbg.generated Tue May 20 15:31:42 CST 2025
     */
    int updateByExampleWithBLOBs(@Param("row") ChatMessageDO row, @Param("example") ChatMessageDOExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table smart_processor_chat_message
     *
     * @mbg.generated Tue May 20 15:31:42 CST 2025
     */
    int updateByExample(@Param("row") ChatMessageDO row, @Param("example") ChatMessageDOExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table smart_processor_chat_message
     *
     * @mbg.generated Tue May 20 15:31:42 CST 2025
     */
    int updateByPrimaryKeySelective(ChatMessageDO row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table smart_processor_chat_message
     *
     * @mbg.generated Tue May 20 15:31:42 CST 2025
     */
    int updateByPrimaryKeyWithBLOBs(ChatMessageDO row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table smart_processor_chat_message
     *
     * @mbg.generated Tue May 20 15:31:42 CST 2025
     */
    int updateByPrimaryKey(ChatMessageDO row);
}