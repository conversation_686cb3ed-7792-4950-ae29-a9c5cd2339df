package com.alibaba.smart.processor.utils;

import com.alibaba.smart.processor.model.BaseDataObject;

import java.util.Date;

public class BaseDataBuildUtils {
    public static void insertBuild(BaseDataObject record) {
        if (record == null) {
            return;
        }
        Date now = new Date();
        record.setIsDeleted("n");
        record.setGmtCreate(now);
        record.setGmtModified(now);
        record.setCreator("0323191242-856571019");
        record.setModifier("0323191242-856571019");
        record.setCorpId("ding5d17e3add038d44535c2f4657eb6378f");
    }

    public static void updateBuild(BaseDataObject record) {
        if (record == null) {
            return;
        }
        Date now = new Date();
        record.setIsDeleted("n");
        record.setGmtModified(now);
        record.setModifier("0323191242-856571019");
        record.setCorpId("ding5d17e3add038d44535c2f4657eb6378f");
    }
}
