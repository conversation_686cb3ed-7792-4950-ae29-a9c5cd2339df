package com.alibaba.smart.processor.mapper;

import com.alibaba.smart.processor.model.attach.FileRecord;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface FileRecordMapper {

    /**
     * 新增
     * @param fileRecord
     */
    void insert(FileRecord fileRecord);
    /**
     * 根据ossKey获取附件详情
     */
    FileRecord getFileByKey(@Param("ossKey") String ossKey);

    void updateImmKey(@Param("ossKey") String ossKey, @Param("immKey") String immKey);
}
