package com.alibaba.smart.processor.mapper;

import com.alibaba.smart.processor.model.AppResourceDO;
import com.alibaba.smart.processor.model.AppResourceDOExample;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.session.RowBounds;

@Mapper
public interface AppResourceMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table smart_processor_app_resource
     *
     * @mbg.generated Fri May 23 15:27:49 CST 2025
     */
    long countByExample(AppResourceDOExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table smart_processor_app_resource
     *
     * @mbg.generated Fri May 23 15:27:49 CST 2025
     */
    int insert(AppResourceDO row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table smart_processor_app_resource
     *
     * @mbg.generated Fri May 23 15:27:49 CST 2025
     */
    int insertSelective(AppResourceDO row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table smart_processor_app_resource
     *
     * @mbg.generated Fri May 23 15:27:49 CST 2025
     */
    List<AppResourceDO> selectByExampleWithRowbounds(AppResourceDOExample example, RowBounds rowBounds);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table smart_processor_app_resource
     *
     * @mbg.generated Fri May 23 15:27:49 CST 2025
     */
    List<AppResourceDO> selectByExample(AppResourceDOExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table smart_processor_app_resource
     *
     * @mbg.generated Fri May 23 15:27:49 CST 2025
     */
    AppResourceDO selectByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table smart_processor_app_resource
     *
     * @mbg.generated Fri May 23 15:27:49 CST 2025
     */
    int updateByExampleSelective(@Param("row") AppResourceDO row, @Param("example") AppResourceDOExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table smart_processor_app_resource
     *
     * @mbg.generated Fri May 23 15:27:49 CST 2025
     */
    int updateByExample(@Param("row") AppResourceDO row, @Param("example") AppResourceDOExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table smart_processor_app_resource
     *
     * @mbg.generated Fri May 23 15:27:49 CST 2025
     */
    int updateByPrimaryKeySelective(AppResourceDO row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table smart_processor_app_resource
     *
     * @mbg.generated Fri May 23 15:27:49 CST 2025
     */
    int updateByPrimaryKey(AppResourceDO row);
}