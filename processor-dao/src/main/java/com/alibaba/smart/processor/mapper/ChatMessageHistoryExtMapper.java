package com.alibaba.smart.processor.mapper;

import com.alibaba.smart.processor.model.ChatMessageHistoryDO;
import com.alibaba.smart.processor.model.ChatMessageHistoryDOExample;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.session.RowBounds;

import java.util.List;

@Mapper
public interface ChatMessageHistoryExtMapper extends ChatMessageHistoryMapper{
    void batchInsert(List<ChatMessageHistoryDO> rows);

    void deleteByMessageId(@Param("sessionId") String sessionId, @Param("messageId") String messageId);
}