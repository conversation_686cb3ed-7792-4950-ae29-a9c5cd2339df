package com.alibaba.smart.processor.model;

import java.util.Date;

public class ChatM<PERSON>ageDO {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column smart_processor_chat_message.id
     *
     * @mbg.generated Tue May 20 15:31:42 CST 2025
     */
    private Long id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column smart_processor_chat_message.gmt_create
     *
     * @mbg.generated Tue May 20 15:31:42 CST 2025
     */
    private Date gmtCreate;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column smart_processor_chat_message.gmt_modified
     *
     * @mbg.generated Tue May 20 15:31:42 CST 2025
     */
    private Date gmtModified;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column smart_processor_chat_message.creator
     *
     * @mbg.generated Tue May 20 15:31:42 CST 2025
     */
    private String creator;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column smart_processor_chat_message.modifier
     *
     * @mbg.generated Tue May 20 15:31:42 CST 2025
     */
    private String modifier;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column smart_processor_chat_message.is_deleted
     *
     * @mbg.generated Tue May 20 15:31:42 CST 2025
     */
    private String isDeleted;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column smart_processor_chat_message.session_id
     *
     * @mbg.generated Tue May 20 15:31:42 CST 2025
     */
    private String sessionId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column smart_processor_chat_message.sender
     *
     * @mbg.generated Tue May 20 15:31:42 CST 2025
     */
    private String sender;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column smart_processor_chat_message.sender_type
     *
     * @mbg.generated Tue May 20 15:31:42 CST 2025
     */
    private String senderType;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column smart_processor_chat_message.message_id
     *
     * @mbg.generated Tue May 20 15:31:42 CST 2025
     */
    private String messageId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column smart_processor_chat_message.corp_id
     *
     * @mbg.generated Tue May 20 15:31:42 CST 2025
     */
    private String corpId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column smart_processor_chat_message.message
     *
     * @mbg.generated Tue May 20 15:31:42 CST 2025
     */
    private String message;

    /**
     * 关联的附件信息
     */
    private String files;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column smart_processor_chat_message.id
     *
     * @return the value of smart_processor_chat_message.id
     *
     * @mbg.generated Tue May 20 15:31:42 CST 2025
     */
    public Long getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column smart_processor_chat_message.id
     *
     * @param id the value for smart_processor_chat_message.id
     *
     * @mbg.generated Tue May 20 15:31:42 CST 2025
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column smart_processor_chat_message.gmt_create
     *
     * @return the value of smart_processor_chat_message.gmt_create
     *
     * @mbg.generated Tue May 20 15:31:42 CST 2025
     */
    public Date getGmtCreate() {
        return gmtCreate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column smart_processor_chat_message.gmt_create
     *
     * @param gmtCreate the value for smart_processor_chat_message.gmt_create
     *
     * @mbg.generated Tue May 20 15:31:42 CST 2025
     */
    public void setGmtCreate(Date gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column smart_processor_chat_message.gmt_modified
     *
     * @return the value of smart_processor_chat_message.gmt_modified
     *
     * @mbg.generated Tue May 20 15:31:42 CST 2025
     */
    public Date getGmtModified() {
        return gmtModified;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column smart_processor_chat_message.gmt_modified
     *
     * @param gmtModified the value for smart_processor_chat_message.gmt_modified
     *
     * @mbg.generated Tue May 20 15:31:42 CST 2025
     */
    public void setGmtModified(Date gmtModified) {
        this.gmtModified = gmtModified;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column smart_processor_chat_message.creator
     *
     * @return the value of smart_processor_chat_message.creator
     *
     * @mbg.generated Tue May 20 15:31:42 CST 2025
     */
    public String getCreator() {
        return creator;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column smart_processor_chat_message.creator
     *
     * @param creator the value for smart_processor_chat_message.creator
     *
     * @mbg.generated Tue May 20 15:31:42 CST 2025
     */
    public void setCreator(String creator) {
        this.creator = creator == null ? null : creator.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column smart_processor_chat_message.modifier
     *
     * @return the value of smart_processor_chat_message.modifier
     *
     * @mbg.generated Tue May 20 15:31:42 CST 2025
     */
    public String getModifier() {
        return modifier;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column smart_processor_chat_message.modifier
     *
     * @param modifier the value for smart_processor_chat_message.modifier
     *
     * @mbg.generated Tue May 20 15:31:42 CST 2025
     */
    public void setModifier(String modifier) {
        this.modifier = modifier == null ? null : modifier.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column smart_processor_chat_message.is_deleted
     *
     * @return the value of smart_processor_chat_message.is_deleted
     *
     * @mbg.generated Tue May 20 15:31:42 CST 2025
     */
    public String getIsDeleted() {
        return isDeleted;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column smart_processor_chat_message.is_deleted
     *
     * @param isDeleted the value for smart_processor_chat_message.is_deleted
     *
     * @mbg.generated Tue May 20 15:31:42 CST 2025
     */
    public void setIsDeleted(String isDeleted) {
        this.isDeleted = isDeleted == null ? null : isDeleted.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column smart_processor_chat_message.session_id
     *
     * @return the value of smart_processor_chat_message.session_id
     *
     * @mbg.generated Tue May 20 15:31:42 CST 2025
     */
    public String getSessionId() {
        return sessionId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column smart_processor_chat_message.session_id
     *
     * @param sessionId the value for smart_processor_chat_message.session_id
     *
     * @mbg.generated Tue May 20 15:31:42 CST 2025
     */
    public void setSessionId(String sessionId) {
        this.sessionId = sessionId == null ? null : sessionId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column smart_processor_chat_message.sender
     *
     * @return the value of smart_processor_chat_message.sender
     *
     * @mbg.generated Tue May 20 15:31:42 CST 2025
     */
    public String getSender() {
        return sender;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column smart_processor_chat_message.sender
     *
     * @param sender the value for smart_processor_chat_message.sender
     *
     * @mbg.generated Tue May 20 15:31:42 CST 2025
     */
    public void setSender(String sender) {
        this.sender = sender == null ? null : sender.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column smart_processor_chat_message.sender_type
     *
     * @return the value of smart_processor_chat_message.sender_type
     *
     * @mbg.generated Tue May 20 15:31:42 CST 2025
     */
    public String getSenderType() {
        return senderType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column smart_processor_chat_message.sender_type
     *
     * @param senderType the value for smart_processor_chat_message.sender_type
     *
     * @mbg.generated Tue May 20 15:31:42 CST 2025
     */
    public void setSenderType(String senderType) {
        this.senderType = senderType == null ? null : senderType.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column smart_processor_chat_message.message_id
     *
     * @return the value of smart_processor_chat_message.message_id
     *
     * @mbg.generated Tue May 20 15:31:42 CST 2025
     */
    public String getMessageId() {
        return messageId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column smart_processor_chat_message.message_id
     *
     * @param messageId the value for smart_processor_chat_message.message_id
     *
     * @mbg.generated Tue May 20 15:31:42 CST 2025
     */
    public void setMessageId(String messageId) {
        this.messageId = messageId == null ? null : messageId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column smart_processor_chat_message.corp_id
     *
     * @return the value of smart_processor_chat_message.corp_id
     *
     * @mbg.generated Tue May 20 15:31:42 CST 2025
     */
    public String getCorpId() {
        return corpId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column smart_processor_chat_message.corp_id
     *
     * @param corpId the value for smart_processor_chat_message.corp_id
     *
     * @mbg.generated Tue May 20 15:31:42 CST 2025
     */
    public void setCorpId(String corpId) {
        this.corpId = corpId == null ? null : corpId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column smart_processor_chat_message.message
     *
     * @return the value of smart_processor_chat_message.message
     *
     * @mbg.generated Tue May 20 15:31:42 CST 2025
     */
    public String getMessage() {
        return message;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column smart_processor_chat_message.message
     *
     * @param message the value for smart_processor_chat_message.message
     *
     * @mbg.generated Tue May 20 15:31:42 CST 2025
     */
    public void setMessage(String message) {
        this.message = message == null ? null : message.trim();
    }

    public String getFiles() {
        return files;
    }

    public void setFiles(String files) {
        this.files = files;
    }
}