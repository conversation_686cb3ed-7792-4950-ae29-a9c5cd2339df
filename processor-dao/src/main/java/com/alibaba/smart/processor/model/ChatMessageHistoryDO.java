package com.alibaba.smart.processor.model;

import java.util.Date;

public class Chat<PERSON>essageHistoryDO {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column smart_processor_chat_message_history.id
     *
     * @mbg.generated Tue May 20 15:31:42 CST 2025
     */
    private Long id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column smart_processor_chat_message_history.gmt_create
     *
     * @mbg.generated Tue May 20 15:31:42 CST 2025
     */
    private Date gmtCreate;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column smart_processor_chat_message_history.gmt_modified
     *
     * @mbg.generated Tue May 20 15:31:42 CST 2025
     */
    private Date gmtModified;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column smart_processor_chat_message_history.creator
     *
     * @mbg.generated Tue May 20 15:31:42 CST 2025
     */
    private String creator;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column smart_processor_chat_message_history.modifier
     *
     * @mbg.generated Tue May 20 15:31:42 CST 2025
     */
    private String modifier;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column smart_processor_chat_message_history.is_deleted
     *
     * @mbg.generated Tue May 20 15:31:42 CST 2025
     */
    private String isDeleted;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column smart_processor_chat_message_history.session_id
     *
     * @mbg.generated Tue May 20 15:31:42 CST 2025
     */
    private String sessionId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column smart_processor_chat_message_history.message_id
     *
     * @mbg.generated Tue May 20 15:31:42 CST 2025
     */
    private String messageId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column smart_processor_chat_message_history.extension
     *
     * @mbg.generated Tue May 20 15:31:42 CST 2025
     */
    private String extension;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column smart_processor_chat_message_history.content
     *
     * @mbg.generated Tue May 20 15:31:42 CST 2025
     */
    private String content;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column smart_processor_chat_message_history.id
     *
     * @return the value of smart_processor_chat_message_history.id
     *
     * @mbg.generated Tue May 20 15:31:42 CST 2025
     */
    public Long getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column smart_processor_chat_message_history.id
     *
     * @param id the value for smart_processor_chat_message_history.id
     *
     * @mbg.generated Tue May 20 15:31:42 CST 2025
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column smart_processor_chat_message_history.gmt_create
     *
     * @return the value of smart_processor_chat_message_history.gmt_create
     *
     * @mbg.generated Tue May 20 15:31:42 CST 2025
     */
    public Date getGmtCreate() {
        return gmtCreate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column smart_processor_chat_message_history.gmt_create
     *
     * @param gmtCreate the value for smart_processor_chat_message_history.gmt_create
     *
     * @mbg.generated Tue May 20 15:31:42 CST 2025
     */
    public void setGmtCreate(Date gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column smart_processor_chat_message_history.gmt_modified
     *
     * @return the value of smart_processor_chat_message_history.gmt_modified
     *
     * @mbg.generated Tue May 20 15:31:42 CST 2025
     */
    public Date getGmtModified() {
        return gmtModified;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column smart_processor_chat_message_history.gmt_modified
     *
     * @param gmtModified the value for smart_processor_chat_message_history.gmt_modified
     *
     * @mbg.generated Tue May 20 15:31:42 CST 2025
     */
    public void setGmtModified(Date gmtModified) {
        this.gmtModified = gmtModified;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column smart_processor_chat_message_history.creator
     *
     * @return the value of smart_processor_chat_message_history.creator
     *
     * @mbg.generated Tue May 20 15:31:42 CST 2025
     */
    public String getCreator() {
        return creator;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column smart_processor_chat_message_history.creator
     *
     * @param creator the value for smart_processor_chat_message_history.creator
     *
     * @mbg.generated Tue May 20 15:31:42 CST 2025
     */
    public void setCreator(String creator) {
        this.creator = creator == null ? null : creator.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column smart_processor_chat_message_history.modifier
     *
     * @return the value of smart_processor_chat_message_history.modifier
     *
     * @mbg.generated Tue May 20 15:31:42 CST 2025
     */
    public String getModifier() {
        return modifier;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column smart_processor_chat_message_history.modifier
     *
     * @param modifier the value for smart_processor_chat_message_history.modifier
     *
     * @mbg.generated Tue May 20 15:31:42 CST 2025
     */
    public void setModifier(String modifier) {
        this.modifier = modifier == null ? null : modifier.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column smart_processor_chat_message_history.is_deleted
     *
     * @return the value of smart_processor_chat_message_history.is_deleted
     *
     * @mbg.generated Tue May 20 15:31:42 CST 2025
     */
    public String getIsDeleted() {
        return isDeleted;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column smart_processor_chat_message_history.is_deleted
     *
     * @param isDeleted the value for smart_processor_chat_message_history.is_deleted
     *
     * @mbg.generated Tue May 20 15:31:42 CST 2025
     */
    public void setIsDeleted(String isDeleted) {
        this.isDeleted = isDeleted == null ? null : isDeleted.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column smart_processor_chat_message_history.session_id
     *
     * @return the value of smart_processor_chat_message_history.session_id
     *
     * @mbg.generated Tue May 20 15:31:42 CST 2025
     */
    public String getSessionId() {
        return sessionId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column smart_processor_chat_message_history.session_id
     *
     * @param sessionId the value for smart_processor_chat_message_history.session_id
     *
     * @mbg.generated Tue May 20 15:31:42 CST 2025
     */
    public void setSessionId(String sessionId) {
        this.sessionId = sessionId == null ? null : sessionId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column smart_processor_chat_message_history.message_id
     *
     * @return the value of smart_processor_chat_message_history.message_id
     *
     * @mbg.generated Tue May 20 15:31:42 CST 2025
     */
    public String getMessageId() {
        return messageId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column smart_processor_chat_message_history.message_id
     *
     * @param messageId the value for smart_processor_chat_message_history.message_id
     *
     * @mbg.generated Tue May 20 15:31:42 CST 2025
     */
    public void setMessageId(String messageId) {
        this.messageId = messageId == null ? null : messageId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column smart_processor_chat_message_history.extension
     *
     * @return the value of smart_processor_chat_message_history.extension
     *
     * @mbg.generated Tue May 20 15:31:42 CST 2025
     */
    public String getExtension() {
        return extension;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column smart_processor_chat_message_history.extension
     *
     * @param extension the value for smart_processor_chat_message_history.extension
     *
     * @mbg.generated Tue May 20 15:31:42 CST 2025
     */
    public void setExtension(String extension) {
        this.extension = extension == null ? null : extension.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column smart_processor_chat_message_history.content
     *
     * @return the value of smart_processor_chat_message_history.content
     *
     * @mbg.generated Tue May 20 15:31:42 CST 2025
     */
    public String getContent() {
        return content;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column smart_processor_chat_message_history.content
     *
     * @param content the value for smart_processor_chat_message_history.content
     *
     * @mbg.generated Tue May 20 15:31:42 CST 2025
     */
    public void setContent(String content) {
        this.content = content == null ? null : content.trim();
    }
}