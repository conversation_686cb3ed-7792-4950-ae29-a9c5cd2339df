package com.alibaba.smart.processor.mapper;

import com.alibaba.smart.processor.model.AppInfoDO;
import com.alibaba.smart.processor.model.AppInfoDOExample;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.session.RowBounds;

import java.util.List;

@Mapper
public interface AppInfoExtMapper extends AppInfoMapper{
    /**
     * 分页获取
     * @param pageIndex
     * @param corpId
     * @param pageSize
     * @return
     */
    List<AppInfoDO> listAppWithPage(@Param("corpId") String corpId, @Param("pageIndex") Integer pageIndex, @Param("pageSize") Integer pageSize);
}