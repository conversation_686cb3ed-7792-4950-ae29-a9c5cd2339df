package com.alibaba.smart.processor.model;

import java.util.Date;

public class AppResourceDO {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column smart_processor_app_resource.id
     *
     * @mbg.generated Fri May 23 15:27:49 CST 2025
     */
    private Long id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column smart_processor_app_resource.gmt_create
     *
     * @mbg.generated Fri May 23 15:27:49 CST 2025
     */
    private Date gmtCreate;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column smart_processor_app_resource.gmt_modified
     *
     * @mbg.generated Fri May 23 15:27:49 CST 2025
     */
    private Date gmtModified;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column smart_processor_app_resource.creator
     *
     * @mbg.generated Fri May 23 15:27:49 CST 2025
     */
    private String creator;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column smart_processor_app_resource.modifier
     *
     * @mbg.generated Fri May 23 15:27:49 CST 2025
     */
    private String modifier;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column smart_processor_app_resource.is_deleted
     *
     * @mbg.generated Fri May 23 15:27:49 CST 2025
     */
    private String isDeleted;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column smart_processor_app_resource.app_id
     *
     * @mbg.generated Fri May 23 15:27:49 CST 2025
     */
    private String appId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column smart_processor_app_resource.resource_id
     *
     * @mbg.generated Fri May 23 15:27:49 CST 2025
     */
    private String resourceId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column smart_processor_app_resource.source_key
     *
     * @mbg.generated Fri May 23 15:27:49 CST 2025
     */
    private String sourceKey;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column smart_processor_app_resource.source_compiled_key
     *
     * @mbg.generated Fri May 23 15:27:49 CST 2025
     */
    private String sourceCompiledKey;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column smart_processor_app_resource.resource_type
     *
     * @mbg.generated Fri May 23 15:27:49 CST 2025
     */
    private String resourceType;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column smart_processor_app_resource.version
     *
     * @mbg.generated Fri May 23 15:27:49 CST 2025
     */
    private String version;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column smart_processor_app_resource.corp_id
     *
     * @mbg.generated Fri May 23 15:27:49 CST 2025
     */
    private String corpId;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column smart_processor_app_resource.id
     *
     * @return the value of smart_processor_app_resource.id
     *
     * @mbg.generated Fri May 23 15:27:49 CST 2025
     */
    public Long getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column smart_processor_app_resource.id
     *
     * @param id the value for smart_processor_app_resource.id
     *
     * @mbg.generated Fri May 23 15:27:49 CST 2025
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column smart_processor_app_resource.gmt_create
     *
     * @return the value of smart_processor_app_resource.gmt_create
     *
     * @mbg.generated Fri May 23 15:27:49 CST 2025
     */
    public Date getGmtCreate() {
        return gmtCreate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column smart_processor_app_resource.gmt_create
     *
     * @param gmtCreate the value for smart_processor_app_resource.gmt_create
     *
     * @mbg.generated Fri May 23 15:27:49 CST 2025
     */
    public void setGmtCreate(Date gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column smart_processor_app_resource.gmt_modified
     *
     * @return the value of smart_processor_app_resource.gmt_modified
     *
     * @mbg.generated Fri May 23 15:27:49 CST 2025
     */
    public Date getGmtModified() {
        return gmtModified;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column smart_processor_app_resource.gmt_modified
     *
     * @param gmtModified the value for smart_processor_app_resource.gmt_modified
     *
     * @mbg.generated Fri May 23 15:27:49 CST 2025
     */
    public void setGmtModified(Date gmtModified) {
        this.gmtModified = gmtModified;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column smart_processor_app_resource.creator
     *
     * @return the value of smart_processor_app_resource.creator
     *
     * @mbg.generated Fri May 23 15:27:49 CST 2025
     */
    public String getCreator() {
        return creator;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column smart_processor_app_resource.creator
     *
     * @param creator the value for smart_processor_app_resource.creator
     *
     * @mbg.generated Fri May 23 15:27:49 CST 2025
     */
    public void setCreator(String creator) {
        this.creator = creator == null ? null : creator.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column smart_processor_app_resource.modifier
     *
     * @return the value of smart_processor_app_resource.modifier
     *
     * @mbg.generated Fri May 23 15:27:49 CST 2025
     */
    public String getModifier() {
        return modifier;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column smart_processor_app_resource.modifier
     *
     * @param modifier the value for smart_processor_app_resource.modifier
     *
     * @mbg.generated Fri May 23 15:27:49 CST 2025
     */
    public void setModifier(String modifier) {
        this.modifier = modifier == null ? null : modifier.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column smart_processor_app_resource.is_deleted
     *
     * @return the value of smart_processor_app_resource.is_deleted
     *
     * @mbg.generated Fri May 23 15:27:49 CST 2025
     */
    public String getIsDeleted() {
        return isDeleted;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column smart_processor_app_resource.is_deleted
     *
     * @param isDeleted the value for smart_processor_app_resource.is_deleted
     *
     * @mbg.generated Fri May 23 15:27:49 CST 2025
     */
    public void setIsDeleted(String isDeleted) {
        this.isDeleted = isDeleted == null ? null : isDeleted.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column smart_processor_app_resource.app_id
     *
     * @return the value of smart_processor_app_resource.app_id
     *
     * @mbg.generated Fri May 23 15:27:49 CST 2025
     */
    public String getAppId() {
        return appId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column smart_processor_app_resource.app_id
     *
     * @param appId the value for smart_processor_app_resource.app_id
     *
     * @mbg.generated Fri May 23 15:27:49 CST 2025
     */
    public void setAppId(String appId) {
        this.appId = appId == null ? null : appId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column smart_processor_app_resource.resource_id
     *
     * @return the value of smart_processor_app_resource.resource_id
     *
     * @mbg.generated Fri May 23 15:27:49 CST 2025
     */
    public String getResourceId() {
        return resourceId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column smart_processor_app_resource.resource_id
     *
     * @param resourceId the value for smart_processor_app_resource.resource_id
     *
     * @mbg.generated Fri May 23 15:27:49 CST 2025
     */
    public void setResourceId(String resourceId) {
        this.resourceId = resourceId == null ? null : resourceId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column smart_processor_app_resource.source_key
     *
     * @return the value of smart_processor_app_resource.source_key
     *
     * @mbg.generated Fri May 23 15:27:49 CST 2025
     */
    public String getSourceKey() {
        return sourceKey;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column smart_processor_app_resource.source_key
     *
     * @param sourceKey the value for smart_processor_app_resource.source_key
     *
     * @mbg.generated Fri May 23 15:27:49 CST 2025
     */
    public void setSourceKey(String sourceKey) {
        this.sourceKey = sourceKey == null ? null : sourceKey.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column smart_processor_app_resource.source_compiled_key
     *
     * @return the value of smart_processor_app_resource.source_compiled_key
     *
     * @mbg.generated Fri May 23 15:27:49 CST 2025
     */
    public String getSourceCompiledKey() {
        return sourceCompiledKey;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column smart_processor_app_resource.source_compiled_key
     *
     * @param sourceCompiledKey the value for smart_processor_app_resource.source_compiled_key
     *
     * @mbg.generated Fri May 23 15:27:49 CST 2025
     */
    public void setSourceCompiledKey(String sourceCompiledKey) {
        this.sourceCompiledKey = sourceCompiledKey == null ? null : sourceCompiledKey.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column smart_processor_app_resource.resource_type
     *
     * @return the value of smart_processor_app_resource.resource_type
     *
     * @mbg.generated Fri May 23 15:27:49 CST 2025
     */
    public String getResourceType() {
        return resourceType;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column smart_processor_app_resource.resource_type
     *
     * @param resourceType the value for smart_processor_app_resource.resource_type
     *
     * @mbg.generated Fri May 23 15:27:49 CST 2025
     */
    public void setResourceType(String resourceType) {
        this.resourceType = resourceType == null ? null : resourceType.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column smart_processor_app_resource.version
     *
     * @return the value of smart_processor_app_resource.version
     *
     * @mbg.generated Fri May 23 15:27:49 CST 2025
     */
    public String getVersion() {
        return version;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column smart_processor_app_resource.version
     *
     * @param version the value for smart_processor_app_resource.version
     *
     * @mbg.generated Fri May 23 15:27:49 CST 2025
     */
    public void setVersion(String version) {
        this.version = version == null ? null : version.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column smart_processor_app_resource.corp_id
     *
     * @return the value of smart_processor_app_resource.corp_id
     *
     * @mbg.generated Fri May 23 15:27:49 CST 2025
     */
    public String getCorpId() {
        return corpId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column smart_processor_app_resource.corp_id
     *
     * @param corpId the value for smart_processor_app_resource.corp_id
     *
     * @mbg.generated Fri May 23 15:27:49 CST 2025
     */
    public void setCorpId(String corpId) {
        this.corpId = corpId == null ? null : corpId.trim();
    }
}