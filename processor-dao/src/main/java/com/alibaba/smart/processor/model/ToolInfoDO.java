package com.alibaba.smart.processor.model;

import java.util.Date;

public class ToolInfoDO {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column smart_processor_tool_info.id
     *
     * @mbg.generated Thu May 15 19:11:45 CST 2025
     */
    private Long id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column smart_processor_tool_info.gmt_create
     *
     * @mbg.generated Thu May 15 19:11:45 CST 2025
     */
    private Date gmtCreate;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column smart_processor_tool_info.gmt_modified
     *
     * @mbg.generated Thu May 15 19:11:45 CST 2025
     */
    private Date gmtModified;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column smart_processor_tool_info.creator
     *
     * @mbg.generated Thu May 15 19:11:45 CST 2025
     */
    private String creator;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column smart_processor_tool_info.modifier
     *
     * @mbg.generated Thu May 15 19:11:45 CST 2025
     */
    private String modifier;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column smart_processor_tool_info.is_deleted
     *
     * @mbg.generated Thu May 15 19:11:45 CST 2025
     */
    private String isDeleted;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column smart_processor_tool_info.corp_id
     *
     * @mbg.generated Thu May 15 19:11:45 CST 2025
     */
    private String corpId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column smart_processor_tool_info.session_id
     *
     * @mbg.generated Thu May 15 19:11:45 CST 2025
     */
    private String sessionId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column smart_processor_tool_info.agent_id
     *
     * @mbg.generated Thu May 15 19:11:45 CST 2025
     */
    private String agentId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column smart_processor_tool_info.uuid
     *
     * @mbg.generated Thu May 15 19:11:45 CST 2025
     */
    private String uuid;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column smart_processor_tool_info.name
     *
     * @mbg.generated Thu May 15 19:11:45 CST 2025
     */
    private String name;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column smart_processor_tool_info.type
     *
     * @mbg.generated Thu May 15 19:11:45 CST 2025
     */
    private String type;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column smart_processor_tool_info.description
     *
     * @mbg.generated Thu May 15 19:11:45 CST 2025
     */
    private String description;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column smart_processor_tool_info.status
     *
     * @mbg.generated Thu May 15 19:11:45 CST 2025
     */
    private String status;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column smart_processor_tool_info.version
     *
     * @mbg.generated Thu May 15 19:11:45 CST 2025
     */
    private String version;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column smart_processor_tool_info.content
     *
     * @mbg.generated Thu May 15 19:11:45 CST 2025
     */
    private String content;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column smart_processor_tool_info.request_format
     *
     * @mbg.generated Thu May 15 19:11:45 CST 2025
     */
    private String requestFormat;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column smart_processor_tool_info.response_format
     *
     * @mbg.generated Thu May 15 19:11:45 CST 2025
     */
    private String responseFormat;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column smart_processor_tool_info.id
     *
     * @return the value of smart_processor_tool_info.id
     *
     * @mbg.generated Thu May 15 19:11:45 CST 2025
     */
    public Long getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column smart_processor_tool_info.id
     *
     * @param id the value for smart_processor_tool_info.id
     *
     * @mbg.generated Thu May 15 19:11:45 CST 2025
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column smart_processor_tool_info.gmt_create
     *
     * @return the value of smart_processor_tool_info.gmt_create
     *
     * @mbg.generated Thu May 15 19:11:45 CST 2025
     */
    public Date getGmtCreate() {
        return gmtCreate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column smart_processor_tool_info.gmt_create
     *
     * @param gmtCreate the value for smart_processor_tool_info.gmt_create
     *
     * @mbg.generated Thu May 15 19:11:45 CST 2025
     */
    public void setGmtCreate(Date gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column smart_processor_tool_info.gmt_modified
     *
     * @return the value of smart_processor_tool_info.gmt_modified
     *
     * @mbg.generated Thu May 15 19:11:45 CST 2025
     */
    public Date getGmtModified() {
        return gmtModified;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column smart_processor_tool_info.gmt_modified
     *
     * @param gmtModified the value for smart_processor_tool_info.gmt_modified
     *
     * @mbg.generated Thu May 15 19:11:45 CST 2025
     */
    public void setGmtModified(Date gmtModified) {
        this.gmtModified = gmtModified;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column smart_processor_tool_info.creator
     *
     * @return the value of smart_processor_tool_info.creator
     *
     * @mbg.generated Thu May 15 19:11:45 CST 2025
     */
    public String getCreator() {
        return creator;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column smart_processor_tool_info.creator
     *
     * @param creator the value for smart_processor_tool_info.creator
     *
     * @mbg.generated Thu May 15 19:11:45 CST 2025
     */
    public void setCreator(String creator) {
        this.creator = creator == null ? null : creator.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column smart_processor_tool_info.modifier
     *
     * @return the value of smart_processor_tool_info.modifier
     *
     * @mbg.generated Thu May 15 19:11:45 CST 2025
     */
    public String getModifier() {
        return modifier;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column smart_processor_tool_info.modifier
     *
     * @param modifier the value for smart_processor_tool_info.modifier
     *
     * @mbg.generated Thu May 15 19:11:45 CST 2025
     */
    public void setModifier(String modifier) {
        this.modifier = modifier == null ? null : modifier.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column smart_processor_tool_info.is_deleted
     *
     * @return the value of smart_processor_tool_info.is_deleted
     *
     * @mbg.generated Thu May 15 19:11:45 CST 2025
     */
    public String getIsDeleted() {
        return isDeleted;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column smart_processor_tool_info.is_deleted
     *
     * @param isDeleted the value for smart_processor_tool_info.is_deleted
     *
     * @mbg.generated Thu May 15 19:11:45 CST 2025
     */
    public void setIsDeleted(String isDeleted) {
        this.isDeleted = isDeleted == null ? null : isDeleted.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column smart_processor_tool_info.corp_id
     *
     * @return the value of smart_processor_tool_info.corp_id
     *
     * @mbg.generated Thu May 15 19:11:45 CST 2025
     */
    public String getCorpId() {
        return corpId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column smart_processor_tool_info.corp_id
     *
     * @param corpId the value for smart_processor_tool_info.corp_id
     *
     * @mbg.generated Thu May 15 19:11:45 CST 2025
     */
    public void setCorpId(String corpId) {
        this.corpId = corpId == null ? null : corpId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column smart_processor_tool_info.session_id
     *
     * @return the value of smart_processor_tool_info.session_id
     *
     * @mbg.generated Thu May 15 19:11:45 CST 2025
     */
    public String getSessionId() {
        return sessionId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column smart_processor_tool_info.session_id
     *
     * @param sessionId the value for smart_processor_tool_info.session_id
     *
     * @mbg.generated Thu May 15 19:11:45 CST 2025
     */
    public void setSessionId(String sessionId) {
        this.sessionId = sessionId == null ? null : sessionId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column smart_processor_tool_info.agent_id
     *
     * @return the value of smart_processor_tool_info.agent_id
     *
     * @mbg.generated Thu May 15 19:11:45 CST 2025
     */
    public String getAgentId() {
        return agentId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column smart_processor_tool_info.agent_id
     *
     * @param agentId the value for smart_processor_tool_info.agent_id
     *
     * @mbg.generated Thu May 15 19:11:45 CST 2025
     */
    public void setAgentId(String agentId) {
        this.agentId = agentId == null ? null : agentId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column smart_processor_tool_info.uuid
     *
     * @return the value of smart_processor_tool_info.uuid
     *
     * @mbg.generated Thu May 15 19:11:45 CST 2025
     */
    public String getUuid() {
        return uuid;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column smart_processor_tool_info.uuid
     *
     * @param uuid the value for smart_processor_tool_info.uuid
     *
     * @mbg.generated Thu May 15 19:11:45 CST 2025
     */
    public void setUuid(String uuid) {
        this.uuid = uuid == null ? null : uuid.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column smart_processor_tool_info.name
     *
     * @return the value of smart_processor_tool_info.name
     *
     * @mbg.generated Thu May 15 19:11:45 CST 2025
     */
    public String getName() {
        return name;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column smart_processor_tool_info.name
     *
     * @param name the value for smart_processor_tool_info.name
     *
     * @mbg.generated Thu May 15 19:11:45 CST 2025
     */
    public void setName(String name) {
        this.name = name == null ? null : name.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column smart_processor_tool_info.type
     *
     * @return the value of smart_processor_tool_info.type
     *
     * @mbg.generated Thu May 15 19:11:45 CST 2025
     */
    public String getType() {
        return type;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column smart_processor_tool_info.type
     *
     * @param type the value for smart_processor_tool_info.type
     *
     * @mbg.generated Thu May 15 19:11:45 CST 2025
     */
    public void setType(String type) {
        this.type = type == null ? null : type.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column smart_processor_tool_info.description
     *
     * @return the value of smart_processor_tool_info.description
     *
     * @mbg.generated Thu May 15 19:11:45 CST 2025
     */
    public String getDescription() {
        return description;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column smart_processor_tool_info.description
     *
     * @param description the value for smart_processor_tool_info.description
     *
     * @mbg.generated Thu May 15 19:11:45 CST 2025
     */
    public void setDescription(String description) {
        this.description = description == null ? null : description.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column smart_processor_tool_info.status
     *
     * @return the value of smart_processor_tool_info.status
     *
     * @mbg.generated Thu May 15 19:11:45 CST 2025
     */
    public String getStatus() {
        return status;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column smart_processor_tool_info.status
     *
     * @param status the value for smart_processor_tool_info.status
     *
     * @mbg.generated Thu May 15 19:11:45 CST 2025
     */
    public void setStatus(String status) {
        this.status = status == null ? null : status.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column smart_processor_tool_info.version
     *
     * @return the value of smart_processor_tool_info.version
     *
     * @mbg.generated Thu May 15 19:11:45 CST 2025
     */
    public String getVersion() {
        return version;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column smart_processor_tool_info.version
     *
     * @param version the value for smart_processor_tool_info.version
     *
     * @mbg.generated Thu May 15 19:11:45 CST 2025
     */
    public void setVersion(String version) {
        this.version = version == null ? null : version.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column smart_processor_tool_info.content
     *
     * @return the value of smart_processor_tool_info.content
     *
     * @mbg.generated Thu May 15 19:11:45 CST 2025
     */
    public String getContent() {
        return content;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column smart_processor_tool_info.content
     *
     * @param content the value for smart_processor_tool_info.content
     *
     * @mbg.generated Thu May 15 19:11:45 CST 2025
     */
    public void setContent(String content) {
        this.content = content == null ? null : content.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column smart_processor_tool_info.request_format
     *
     * @return the value of smart_processor_tool_info.request_format
     *
     * @mbg.generated Thu May 15 19:11:45 CST 2025
     */
    public String getRequestFormat() {
        return requestFormat;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column smart_processor_tool_info.request_format
     *
     * @param requestFormat the value for smart_processor_tool_info.request_format
     *
     * @mbg.generated Thu May 15 19:11:45 CST 2025
     */
    public void setRequestFormat(String requestFormat) {
        this.requestFormat = requestFormat == null ? null : requestFormat.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column smart_processor_tool_info.response_format
     *
     * @return the value of smart_processor_tool_info.response_format
     *
     * @mbg.generated Thu May 15 19:11:45 CST 2025
     */
    public String getResponseFormat() {
        return responseFormat;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column smart_processor_tool_info.response_format
     *
     * @param responseFormat the value for smart_processor_tool_info.response_format
     *
     * @mbg.generated Thu May 15 19:11:45 CST 2025
     */
    public void setResponseFormat(String responseFormat) {
        this.responseFormat = responseFormat == null ? null : responseFormat.trim();
    }
}