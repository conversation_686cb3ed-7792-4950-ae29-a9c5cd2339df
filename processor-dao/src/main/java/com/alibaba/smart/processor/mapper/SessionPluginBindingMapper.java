package com.alibaba.smart.processor.mapper;

import com.alibaba.smart.processor.model.SessionPluginBindingDO;
import com.alibaba.smart.processor.model.SessionPluginBindingDOExample;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.session.RowBounds;

@Mapper
public interface SessionPluginBindingMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table smart_processor_session_plugins_binding
     *
     * @mbg.generated Mon Jun 16 20:06:22 CST 2025
     */
    long countByExample(SessionPluginBindingDOExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table smart_processor_session_plugins_binding
     *
     * @mbg.generated Mon Jun 16 20:06:22 CST 2025
     */
    int insert(SessionPluginBindingDO row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table smart_processor_session_plugins_binding
     *
     * @mbg.generated Mon Jun 16 20:06:22 CST 2025
     */
    int insertSelective(SessionPluginBindingDO row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table smart_processor_session_plugins_binding
     *
     * @mbg.generated Mon Jun 16 20:06:22 CST 2025
     */
    List<SessionPluginBindingDO> selectByExampleWithRowbounds(SessionPluginBindingDOExample example, RowBounds rowBounds);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table smart_processor_session_plugins_binding
     *
     * @mbg.generated Mon Jun 16 20:06:22 CST 2025
     */
    List<SessionPluginBindingDO> selectByExample(SessionPluginBindingDOExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table smart_processor_session_plugins_binding
     *
     * @mbg.generated Mon Jun 16 20:06:22 CST 2025
     */
    SessionPluginBindingDO selectByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table smart_processor_session_plugins_binding
     *
     * @mbg.generated Mon Jun 16 20:06:22 CST 2025
     */
    int updateByExampleSelective(@Param("row") SessionPluginBindingDO row, @Param("example") SessionPluginBindingDOExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table smart_processor_session_plugins_binding
     *
     * @mbg.generated Mon Jun 16 20:06:22 CST 2025
     */
    int updateByExample(@Param("row") SessionPluginBindingDO row, @Param("example") SessionPluginBindingDOExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table smart_processor_session_plugins_binding
     *
     * @mbg.generated Mon Jun 16 20:06:22 CST 2025
     */
    int updateByPrimaryKeySelective(SessionPluginBindingDO row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table smart_processor_session_plugins_binding
     *
     * @mbg.generated Mon Jun 16 20:06:22 CST 2025
     */
    int updateByPrimaryKey(SessionPluginBindingDO row);
}