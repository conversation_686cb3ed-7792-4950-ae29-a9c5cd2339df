package com.alibaba.smart.processor.model;

import java.util.Date;

public class PluginsDO {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column smart_processor_plugins.id
     *
     * @mbg.generated Tue Jun 17 19:25:00 CST 2025
     */
    private Long id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column smart_processor_plugins.gmt_create
     *
     * @mbg.generated Tue Jun 17 19:25:00 CST 2025
     */
    private Date gmtCreate;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column smart_processor_plugins.gmt_modified
     *
     * @mbg.generated Tue Jun 17 19:25:00 CST 2025
     */
    private Date gmtModified;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column smart_processor_plugins.creator
     *
     * @mbg.generated Tue Jun 17 19:25:00 CST 2025
     */
    private String creator;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column smart_processor_plugins.modifier
     *
     * @mbg.generated Tue Jun 17 19:25:00 CST 2025
     */
    private String modifier;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column smart_processor_plugins.is_deleted
     *
     * @mbg.generated Tue Jun 17 19:25:00 CST 2025
     */
    private String isDeleted;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column smart_processor_plugins.plugin_id
     *
     * @mbg.generated Tue Jun 17 19:25:00 CST 2025
     */
    private String pluginId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column smart_processor_plugins.name
     *
     * @mbg.generated Tue Jun 17 19:25:00 CST 2025
     */
    private String name;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column smart_processor_plugins.description
     *
     * @mbg.generated Tue Jun 17 19:25:00 CST 2025
     */
    private String description;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column smart_processor_plugins.icon
     *
     * @mbg.generated Tue Jun 17 19:25:00 CST 2025
     */
    private String icon;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column smart_processor_plugins.type
     *
     * @mbg.generated Tue Jun 17 19:25:00 CST 2025
     */
    private String type;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column smart_processor_plugins.corp_id
     *
     * @mbg.generated Tue Jun 17 19:25:00 CST 2025
     */
    private String corpId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column smart_processor_plugins.auth_config
     *
     * @mbg.generated Tue Jun 17 19:25:00 CST 2025
     */
    private String authConfig;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column smart_processor_plugins.content
     *
     * @mbg.generated Tue Jun 17 19:25:00 CST 2025
     */
    private String content;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column smart_processor_plugins.id
     *
     * @return the value of smart_processor_plugins.id
     *
     * @mbg.generated Tue Jun 17 19:25:00 CST 2025
     */
    public Long getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column smart_processor_plugins.id
     *
     * @param id the value for smart_processor_plugins.id
     *
     * @mbg.generated Tue Jun 17 19:25:00 CST 2025
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column smart_processor_plugins.gmt_create
     *
     * @return the value of smart_processor_plugins.gmt_create
     *
     * @mbg.generated Tue Jun 17 19:25:00 CST 2025
     */
    public Date getGmtCreate() {
        return gmtCreate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column smart_processor_plugins.gmt_create
     *
     * @param gmtCreate the value for smart_processor_plugins.gmt_create
     *
     * @mbg.generated Tue Jun 17 19:25:00 CST 2025
     */
    public void setGmtCreate(Date gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column smart_processor_plugins.gmt_modified
     *
     * @return the value of smart_processor_plugins.gmt_modified
     *
     * @mbg.generated Tue Jun 17 19:25:00 CST 2025
     */
    public Date getGmtModified() {
        return gmtModified;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column smart_processor_plugins.gmt_modified
     *
     * @param gmtModified the value for smart_processor_plugins.gmt_modified
     *
     * @mbg.generated Tue Jun 17 19:25:00 CST 2025
     */
    public void setGmtModified(Date gmtModified) {
        this.gmtModified = gmtModified;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column smart_processor_plugins.creator
     *
     * @return the value of smart_processor_plugins.creator
     *
     * @mbg.generated Tue Jun 17 19:25:00 CST 2025
     */
    public String getCreator() {
        return creator;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column smart_processor_plugins.creator
     *
     * @param creator the value for smart_processor_plugins.creator
     *
     * @mbg.generated Tue Jun 17 19:25:00 CST 2025
     */
    public void setCreator(String creator) {
        this.creator = creator == null ? null : creator.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column smart_processor_plugins.modifier
     *
     * @return the value of smart_processor_plugins.modifier
     *
     * @mbg.generated Tue Jun 17 19:25:00 CST 2025
     */
    public String getModifier() {
        return modifier;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column smart_processor_plugins.modifier
     *
     * @param modifier the value for smart_processor_plugins.modifier
     *
     * @mbg.generated Tue Jun 17 19:25:00 CST 2025
     */
    public void setModifier(String modifier) {
        this.modifier = modifier == null ? null : modifier.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column smart_processor_plugins.is_deleted
     *
     * @return the value of smart_processor_plugins.is_deleted
     *
     * @mbg.generated Tue Jun 17 19:25:00 CST 2025
     */
    public String getIsDeleted() {
        return isDeleted;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column smart_processor_plugins.is_deleted
     *
     * @param isDeleted the value for smart_processor_plugins.is_deleted
     *
     * @mbg.generated Tue Jun 17 19:25:00 CST 2025
     */
    public void setIsDeleted(String isDeleted) {
        this.isDeleted = isDeleted == null ? null : isDeleted.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column smart_processor_plugins.plugin_id
     *
     * @return the value of smart_processor_plugins.plugin_id
     *
     * @mbg.generated Tue Jun 17 19:25:00 CST 2025
     */
    public String getPluginId() {
        return pluginId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column smart_processor_plugins.plugin_id
     *
     * @param pluginId the value for smart_processor_plugins.plugin_id
     *
     * @mbg.generated Tue Jun 17 19:25:00 CST 2025
     */
    public void setPluginId(String pluginId) {
        this.pluginId = pluginId == null ? null : pluginId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column smart_processor_plugins.name
     *
     * @return the value of smart_processor_plugins.name
     *
     * @mbg.generated Tue Jun 17 19:25:00 CST 2025
     */
    public String getName() {
        return name;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column smart_processor_plugins.name
     *
     * @param name the value for smart_processor_plugins.name
     *
     * @mbg.generated Tue Jun 17 19:25:00 CST 2025
     */
    public void setName(String name) {
        this.name = name == null ? null : name.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column smart_processor_plugins.description
     *
     * @return the value of smart_processor_plugins.description
     *
     * @mbg.generated Tue Jun 17 19:25:00 CST 2025
     */
    public String getDescription() {
        return description;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column smart_processor_plugins.description
     *
     * @param description the value for smart_processor_plugins.description
     *
     * @mbg.generated Tue Jun 17 19:25:00 CST 2025
     */
    public void setDescription(String description) {
        this.description = description == null ? null : description.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column smart_processor_plugins.icon
     *
     * @return the value of smart_processor_plugins.icon
     *
     * @mbg.generated Tue Jun 17 19:25:00 CST 2025
     */
    public String getIcon() {
        return icon;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column smart_processor_plugins.icon
     *
     * @param icon the value for smart_processor_plugins.icon
     *
     * @mbg.generated Tue Jun 17 19:25:00 CST 2025
     */
    public void setIcon(String icon) {
        this.icon = icon == null ? null : icon.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column smart_processor_plugins.type
     *
     * @return the value of smart_processor_plugins.type
     *
     * @mbg.generated Tue Jun 17 19:25:00 CST 2025
     */
    public String getType() {
        return type;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column smart_processor_plugins.type
     *
     * @param type the value for smart_processor_plugins.type
     *
     * @mbg.generated Tue Jun 17 19:25:00 CST 2025
     */
    public void setType(String type) {
        this.type = type == null ? null : type.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column smart_processor_plugins.corp_id
     *
     * @return the value of smart_processor_plugins.corp_id
     *
     * @mbg.generated Tue Jun 17 19:25:00 CST 2025
     */
    public String getCorpId() {
        return corpId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column smart_processor_plugins.corp_id
     *
     * @param corpId the value for smart_processor_plugins.corp_id
     *
     * @mbg.generated Tue Jun 17 19:25:00 CST 2025
     */
    public void setCorpId(String corpId) {
        this.corpId = corpId == null ? null : corpId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column smart_processor_plugins.auth_config
     *
     * @return the value of smart_processor_plugins.auth_config
     *
     * @mbg.generated Tue Jun 17 19:25:00 CST 2025
     */
    public String getAuthConfig() {
        return authConfig;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column smart_processor_plugins.auth_config
     *
     * @param authConfig the value for smart_processor_plugins.auth_config
     *
     * @mbg.generated Tue Jun 17 19:25:00 CST 2025
     */
    public void setAuthConfig(String authConfig) {
        this.authConfig = authConfig == null ? null : authConfig.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column smart_processor_plugins.content
     *
     * @return the value of smart_processor_plugins.content
     *
     * @mbg.generated Tue Jun 17 19:25:00 CST 2025
     */
    public String getContent() {
        return content;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column smart_processor_plugins.content
     *
     * @param content the value for smart_processor_plugins.content
     *
     * @mbg.generated Tue Jun 17 19:25:00 CST 2025
     */
    public void setContent(String content) {
        this.content = content == null ? null : content.trim();
    }
}