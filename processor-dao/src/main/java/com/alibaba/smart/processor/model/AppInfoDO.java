package com.alibaba.smart.processor.model;

import java.util.Date;

public class AppInfoDO {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column smart_processor_app_info.id
     *
     * @mbg.generated Fri May 23 15:27:49 CST 2025
     */
    private Long id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column smart_processor_app_info.gmt_create
     *
     * @mbg.generated Fri May 23 15:27:49 CST 2025
     */
    private Date gmtCreate;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column smart_processor_app_info.gmt_modified
     *
     * @mbg.generated Fri May 23 15:27:49 CST 2025
     */
    private Date gmtModified;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column smart_processor_app_info.creator
     *
     * @mbg.generated Fri May 23 15:27:49 CST 2025
     */
    private String creator;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column smart_processor_app_info.modifier
     *
     * @mbg.generated Fri May 23 15:27:49 CST 2025
     */
    private String modifier;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column smart_processor_app_info.is_deleted
     *
     * @mbg.generated Fri May 23 15:27:49 CST 2025
     */
    private String isDeleted;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column smart_processor_app_info.app_id
     *
     * @mbg.generated Fri May 23 15:27:49 CST 2025
     */
    private String appId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column smart_processor_app_info.app_name
     *
     * @mbg.generated Fri May 23 15:27:49 CST 2025
     */
    private String appName;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column smart_processor_app_info.app_desc
     *
     * @mbg.generated Fri May 23 15:27:49 CST 2025
     */
    private String appDesc;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column smart_processor_app_info.app_icon
     *
     * @mbg.generated Fri May 23 15:27:49 CST 2025
     */
    private String appIcon;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column smart_processor_app_info.session_id
     *
     * @mbg.generated Fri May 23 15:27:49 CST 2025
     */
    private String sessionId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column smart_processor_app_info.status
     *
     * @mbg.generated Fri May 23 15:27:49 CST 2025
     */
    private String status;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column smart_processor_app_info.corp_id
     *
     * @mbg.generated Fri May 23 15:27:49 CST 2025
     */
    private String corpId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column smart_processor_app_info.app_config
     *
     * @mbg.generated Fri May 23 15:27:49 CST 2025
     */
    private String appConfig;

    /**
     * 应用版本
     */
    private String version;

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column smart_processor_app_info.id
     *
     * @return the value of smart_processor_app_info.id
     *
     * @mbg.generated Fri May 23 15:27:49 CST 2025
     */
    public Long getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column smart_processor_app_info.id
     *
     * @param id the value for smart_processor_app_info.id
     *
     * @mbg.generated Fri May 23 15:27:49 CST 2025
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column smart_processor_app_info.gmt_create
     *
     * @return the value of smart_processor_app_info.gmt_create
     *
     * @mbg.generated Fri May 23 15:27:49 CST 2025
     */
    public Date getGmtCreate() {
        return gmtCreate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column smart_processor_app_info.gmt_create
     *
     * @param gmtCreate the value for smart_processor_app_info.gmt_create
     *
     * @mbg.generated Fri May 23 15:27:49 CST 2025
     */
    public void setGmtCreate(Date gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column smart_processor_app_info.gmt_modified
     *
     * @return the value of smart_processor_app_info.gmt_modified
     *
     * @mbg.generated Fri May 23 15:27:49 CST 2025
     */
    public Date getGmtModified() {
        return gmtModified;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column smart_processor_app_info.gmt_modified
     *
     * @param gmtModified the value for smart_processor_app_info.gmt_modified
     *
     * @mbg.generated Fri May 23 15:27:49 CST 2025
     */
    public void setGmtModified(Date gmtModified) {
        this.gmtModified = gmtModified;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column smart_processor_app_info.creator
     *
     * @return the value of smart_processor_app_info.creator
     *
     * @mbg.generated Fri May 23 15:27:49 CST 2025
     */
    public String getCreator() {
        return creator;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column smart_processor_app_info.creator
     *
     * @param creator the value for smart_processor_app_info.creator
     *
     * @mbg.generated Fri May 23 15:27:49 CST 2025
     */
    public void setCreator(String creator) {
        this.creator = creator == null ? null : creator.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column smart_processor_app_info.modifier
     *
     * @return the value of smart_processor_app_info.modifier
     *
     * @mbg.generated Fri May 23 15:27:49 CST 2025
     */
    public String getModifier() {
        return modifier;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column smart_processor_app_info.modifier
     *
     * @param modifier the value for smart_processor_app_info.modifier
     *
     * @mbg.generated Fri May 23 15:27:49 CST 2025
     */
    public void setModifier(String modifier) {
        this.modifier = modifier == null ? null : modifier.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column smart_processor_app_info.is_deleted
     *
     * @return the value of smart_processor_app_info.is_deleted
     *
     * @mbg.generated Fri May 23 15:27:49 CST 2025
     */
    public String getIsDeleted() {
        return isDeleted;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column smart_processor_app_info.is_deleted
     *
     * @param isDeleted the value for smart_processor_app_info.is_deleted
     *
     * @mbg.generated Fri May 23 15:27:49 CST 2025
     */
    public void setIsDeleted(String isDeleted) {
        this.isDeleted = isDeleted == null ? null : isDeleted.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column smart_processor_app_info.app_id
     *
     * @return the value of smart_processor_app_info.app_id
     *
     * @mbg.generated Fri May 23 15:27:49 CST 2025
     */
    public String getAppId() {
        return appId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column smart_processor_app_info.app_id
     *
     * @param appId the value for smart_processor_app_info.app_id
     *
     * @mbg.generated Fri May 23 15:27:49 CST 2025
     */
    public void setAppId(String appId) {
        this.appId = appId == null ? null : appId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column smart_processor_app_info.app_name
     *
     * @return the value of smart_processor_app_info.app_name
     *
     * @mbg.generated Fri May 23 15:27:49 CST 2025
     */
    public String getAppName() {
        return appName;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column smart_processor_app_info.app_name
     *
     * @param appName the value for smart_processor_app_info.app_name
     *
     * @mbg.generated Fri May 23 15:27:49 CST 2025
     */
    public void setAppName(String appName) {
        this.appName = appName == null ? null : appName.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column smart_processor_app_info.app_desc
     *
     * @return the value of smart_processor_app_info.app_desc
     *
     * @mbg.generated Fri May 23 15:27:49 CST 2025
     */
    public String getAppDesc() {
        return appDesc;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column smart_processor_app_info.app_desc
     *
     * @param appDesc the value for smart_processor_app_info.app_desc
     *
     * @mbg.generated Fri May 23 15:27:49 CST 2025
     */
    public void setAppDesc(String appDesc) {
        this.appDesc = appDesc == null ? null : appDesc.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column smart_processor_app_info.app_icon
     *
     * @return the value of smart_processor_app_info.app_icon
     *
     * @mbg.generated Fri May 23 15:27:49 CST 2025
     */
    public String getAppIcon() {
        return appIcon;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column smart_processor_app_info.app_icon
     *
     * @param appIcon the value for smart_processor_app_info.app_icon
     *
     * @mbg.generated Fri May 23 15:27:49 CST 2025
     */
    public void setAppIcon(String appIcon) {
        this.appIcon = appIcon == null ? null : appIcon.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column smart_processor_app_info.session_id
     *
     * @return the value of smart_processor_app_info.session_id
     *
     * @mbg.generated Fri May 23 15:27:49 CST 2025
     */
    public String getSessionId() {
        return sessionId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column smart_processor_app_info.session_id
     *
     * @param sessionId the value for smart_processor_app_info.session_id
     *
     * @mbg.generated Fri May 23 15:27:49 CST 2025
     */
    public void setSessionId(String sessionId) {
        this.sessionId = sessionId == null ? null : sessionId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column smart_processor_app_info.status
     *
     * @return the value of smart_processor_app_info.status
     *
     * @mbg.generated Fri May 23 15:27:49 CST 2025
     */
    public String getStatus() {
        return status;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column smart_processor_app_info.status
     *
     * @param status the value for smart_processor_app_info.status
     *
     * @mbg.generated Fri May 23 15:27:49 CST 2025
     */
    public void setStatus(String status) {
        this.status = status == null ? null : status.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column smart_processor_app_info.corp_id
     *
     * @return the value of smart_processor_app_info.corp_id
     *
     * @mbg.generated Fri May 23 15:27:49 CST 2025
     */
    public String getCorpId() {
        return corpId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column smart_processor_app_info.corp_id
     *
     * @param corpId the value for smart_processor_app_info.corp_id
     *
     * @mbg.generated Fri May 23 15:27:49 CST 2025
     */
    public void setCorpId(String corpId) {
        this.corpId = corpId == null ? null : corpId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column smart_processor_app_info.app_config
     *
     * @return the value of smart_processor_app_info.app_config
     *
     * @mbg.generated Fri May 23 15:27:49 CST 2025
     */
    public String getAppConfig() {
        return appConfig;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column smart_processor_app_info.app_config
     *
     * @param appConfig the value for smart_processor_app_info.app_config
     *
     * @mbg.generated Fri May 23 15:27:49 CST 2025
     */
    public void setAppConfig(String appConfig) {
        this.appConfig = appConfig == null ? null : appConfig.trim();
    }
}