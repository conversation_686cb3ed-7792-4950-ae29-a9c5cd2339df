package com.alibaba.smart.processor.mapper;

import com.alibaba.smart.processor.model.AppVersionDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface AppVersionExtMapper extends AppVersionMapper {

    List<AppVersionDO> listAppVersion(@Param("appId") String appId,
                                      @Param("pageIndex") Integer pageIndex,
                                      @Param("pageSize") Integer pageSize,
                                      @Param("key") String key);

    long countAppVersion(@Param("appId") String appId,@Param("key") String key);
}