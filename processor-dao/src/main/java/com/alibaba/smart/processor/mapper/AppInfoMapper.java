package com.alibaba.smart.processor.mapper;

import com.alibaba.smart.processor.model.AppInfoDO;
import com.alibaba.smart.processor.model.AppInfoDOExample;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.session.RowBounds;

@Mapper
public interface AppInfoMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table smart_processor_app_info
     *
     * @mbg.generated Fri May 23 15:27:49 CST 2025
     */
    long countByExample(AppInfoDOExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table smart_processor_app_info
     *
     * @mbg.generated Fri May 23 15:27:49 CST 2025
     */
    int insert(AppInfoDO row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table smart_processor_app_info
     *
     * @mbg.generated Fri May 23 15:27:49 CST 2025
     */
    int insertSelective(AppInfoDO row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table smart_processor_app_info
     *
     * @mbg.generated Fri May 23 15:27:49 CST 2025
     */
    List<AppInfoDO> selectByExampleWithBLOBsWithRowbounds(AppInfoDOExample example, RowBounds rowBounds);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table smart_processor_app_info
     *
     * @mbg.generated Fri May 23 15:27:49 CST 2025
     */
    List<AppInfoDO> selectByExampleWithBLOBs(AppInfoDOExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table smart_processor_app_info
     *
     * @mbg.generated Fri May 23 15:27:49 CST 2025
     */
    List<AppInfoDO> selectByExampleWithRowbounds(AppInfoDOExample example, RowBounds rowBounds);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table smart_processor_app_info
     *
     * @mbg.generated Fri May 23 15:27:49 CST 2025
     */
    List<AppInfoDO> selectByExample(AppInfoDOExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table smart_processor_app_info
     *
     * @mbg.generated Fri May 23 15:27:49 CST 2025
     */
    AppInfoDO selectByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table smart_processor_app_info
     *
     * @mbg.generated Fri May 23 15:27:49 CST 2025
     */
    int updateByExampleSelective(@Param("row") AppInfoDO row, @Param("example") AppInfoDOExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table smart_processor_app_info
     *
     * @mbg.generated Fri May 23 15:27:49 CST 2025
     */
    int updateByExampleWithBLOBs(@Param("row") AppInfoDO row, @Param("example") AppInfoDOExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table smart_processor_app_info
     *
     * @mbg.generated Fri May 23 15:27:49 CST 2025
     */
    int updateByExample(@Param("row") AppInfoDO row, @Param("example") AppInfoDOExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table smart_processor_app_info
     *
     * @mbg.generated Fri May 23 15:27:49 CST 2025
     */
    int updateByPrimaryKeySelective(AppInfoDO row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table smart_processor_app_info
     *
     * @mbg.generated Fri May 23 15:27:49 CST 2025
     */
    int updateByPrimaryKeyWithBLOBs(AppInfoDO row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table smart_processor_app_info
     *
     * @mbg.generated Fri May 23 15:27:49 CST 2025
     */
    int updateByPrimaryKey(AppInfoDO row);
}