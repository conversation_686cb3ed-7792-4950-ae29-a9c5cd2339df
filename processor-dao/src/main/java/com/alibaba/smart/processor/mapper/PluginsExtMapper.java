package com.alibaba.smart.processor.mapper;

import com.alibaba.smart.processor.model.PluginsDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface PluginsExtMapper extends PluginsMapper{

    List<PluginsDO> listPlugins(@Param("corpId") String corpId,
                                @Param("key") String key,
                                @Param("pageIndex") Integer pageIndex,
                                @Param("pageSize") Integer pageSize);
    long countPlugins(@Param("corpId") String corpId, @Param("key") String key);
}