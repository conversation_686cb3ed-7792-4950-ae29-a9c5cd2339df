package com.alibaba.smart.processor.mapper;

import com.alibaba.smart.processor.model.SessionPluginBindingDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface SessionPluginBindingExtMapper extends SessionPluginBindingMapper{

    void batchInsert(List<SessionPluginBindingDO> rows);

    void batchUpdate(@Param("sessionId") String sessionId,
                     @Param("pluginIds") List<String> pluginIds,
                     @Param("modifier") String modifier);

    void deleteBySessionId(@Param("sessionId") String sessionId);
}