package com.alibaba.smart.processor.mapper;

import com.alibaba.smart.processor.model.AppVersionDO;
import com.alibaba.smart.processor.model.AppVersionDOExample;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.session.RowBounds;

@Mapper
public interface AppVersionMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table smart_processor_app_version
     *
     * @mbg.generated Tue Jun 10 20:11:55 CST 2025
     */
    long countByExample(AppVersionDOExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table smart_processor_app_version
     *
     * @mbg.generated Tue Jun 10 20:11:55 CST 2025
     */
    int insert(AppVersionDO row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table smart_processor_app_version
     *
     * @mbg.generated Tue Jun 10 20:11:55 CST 2025
     */
    int insertSelective(AppVersionDO row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table smart_processor_app_version
     *
     * @mbg.generated Tue Jun 10 20:11:55 CST 2025
     */
    List<AppVersionDO> selectByExampleWithBLOBsWithRowbounds(AppVersionDOExample example, RowBounds rowBounds);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table smart_processor_app_version
     *
     * @mbg.generated Tue Jun 10 20:11:55 CST 2025
     */
    List<AppVersionDO> selectByExampleWithBLOBs(AppVersionDOExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table smart_processor_app_version
     *
     * @mbg.generated Tue Jun 10 20:11:55 CST 2025
     */
    List<AppVersionDO> selectByExampleWithRowbounds(AppVersionDOExample example, RowBounds rowBounds);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table smart_processor_app_version
     *
     * @mbg.generated Tue Jun 10 20:11:55 CST 2025
     */
    List<AppVersionDO> selectByExample(AppVersionDOExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table smart_processor_app_version
     *
     * @mbg.generated Tue Jun 10 20:11:55 CST 2025
     */
    AppVersionDO selectByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table smart_processor_app_version
     *
     * @mbg.generated Tue Jun 10 20:11:55 CST 2025
     */
    int updateByExampleSelective(@Param("row") AppVersionDO row, @Param("example") AppVersionDOExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table smart_processor_app_version
     *
     * @mbg.generated Tue Jun 10 20:11:55 CST 2025
     */
    int updateByExampleWithBLOBs(@Param("row") AppVersionDO row, @Param("example") AppVersionDOExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table smart_processor_app_version
     *
     * @mbg.generated Tue Jun 10 20:11:55 CST 2025
     */
    int updateByExample(@Param("row") AppVersionDO row, @Param("example") AppVersionDOExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table smart_processor_app_version
     *
     * @mbg.generated Tue Jun 10 20:11:55 CST 2025
     */
    int updateByPrimaryKeySelective(AppVersionDO row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table smart_processor_app_version
     *
     * @mbg.generated Tue Jun 10 20:11:55 CST 2025
     */
    int updateByPrimaryKeyWithBLOBs(AppVersionDO row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table smart_processor_app_version
     *
     * @mbg.generated Tue Jun 10 20:11:55 CST 2025
     */
    int updateByPrimaryKey(AppVersionDO row);
}