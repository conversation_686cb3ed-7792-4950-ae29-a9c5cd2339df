package com.alibaba.smart.processor.model;

import java.util.Date;

public class SessionPluginB<PERSON>ingDO {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column smart_processor_session_plugins_binding.id
     *
     * @mbg.generated Mon Jun 16 20:06:22 CST 2025
     */
    private Long id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column smart_processor_session_plugins_binding.gmt_create
     *
     * @mbg.generated Mon Jun 16 20:06:22 CST 2025
     */
    private Date gmtCreate;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column smart_processor_session_plugins_binding.gmt_modified
     *
     * @mbg.generated Mon Jun 16 20:06:22 CST 2025
     */
    private Date gmtModified;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column smart_processor_session_plugins_binding.creator
     *
     * @mbg.generated Mon Jun 16 20:06:22 CST 2025
     */
    private String creator;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column smart_processor_session_plugins_binding.modifier
     *
     * @mbg.generated Mon Jun 16 20:06:22 CST 2025
     */
    private String modifier;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column smart_processor_session_plugins_binding.is_deleted
     *
     * @mbg.generated Mon Jun 16 20:06:22 CST 2025
     */
    private String isDeleted;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column smart_processor_session_plugins_binding.session_id
     *
     * @mbg.generated Mon Jun 16 20:06:22 CST 2025
     */
    private String sessionId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column smart_processor_session_plugins_binding.plugin_id
     *
     * @mbg.generated Mon Jun 16 20:06:22 CST 2025
     */
    private String pluginId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column smart_processor_session_plugins_binding.corp_id
     *
     * @mbg.generated Mon Jun 16 20:06:22 CST 2025
     */
    private String corpId;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column smart_processor_session_plugins_binding.id
     *
     * @return the value of smart_processor_session_plugins_binding.id
     *
     * @mbg.generated Mon Jun 16 20:06:22 CST 2025
     */
    public Long getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column smart_processor_session_plugins_binding.id
     *
     * @param id the value for smart_processor_session_plugins_binding.id
     *
     * @mbg.generated Mon Jun 16 20:06:22 CST 2025
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column smart_processor_session_plugins_binding.gmt_create
     *
     * @return the value of smart_processor_session_plugins_binding.gmt_create
     *
     * @mbg.generated Mon Jun 16 20:06:22 CST 2025
     */
    public Date getGmtCreate() {
        return gmtCreate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column smart_processor_session_plugins_binding.gmt_create
     *
     * @param gmtCreate the value for smart_processor_session_plugins_binding.gmt_create
     *
     * @mbg.generated Mon Jun 16 20:06:22 CST 2025
     */
    public void setGmtCreate(Date gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column smart_processor_session_plugins_binding.gmt_modified
     *
     * @return the value of smart_processor_session_plugins_binding.gmt_modified
     *
     * @mbg.generated Mon Jun 16 20:06:22 CST 2025
     */
    public Date getGmtModified() {
        return gmtModified;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column smart_processor_session_plugins_binding.gmt_modified
     *
     * @param gmtModified the value for smart_processor_session_plugins_binding.gmt_modified
     *
     * @mbg.generated Mon Jun 16 20:06:22 CST 2025
     */
    public void setGmtModified(Date gmtModified) {
        this.gmtModified = gmtModified;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column smart_processor_session_plugins_binding.creator
     *
     * @return the value of smart_processor_session_plugins_binding.creator
     *
     * @mbg.generated Mon Jun 16 20:06:22 CST 2025
     */
    public String getCreator() {
        return creator;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column smart_processor_session_plugins_binding.creator
     *
     * @param creator the value for smart_processor_session_plugins_binding.creator
     *
     * @mbg.generated Mon Jun 16 20:06:22 CST 2025
     */
    public void setCreator(String creator) {
        this.creator = creator == null ? null : creator.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column smart_processor_session_plugins_binding.modifier
     *
     * @return the value of smart_processor_session_plugins_binding.modifier
     *
     * @mbg.generated Mon Jun 16 20:06:22 CST 2025
     */
    public String getModifier() {
        return modifier;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column smart_processor_session_plugins_binding.modifier
     *
     * @param modifier the value for smart_processor_session_plugins_binding.modifier
     *
     * @mbg.generated Mon Jun 16 20:06:22 CST 2025
     */
    public void setModifier(String modifier) {
        this.modifier = modifier == null ? null : modifier.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column smart_processor_session_plugins_binding.is_deleted
     *
     * @return the value of smart_processor_session_plugins_binding.is_deleted
     *
     * @mbg.generated Mon Jun 16 20:06:22 CST 2025
     */
    public String getIsDeleted() {
        return isDeleted;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column smart_processor_session_plugins_binding.is_deleted
     *
     * @param isDeleted the value for smart_processor_session_plugins_binding.is_deleted
     *
     * @mbg.generated Mon Jun 16 20:06:22 CST 2025
     */
    public void setIsDeleted(String isDeleted) {
        this.isDeleted = isDeleted == null ? null : isDeleted.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column smart_processor_session_plugins_binding.session_id
     *
     * @return the value of smart_processor_session_plugins_binding.session_id
     *
     * @mbg.generated Mon Jun 16 20:06:22 CST 2025
     */
    public String getSessionId() {
        return sessionId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column smart_processor_session_plugins_binding.session_id
     *
     * @param sessionId the value for smart_processor_session_plugins_binding.session_id
     *
     * @mbg.generated Mon Jun 16 20:06:22 CST 2025
     */
    public void setSessionId(String sessionId) {
        this.sessionId = sessionId == null ? null : sessionId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column smart_processor_session_plugins_binding.plugin_id
     *
     * @return the value of smart_processor_session_plugins_binding.plugin_id
     *
     * @mbg.generated Mon Jun 16 20:06:22 CST 2025
     */
    public String getPluginId() {
        return pluginId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column smart_processor_session_plugins_binding.plugin_id
     *
     * @param pluginId the value for smart_processor_session_plugins_binding.plugin_id
     *
     * @mbg.generated Mon Jun 16 20:06:22 CST 2025
     */
    public void setPluginId(String pluginId) {
        this.pluginId = pluginId == null ? null : pluginId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column smart_processor_session_plugins_binding.corp_id
     *
     * @return the value of smart_processor_session_plugins_binding.corp_id
     *
     * @mbg.generated Mon Jun 16 20:06:22 CST 2025
     */
    public String getCorpId() {
        return corpId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column smart_processor_session_plugins_binding.corp_id
     *
     * @param corpId the value for smart_processor_session_plugins_binding.corp_id
     *
     * @mbg.generated Mon Jun 16 20:06:22 CST 2025
     */
    public void setCorpId(String corpId) {
        this.corpId = corpId == null ? null : corpId.trim();
    }
}