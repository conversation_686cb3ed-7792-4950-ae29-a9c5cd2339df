package com.alibaba.smart.processor.mapper;

import com.alibaba.smart.processor.model.ChatMessageHistoryDO;
import com.alibaba.smart.processor.model.ChatMessageHistoryDOExample;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.session.RowBounds;

import java.util.List;
@Mapper
public interface ChatMessageHistoryMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table smart_processor_chat_message_history
     *
     * @mbg.generated Tue May 20 15:31:42 CST 2025
     */
    long countByExample(ChatMessageHistoryDOExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table smart_processor_chat_message_history
     *
     * @mbg.generated Tue May 20 15:31:42 CST 2025
     */
    int insert(ChatMessageHistoryDO row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table smart_processor_chat_message_history
     *
     * @mbg.generated Tue May 20 15:31:42 CST 2025
     */
    int insertSelective(ChatMessageHistoryDO row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table smart_processor_chat_message_history
     *
     * @mbg.generated Tue May 20 15:31:42 CST 2025
     */
    List<ChatMessageHistoryDO> selectByExampleWithBLOBsWithRowbounds(ChatMessageHistoryDOExample example, RowBounds rowBounds);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table smart_processor_chat_message_history
     *
     * @mbg.generated Tue May 20 15:31:42 CST 2025
     */
    List<ChatMessageHistoryDO> selectByExampleWithBLOBs(ChatMessageHistoryDOExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table smart_processor_chat_message_history
     *
     * @mbg.generated Tue May 20 15:31:42 CST 2025
     */
    List<ChatMessageHistoryDO> selectByExampleWithRowbounds(ChatMessageHistoryDOExample example, RowBounds rowBounds);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table smart_processor_chat_message_history
     *
     * @mbg.generated Tue May 20 15:31:42 CST 2025
     */
    List<ChatMessageHistoryDO> selectByExample(ChatMessageHistoryDOExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table smart_processor_chat_message_history
     *
     * @mbg.generated Tue May 20 15:31:42 CST 2025
     */
    ChatMessageHistoryDO selectByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table smart_processor_chat_message_history
     *
     * @mbg.generated Tue May 20 15:31:42 CST 2025
     */
    int updateByExampleSelective(@Param("row") ChatMessageHistoryDO row, @Param("example") ChatMessageHistoryDOExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table smart_processor_chat_message_history
     *
     * @mbg.generated Tue May 20 15:31:42 CST 2025
     */
    int updateByExampleWithBLOBs(@Param("row") ChatMessageHistoryDO row, @Param("example") ChatMessageHistoryDOExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table smart_processor_chat_message_history
     *
     * @mbg.generated Tue May 20 15:31:42 CST 2025
     */
    int updateByExample(@Param("row") ChatMessageHistoryDO row, @Param("example") ChatMessageHistoryDOExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table smart_processor_chat_message_history
     *
     * @mbg.generated Tue May 20 15:31:42 CST 2025
     */
    int updateByPrimaryKeySelective(ChatMessageHistoryDO row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table smart_processor_chat_message_history
     *
     * @mbg.generated Tue May 20 15:31:42 CST 2025
     */
    int updateByPrimaryKeyWithBLOBs(ChatMessageHistoryDO row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table smart_processor_chat_message_history
     *
     * @mbg.generated Tue May 20 15:31:42 CST 2025
     */
    int updateByPrimaryKey(ChatMessageHistoryDO row);
}