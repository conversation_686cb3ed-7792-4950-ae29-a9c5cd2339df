package com.alibaba.smart.processor.mapper;

import com.alibaba.smart.processor.model.PluginsDO;
import com.alibaba.smart.processor.model.PluginsDOExample;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.session.RowBounds;

@Mapper
public interface PluginsMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table smart_processor_plugins
     *
     * @mbg.generated Tue Jun 17 19:25:00 CST 2025
     */
    long countByExample(PluginsDOExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table smart_processor_plugins
     *
     * @mbg.generated Tue Jun 17 19:25:00 CST 2025
     */
    int insert(PluginsDO row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table smart_processor_plugins
     *
     * @mbg.generated Tue Jun 17 19:25:00 CST 2025
     */
    int insertSelective(PluginsDO row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table smart_processor_plugins
     *
     * @mbg.generated Tue Jun 17 19:25:00 CST 2025
     */
    List<PluginsDO> selectByExampleWithBLOBsWithRowbounds(PluginsDOExample example, RowBounds rowBounds);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table smart_processor_plugins
     *
     * @mbg.generated Tue Jun 17 19:25:00 CST 2025
     */
    List<PluginsDO> selectByExampleWithBLOBs(PluginsDOExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table smart_processor_plugins
     *
     * @mbg.generated Tue Jun 17 19:25:00 CST 2025
     */
    List<PluginsDO> selectByExampleWithRowbounds(PluginsDOExample example, RowBounds rowBounds);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table smart_processor_plugins
     *
     * @mbg.generated Tue Jun 17 19:25:00 CST 2025
     */
    List<PluginsDO> selectByExample(PluginsDOExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table smart_processor_plugins
     *
     * @mbg.generated Tue Jun 17 19:25:00 CST 2025
     */
    PluginsDO selectByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table smart_processor_plugins
     *
     * @mbg.generated Tue Jun 17 19:25:00 CST 2025
     */
    int updateByExampleSelective(@Param("row") PluginsDO row, @Param("example") PluginsDOExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table smart_processor_plugins
     *
     * @mbg.generated Tue Jun 17 19:25:00 CST 2025
     */
    int updateByExampleWithBLOBs(@Param("row") PluginsDO row, @Param("example") PluginsDOExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table smart_processor_plugins
     *
     * @mbg.generated Tue Jun 17 19:25:00 CST 2025
     */
    int updateByExample(@Param("row") PluginsDO row, @Param("example") PluginsDOExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table smart_processor_plugins
     *
     * @mbg.generated Tue Jun 17 19:25:00 CST 2025
     */
    int updateByPrimaryKeySelective(PluginsDO row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table smart_processor_plugins
     *
     * @mbg.generated Tue Jun 17 19:25:00 CST 2025
     */
    int updateByPrimaryKeyWithBLOBs(PluginsDO row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table smart_processor_plugins
     *
     * @mbg.generated Tue Jun 17 19:25:00 CST 2025
     */
    int updateByPrimaryKey(PluginsDO row);
}