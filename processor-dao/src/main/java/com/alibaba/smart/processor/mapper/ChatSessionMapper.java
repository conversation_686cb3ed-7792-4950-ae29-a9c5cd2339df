package com.alibaba.smart.processor.mapper;

import com.alibaba.smart.processor.model.ChatSessionDO;
import com.alibaba.smart.processor.model.ChatSessionDOExample;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.session.RowBounds;

import java.util.List;

@Mapper
public interface ChatSessionMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table smart_processor_chat_session
     *
     * @mbg.generated Tue May 20 15:31:42 CST 2025
     */
    long countByExample(ChatSessionDOExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table smart_processor_chat_session
     *
     * @mbg.generated Tue May 20 15:31:42 CST 2025
     */
    int insert(ChatSessionDO row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table smart_processor_chat_session
     *
     * @mbg.generated Tue May 20 15:31:42 CST 2025
     */
    int insertSelective(ChatSessionDO row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table smart_processor_chat_session
     *
     * @mbg.generated Tue May 20 15:31:42 CST 2025
     */
    List<ChatSessionDO> selectByExampleWithRowbounds(ChatSessionDOExample example, RowBounds rowBounds);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table smart_processor_chat_session
     *
     * @mbg.generated Tue May 20 15:31:42 CST 2025
     */
    List<ChatSessionDO> selectByExample(ChatSessionDOExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table smart_processor_chat_session
     *
     * @mbg.generated Tue May 20 15:31:42 CST 2025
     */
    ChatSessionDO selectByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table smart_processor_chat_session
     *
     * @mbg.generated Tue May 20 15:31:42 CST 2025
     */
    int updateByExampleSelective(@Param("row") ChatSessionDO row, @Param("example") ChatSessionDOExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table smart_processor_chat_session
     *
     * @mbg.generated Tue May 20 15:31:42 CST 2025
     */
    int updateByExample(@Param("row") ChatSessionDO row, @Param("example") ChatSessionDOExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table smart_processor_chat_session
     *
     * @mbg.generated Tue May 20 15:31:42 CST 2025
     */
    int updateByPrimaryKeySelective(ChatSessionDO row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table smart_processor_chat_session
     *
     * @mbg.generated Tue May 20 15:31:42 CST 2025
     */
    int updateByPrimaryKey(ChatSessionDO row);
}