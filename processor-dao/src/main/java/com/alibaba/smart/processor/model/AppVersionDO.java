package com.alibaba.smart.processor.model;

import java.util.Date;

public class AppVersionDO {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column smart_processor_app_version.id
     *
     * @mbg.generated Tue Jun 10 20:11:55 CST 2025
     */
    private Long id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column smart_processor_app_version.gmt_create
     *
     * @mbg.generated Tue Jun 10 20:11:55 CST 2025
     */
    private Date gmtCreate;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column smart_processor_app_version.gmt_modified
     *
     * @mbg.generated Tue Jun 10 20:11:55 CST 2025
     */
    private Date gmtModified;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column smart_processor_app_version.creator
     *
     * @mbg.generated Tue Jun 10 20:11:55 CST 2025
     */
    private String creator;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column smart_processor_app_version.modifier
     *
     * @mbg.generated Tue Jun 10 20:11:55 CST 2025
     */
    private String modifier;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column smart_processor_app_version.is_deleted
     *
     * @mbg.generated Tue Jun 10 20:11:55 CST 2025
     */
    private String isDeleted;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column smart_processor_app_version.app_id
     *
     * @mbg.generated Tue Jun 10 20:11:55 CST 2025
     */
    private String appId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column smart_processor_app_version.version
     *
     * @mbg.generated Tue Jun 10 20:11:55 CST 2025
     */
    private String version;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column smart_processor_app_version.resource_id
     *
     * @mbg.generated Tue Jun 10 20:11:55 CST 2025
     */
    private String resourceId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column smart_processor_app_version.status
     *
     * @mbg.generated Tue Jun 10 20:11:55 CST 2025
     */
    private String status;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column smart_processor_app_version.update_note
     *
     * @mbg.generated Tue Jun 10 20:11:55 CST 2025
     */
    private String updateNote;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column smart_processor_app_version.id
     *
     * @return the value of smart_processor_app_version.id
     *
     * @mbg.generated Tue Jun 10 20:11:55 CST 2025
     */
    public Long getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column smart_processor_app_version.id
     *
     * @param id the value for smart_processor_app_version.id
     *
     * @mbg.generated Tue Jun 10 20:11:55 CST 2025
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column smart_processor_app_version.gmt_create
     *
     * @return the value of smart_processor_app_version.gmt_create
     *
     * @mbg.generated Tue Jun 10 20:11:55 CST 2025
     */
    public Date getGmtCreate() {
        return gmtCreate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column smart_processor_app_version.gmt_create
     *
     * @param gmtCreate the value for smart_processor_app_version.gmt_create
     *
     * @mbg.generated Tue Jun 10 20:11:55 CST 2025
     */
    public void setGmtCreate(Date gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column smart_processor_app_version.gmt_modified
     *
     * @return the value of smart_processor_app_version.gmt_modified
     *
     * @mbg.generated Tue Jun 10 20:11:55 CST 2025
     */
    public Date getGmtModified() {
        return gmtModified;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column smart_processor_app_version.gmt_modified
     *
     * @param gmtModified the value for smart_processor_app_version.gmt_modified
     *
     * @mbg.generated Tue Jun 10 20:11:55 CST 2025
     */
    public void setGmtModified(Date gmtModified) {
        this.gmtModified = gmtModified;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column smart_processor_app_version.creator
     *
     * @return the value of smart_processor_app_version.creator
     *
     * @mbg.generated Tue Jun 10 20:11:55 CST 2025
     */
    public String getCreator() {
        return creator;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column smart_processor_app_version.creator
     *
     * @param creator the value for smart_processor_app_version.creator
     *
     * @mbg.generated Tue Jun 10 20:11:55 CST 2025
     */
    public void setCreator(String creator) {
        this.creator = creator == null ? null : creator.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column smart_processor_app_version.modifier
     *
     * @return the value of smart_processor_app_version.modifier
     *
     * @mbg.generated Tue Jun 10 20:11:55 CST 2025
     */
    public String getModifier() {
        return modifier;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column smart_processor_app_version.modifier
     *
     * @param modifier the value for smart_processor_app_version.modifier
     *
     * @mbg.generated Tue Jun 10 20:11:55 CST 2025
     */
    public void setModifier(String modifier) {
        this.modifier = modifier == null ? null : modifier.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column smart_processor_app_version.is_deleted
     *
     * @return the value of smart_processor_app_version.is_deleted
     *
     * @mbg.generated Tue Jun 10 20:11:55 CST 2025
     */
    public String getIsDeleted() {
        return isDeleted;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column smart_processor_app_version.is_deleted
     *
     * @param isDeleted the value for smart_processor_app_version.is_deleted
     *
     * @mbg.generated Tue Jun 10 20:11:55 CST 2025
     */
    public void setIsDeleted(String isDeleted) {
        this.isDeleted = isDeleted == null ? null : isDeleted.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column smart_processor_app_version.app_id
     *
     * @return the value of smart_processor_app_version.app_id
     *
     * @mbg.generated Tue Jun 10 20:11:55 CST 2025
     */
    public String getAppId() {
        return appId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column smart_processor_app_version.app_id
     *
     * @param appId the value for smart_processor_app_version.app_id
     *
     * @mbg.generated Tue Jun 10 20:11:55 CST 2025
     */
    public void setAppId(String appId) {
        this.appId = appId == null ? null : appId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column smart_processor_app_version.version
     *
     * @return the value of smart_processor_app_version.version
     *
     * @mbg.generated Tue Jun 10 20:11:55 CST 2025
     */
    public String getVersion() {
        return version;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column smart_processor_app_version.version
     *
     * @param version the value for smart_processor_app_version.version
     *
     * @mbg.generated Tue Jun 10 20:11:55 CST 2025
     */
    public void setVersion(String version) {
        this.version = version == null ? null : version.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column smart_processor_app_version.resource_id
     *
     * @return the value of smart_processor_app_version.resource_id
     *
     * @mbg.generated Tue Jun 10 20:11:55 CST 2025
     */
    public String getResourceId() {
        return resourceId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column smart_processor_app_version.resource_id
     *
     * @param resourceId the value for smart_processor_app_version.resource_id
     *
     * @mbg.generated Tue Jun 10 20:11:55 CST 2025
     */
    public void setResourceId(String resourceId) {
        this.resourceId = resourceId == null ? null : resourceId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column smart_processor_app_version.status
     *
     * @return the value of smart_processor_app_version.status
     *
     * @mbg.generated Tue Jun 10 20:11:55 CST 2025
     */
    public String getStatus() {
        return status;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column smart_processor_app_version.status
     *
     * @param status the value for smart_processor_app_version.status
     *
     * @mbg.generated Tue Jun 10 20:11:55 CST 2025
     */
    public void setStatus(String status) {
        this.status = status == null ? null : status.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column smart_processor_app_version.update_note
     *
     * @return the value of smart_processor_app_version.update_note
     *
     * @mbg.generated Tue Jun 10 20:11:55 CST 2025
     */
    public String getUpdateNote() {
        return updateNote;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column smart_processor_app_version.update_note
     *
     * @param updateNote the value for smart_processor_app_version.update_note
     *
     * @mbg.generated Tue Jun 10 20:11:55 CST 2025
     */
    public void setUpdateNote(String updateNote) {
        this.updateNote = updateNote == null ? null : updateNote.trim();
    }
}