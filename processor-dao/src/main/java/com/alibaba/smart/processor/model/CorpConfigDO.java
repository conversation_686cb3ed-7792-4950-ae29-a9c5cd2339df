package com.alibaba.smart.processor.model;

import java.util.Date;

public class CorpConfigDO {
    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column smart_processor_corp_config.id
     *
     * @mbg.generated Wed Jun 04 15:03:09 CST 2025
     */
    private Long id;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column smart_processor_corp_config.gmt_create
     *
     * @mbg.generated Wed Jun 04 15:03:09 CST 2025
     */
    private Date gmtCreate;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column smart_processor_corp_config.gmt_modified
     *
     * @mbg.generated Wed Jun 04 15:03:09 CST 2025
     */
    private Date gmtModified;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column smart_processor_corp_config.creator
     *
     * @mbg.generated Wed Jun 04 15:03:09 CST 2025
     */
    private String creator;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column smart_processor_corp_config.modifier
     *
     * @mbg.generated Wed Jun 04 15:03:09 CST 2025
     */
    private String modifier;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column smart_processor_corp_config.is_deleted
     *
     * @mbg.generated Wed Jun 04 15:03:09 CST 2025
     */
    private String isDeleted;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column smart_processor_corp_config.corp_id
     *
     * @mbg.generated Wed Jun 04 15:03:09 CST 2025
     */
    private String corpId;

    /**
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column smart_processor_corp_config.config
     *
     * @mbg.generated Wed Jun 04 15:03:09 CST 2025
     */
    private String config;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column smart_processor_corp_config.id
     *
     * @return the value of smart_processor_corp_config.id
     *
     * @mbg.generated Wed Jun 04 15:03:09 CST 2025
     */
    public Long getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column smart_processor_corp_config.id
     *
     * @param id the value for smart_processor_corp_config.id
     *
     * @mbg.generated Wed Jun 04 15:03:09 CST 2025
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column smart_processor_corp_config.gmt_create
     *
     * @return the value of smart_processor_corp_config.gmt_create
     *
     * @mbg.generated Wed Jun 04 15:03:09 CST 2025
     */
    public Date getGmtCreate() {
        return gmtCreate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column smart_processor_corp_config.gmt_create
     *
     * @param gmtCreate the value for smart_processor_corp_config.gmt_create
     *
     * @mbg.generated Wed Jun 04 15:03:09 CST 2025
     */
    public void setGmtCreate(Date gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column smart_processor_corp_config.gmt_modified
     *
     * @return the value of smart_processor_corp_config.gmt_modified
     *
     * @mbg.generated Wed Jun 04 15:03:09 CST 2025
     */
    public Date getGmtModified() {
        return gmtModified;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column smart_processor_corp_config.gmt_modified
     *
     * @param gmtModified the value for smart_processor_corp_config.gmt_modified
     *
     * @mbg.generated Wed Jun 04 15:03:09 CST 2025
     */
    public void setGmtModified(Date gmtModified) {
        this.gmtModified = gmtModified;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column smart_processor_corp_config.creator
     *
     * @return the value of smart_processor_corp_config.creator
     *
     * @mbg.generated Wed Jun 04 15:03:09 CST 2025
     */
    public String getCreator() {
        return creator;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column smart_processor_corp_config.creator
     *
     * @param creator the value for smart_processor_corp_config.creator
     *
     * @mbg.generated Wed Jun 04 15:03:09 CST 2025
     */
    public void setCreator(String creator) {
        this.creator = creator == null ? null : creator.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column smart_processor_corp_config.modifier
     *
     * @return the value of smart_processor_corp_config.modifier
     *
     * @mbg.generated Wed Jun 04 15:03:09 CST 2025
     */
    public String getModifier() {
        return modifier;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column smart_processor_corp_config.modifier
     *
     * @param modifier the value for smart_processor_corp_config.modifier
     *
     * @mbg.generated Wed Jun 04 15:03:09 CST 2025
     */
    public void setModifier(String modifier) {
        this.modifier = modifier == null ? null : modifier.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column smart_processor_corp_config.is_deleted
     *
     * @return the value of smart_processor_corp_config.is_deleted
     *
     * @mbg.generated Wed Jun 04 15:03:09 CST 2025
     */
    public String getIsDeleted() {
        return isDeleted;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column smart_processor_corp_config.is_deleted
     *
     * @param isDeleted the value for smart_processor_corp_config.is_deleted
     *
     * @mbg.generated Wed Jun 04 15:03:09 CST 2025
     */
    public void setIsDeleted(String isDeleted) {
        this.isDeleted = isDeleted == null ? null : isDeleted.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column smart_processor_corp_config.corp_id
     *
     * @return the value of smart_processor_corp_config.corp_id
     *
     * @mbg.generated Wed Jun 04 15:03:09 CST 2025
     */
    public String getCorpId() {
        return corpId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column smart_processor_corp_config.corp_id
     *
     * @param corpId the value for smart_processor_corp_config.corp_id
     *
     * @mbg.generated Wed Jun 04 15:03:09 CST 2025
     */
    public void setCorpId(String corpId) {
        this.corpId = corpId == null ? null : corpId.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column smart_processor_corp_config.config
     *
     * @return the value of smart_processor_corp_config.config
     *
     * @mbg.generated Wed Jun 04 15:03:09 CST 2025
     */
    public String getConfig() {
        return config;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column smart_processor_corp_config.config
     *
     * @param config the value for smart_processor_corp_config.config
     *
     * @mbg.generated Wed Jun 04 15:03:09 CST 2025
     */
    public void setConfig(String config) {
        this.config = config == null ? null : config.trim();
    }
}