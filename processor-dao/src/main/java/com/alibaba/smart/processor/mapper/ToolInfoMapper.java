package com.alibaba.smart.processor.mapper;

import com.alibaba.smart.processor.model.ToolInfoDO;
import com.alibaba.smart.processor.model.ToolInfoDOExample;
import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.session.RowBounds;

@Mapper
public interface ToolInfoMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table smart_processor_tool_info
     *
     * @mbg.generated Thu May 15 19:11:45 CST 2025
     */
    long countByExample(ToolInfoDOExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table smart_processor_tool_info
     *
     * @mbg.generated Thu May 15 19:11:45 CST 2025
     */
    int insert(ToolInfoDO row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table smart_processor_tool_info
     *
     * @mbg.generated Thu May 15 19:11:45 CST 2025
     */
    int insertSelective(ToolInfoDO row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table smart_processor_tool_info
     *
     * @mbg.generated Thu May 15 19:11:45 CST 2025
     */
    List<ToolInfoDO> selectByExampleWithBLOBsWithRowbounds(ToolInfoDOExample example, RowBounds rowBounds);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table smart_processor_tool_info
     *
     * @mbg.generated Thu May 15 19:11:45 CST 2025
     */
    List<ToolInfoDO> selectByExampleWithBLOBs(ToolInfoDOExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table smart_processor_tool_info
     *
     * @mbg.generated Thu May 15 19:11:45 CST 2025
     */
    List<ToolInfoDO> selectByExampleWithRowbounds(ToolInfoDOExample example, RowBounds rowBounds);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table smart_processor_tool_info
     *
     * @mbg.generated Thu May 15 19:11:45 CST 2025
     */
    List<ToolInfoDO> selectByExample(ToolInfoDOExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table smart_processor_tool_info
     *
     * @mbg.generated Thu May 15 19:11:45 CST 2025
     */
    ToolInfoDO selectByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table smart_processor_tool_info
     *
     * @mbg.generated Thu May 15 19:11:45 CST 2025
     */
    int updateByExampleSelective(@Param("row") ToolInfoDO row, @Param("example") ToolInfoDOExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table smart_processor_tool_info
     *
     * @mbg.generated Thu May 15 19:11:45 CST 2025
     */
    int updateByExampleWithBLOBs(@Param("row") ToolInfoDO row, @Param("example") ToolInfoDOExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table smart_processor_tool_info
     *
     * @mbg.generated Thu May 15 19:11:45 CST 2025
     */
    int updateByExample(@Param("row") ToolInfoDO row, @Param("example") ToolInfoDOExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table smart_processor_tool_info
     *
     * @mbg.generated Thu May 15 19:11:45 CST 2025
     */
    int updateByPrimaryKeySelective(ToolInfoDO row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table smart_processor_tool_info
     *
     * @mbg.generated Thu May 15 19:11:45 CST 2025
     */
    int updateByPrimaryKeyWithBLOBs(ToolInfoDO row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table smart_processor_tool_info
     *
     * @mbg.generated Thu May 15 19:11:45 CST 2025
     */
    int updateByPrimaryKey(ToolInfoDO row);

    void deleteByToolId(@Param("toolId") String toolId);

    ToolInfoDO selectByToolId(@Param("toolId") String toolId);

}