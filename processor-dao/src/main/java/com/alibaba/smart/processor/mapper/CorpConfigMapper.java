package com.alibaba.smart.processor.mapper;

import com.alibaba.smart.processor.model.CorpConfigDO;
import com.alibaba.smart.processor.model.CorpConfigDOExample;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.session.RowBounds;

@Mapper
public interface CorpConfigMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table smart_processor_corp_config
     *
     * @mbg.generated Wed Jun 04 15:03:09 CST 2025
     */
    long countByExample(CorpConfigDOExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table smart_processor_corp_config
     *
     * @mbg.generated Wed Jun 04 15:03:09 CST 2025
     */
    int insert(CorpConfigDO row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table smart_processor_corp_config
     *
     * @mbg.generated Wed Jun 04 15:03:09 CST 2025
     */
    int insertSelective(CorpConfigDO row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table smart_processor_corp_config
     *
     * @mbg.generated Wed Jun 04 15:03:09 CST 2025
     */
    List<CorpConfigDO> selectByExampleWithBLOBsWithRowbounds(CorpConfigDOExample example, RowBounds rowBounds);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table smart_processor_corp_config
     *
     * @mbg.generated Wed Jun 04 15:03:09 CST 2025
     */
    List<CorpConfigDO> selectByExampleWithBLOBs(CorpConfigDOExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table smart_processor_corp_config
     *
     * @mbg.generated Wed Jun 04 15:03:09 CST 2025
     */
    List<CorpConfigDO> selectByExampleWithRowbounds(CorpConfigDOExample example, RowBounds rowBounds);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table smart_processor_corp_config
     *
     * @mbg.generated Wed Jun 04 15:03:09 CST 2025
     */
    List<CorpConfigDO> selectByExample(CorpConfigDOExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table smart_processor_corp_config
     *
     * @mbg.generated Wed Jun 04 15:03:09 CST 2025
     */
    CorpConfigDO selectByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table smart_processor_corp_config
     *
     * @mbg.generated Wed Jun 04 15:03:09 CST 2025
     */
    int updateByExampleSelective(@Param("row") CorpConfigDO row, @Param("example") CorpConfigDOExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table smart_processor_corp_config
     *
     * @mbg.generated Wed Jun 04 15:03:09 CST 2025
     */
    int updateByExampleWithBLOBs(@Param("row") CorpConfigDO row, @Param("example") CorpConfigDOExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table smart_processor_corp_config
     *
     * @mbg.generated Wed Jun 04 15:03:09 CST 2025
     */
    int updateByExample(@Param("row") CorpConfigDO row, @Param("example") CorpConfigDOExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table smart_processor_corp_config
     *
     * @mbg.generated Wed Jun 04 15:03:09 CST 2025
     */
    int updateByPrimaryKeySelective(CorpConfigDO row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table smart_processor_corp_config
     *
     * @mbg.generated Wed Jun 04 15:03:09 CST 2025
     */
    int updateByPrimaryKeyWithBLOBs(CorpConfigDO row);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table smart_processor_corp_config
     *
     * @mbg.generated Wed Jun 04 15:03:09 CST 2025
     */
    int updateByPrimaryKey(CorpConfigDO row);
}