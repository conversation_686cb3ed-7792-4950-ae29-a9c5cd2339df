package com.alibaba.smart.processor.model.attach;

import com.alibaba.smart.processor.model.BaseDataObject;

public class FileRecord extends BaseDataObject {
    private String fileName;
    private String fileExtType;
    private Long fileSize;
    private String sessionId;
    private String fileMeta;
    private String ossKey;
    private String dingMediaId;
    private String downloadUrl;
    private String immKey;

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public String getFileExtType() {
        return fileExtType;
    }

    public void setFileExtType(String fileExtType) {
        this.fileExtType = fileExtType;
    }

    public Long getFileSize() {
        return fileSize;
    }

    public void setFileSize(Long fileSize) {
        this.fileSize = fileSize;
    }

    public String getSessionId() {
        return sessionId;
    }

    public void setSessionId(String sessionId) {
        this.sessionId = sessionId;
    }

    public String getFileMeta() {
        return fileMeta;
    }

    public void setFileMeta(String fileMeta) {
        this.fileMeta = fileMeta;
    }

    public String getOssKey() {
        return ossKey;
    }

    public void setOssKey(String ossKey) {
        this.ossKey = ossKey;
    }

    public String getDingMediaId() {
        return dingMediaId;
    }

    public void setDingMediaId(String dingMediaId) {
        this.dingMediaId = dingMediaId;
    }

    public String getDownloadUrl() {
        return downloadUrl;
    }

    public void setDownloadUrl(String downloadUrl) {
        this.downloadUrl = downloadUrl;
    }

    public String getImmKey() {
        return immKey;
    }

    public void setImmKey(String immKey) {
        this.immKey = immKey;
    }
}
