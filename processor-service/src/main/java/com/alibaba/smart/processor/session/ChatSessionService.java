package com.alibaba.smart.processor.session;

import com.alibaba.smart.processor.model.ChatSessionDO;

import java.util.List;

public interface ChatSessionService {
    String createSession();

    void updateSessionTitle(String sessionId, String sessionTitle);

    void deleteSession(String sessionId);

    Long countSession();

    List<ChatSessionDO> listSessions();

    ChatSessionDO getSession(String sessionId);
}
