package com.alibaba.smart.processor.user.model;

import java.io.Serializable;

public class MastedataDeptment implements Serializable {
    /**
     * id
     */
    private String   id;

    /**
     * 部门No
     */
    private String deptNo;

    /**
     * 部门名称
     */
    private String deptName;

    /**
     * 部门名称 en
     */
    private String deptNameEn;

    /***
     * 部门主管
     */
    private String masterWorkNo;

    /***
     * 部门树111/222/333
     */
    private String deptPath;

    private String deptNamePath;

    /**
     * 组织类型：corp、bg、bu、dept
     */
    private String orgType;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getDeptNo() {
        return deptNo;
    }

    public void setDeptNo(String deptNo) {
        this.deptNo = deptNo;
    }

    public String getDeptName() {
        return deptName;
    }

    public void setDeptName(String deptName) {
        this.deptName = deptName;
    }

    public String getDeptNameEn() {
        return deptNameEn;
    }

    public void setDeptNameEn(String deptNameEn) {
        this.deptNameEn = deptNameEn;
    }

    public String getMasterWorkNo() {
        return masterWorkNo;
    }

    public void setMasterWorkNo(String masterWorkNo) {
        this.masterWorkNo = masterWorkNo;
    }

    public String getDeptPath() {
        return deptPath;
    }

    public void setDeptPath(String deptPath) {
        this.deptPath = deptPath;
    }

    public String getDeptNamePath() {
        return deptNamePath;
    }

    public void setDeptNamePath(String deptNamePath) {
        this.deptNamePath = deptNamePath;
    }

    public String getOrgType() {
        return orgType;
    }

    public void setOrgType(String orgType) {
        this.orgType = orgType;
    }
}
