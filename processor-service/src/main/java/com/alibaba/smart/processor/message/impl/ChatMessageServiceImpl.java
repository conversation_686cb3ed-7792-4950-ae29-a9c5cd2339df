package com.alibaba.smart.processor.message.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.smart.processor.context.UserContextUtil;
import com.alibaba.smart.processor.mapper.ChatMessageHistoryExtMapper;
import com.alibaba.smart.processor.mapper.ChatMessageMapper;
import com.alibaba.smart.processor.message.ChatMessageService;
import com.alibaba.smart.processor.message.model.MessageTypeEnum;
import com.alibaba.smart.processor.model.ChatMessageDO;
import com.alibaba.smart.processor.model.ChatMessageDOExample;
import com.alibaba.smart.processor.model.ChatMessageHistoryDO;
import com.alibaba.smart.processor.utils.DaoPropertyModifier;
import com.alibaba.smart.processor.utils.UUIDUtils;
import com.alibaba.smart.processor.utils.YNConstant;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;

@Service
public class ChatMessageServiceImpl implements ChatMessageService {
    private final static String MESSAGE_ID_PREFIX = "message";

    private final static Map<String, String> MESSAGE_USERID_MAP = new HashMap<>(){{
        put(MessageTypeEnum.SYSTEM.name(), "system");
        put(MessageTypeEnum.ASSISTANT.name(), "assistant");
    }};

    @Resource
    private ChatMessageMapper chatMessageMapper;

    @Resource
    private ChatMessageHistoryExtMapper chatMessageHistoryExtMapper;

    @Override
    public String saveMessage(String sessionId, String senderType) {
        ChatMessageDO chatMessageDO = saveMessage(sessionId, null, senderType, null, null, null);
        return chatMessageDO.getMessageId();
    }

    @Transactional
    @Override
    public ChatMessageDO saveMessage(String sessionId, String messageId, String senderType,
                                     String message, String files, String historyResponses) {
        ChatMessageDO messageDO = new ChatMessageDO();
        DaoPropertyModifier.buildInsert(messageDO);

        if(StringUtils.isBlank(messageId)){
            messageId = UUIDUtils.randomUUID(MESSAGE_ID_PREFIX).toLowerCase();
        }
        messageDO.setMessageId(messageId);
        messageDO.setSessionId(sessionId);
        messageDO.setSender(getSender(senderType));
        messageDO.setSenderType(senderType);
        messageDO.setMessage(message);
        messageDO.setFiles(files);

        chatMessageMapper.insert(messageDO);

        if(StringUtils.isNotBlank(historyResponses)){

            chatMessageHistoryExtMapper.deleteByMessageId(sessionId, messageId);

            JSONArray responseArray = JSONArray.parseArray(historyResponses);
            List<ChatMessageHistoryDO> historyDOS = new ArrayList<>();
            for (int i = 0; i < responseArray.size(); i++) {
                String responseItem = responseArray.getString(i);
                ChatMessageHistoryDO historyDO = new ChatMessageHistoryDO();
                historyDO.setMessageId(messageDO.getMessageId());
                historyDO.setSessionId(sessionId);
                historyDO.setContent(responseItem);
                DaoPropertyModifier.buildInsert(historyDO);
                historyDOS.add(historyDO);
            }
            chatMessageHistoryExtMapper.batchInsert(historyDOS);
        }
        return messageDO;
    }

    @Override
    public List<ChatMessageDO> listMessage(String sessionId) {
        ChatMessageDOExample example = new ChatMessageDOExample();
        example.createCriteria()
                .andIsDeletedEqualTo(YNConstant.N.getKey())
                .andSessionIdEqualTo(sessionId);
        List<ChatMessageDO> messages = chatMessageMapper.selectByExampleWithBLOBs(example);
        if(CollectionUtils.isEmpty(messages)){
            return new ArrayList<>();
        }
        return messages;
    }

    @Override
    public ChatMessageDO getMessage(String messageId) {
        ChatMessageDOExample example = new ChatMessageDOExample();
        example.createCriteria()
                .andIsDeletedEqualTo(YNConstant.N.getKey())
                .andMessageIdEqualTo(messageId);
        List<ChatMessageDO> messages = chatMessageMapper.selectByExample(example);
        if(CollectionUtils.isEmpty(messages)){
            return null;
        }
        return messages.get(0);
    }

    @Override
    public void updateChatHistory(String sessionId, String messageId, String message) {

        ChatMessageDOExample example = new ChatMessageDOExample();
        example.createCriteria()
                .andIsDeletedEqualTo(YNConstant.N.getKey())
                .andSessionIdEqualTo(sessionId)
                .andMessageIdEqualTo(messageId);
        List<ChatMessageDO> history = chatMessageMapper.selectByExampleWithBLOBs(example);
        if(CollectionUtils.isEmpty(history)){
            return;
        }
        ChatMessageDO messageDO = history.get(0);
        messageDO.setGmtModified(new Date());
        messageDO.setModifier(UserContextUtil.userId());
        messageDO.setMessage(message);
        chatMessageMapper.updateByPrimaryKeySelective(messageDO);
    }

    private String getSender(String senderType) {
        return StringUtils.defaultIfBlank(MESSAGE_USERID_MAP.get(senderType), UserContextUtil.userId());
    }
}
