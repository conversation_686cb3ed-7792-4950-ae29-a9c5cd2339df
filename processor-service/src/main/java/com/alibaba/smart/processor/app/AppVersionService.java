package com.alibaba.smart.processor.app;

import com.alibaba.smart.processor.model.AppVersionDO;

import java.util.List;

public interface AppVersionService {

    /**
     * 创建应用版本信息
     * @param appVersionDO
     */
    void createAppVersion(AppVersionDO appVersionDO);

    /**
     * 下线应用版本信息
     * @param appVersionDO
     */
    void offLineAppVersion(AppVersionDO appVersionDO);

    /**
     * 删除该应用下全部的版本信息
     * @param appId
     */
    void deleteAppVersion(String appId);

    /**
     * 获取应用的所有版本信息
     * @param appId
     * @return
     */
    List<AppVersionDO> getAppVersions(String appId, Integer page, Integer pageSize, String key);

    /**
     * 计算总数
     * @param appId
     * @param key
     * @return
     */
    long countAppVersion(String appId, String key);

    /**
     * 更新版本说明
     * @param appVersionDO
     */
    void updateAppVersionNode(AppVersionDO appVersionDO);

    /**
     * 获取online的应用版本信息
     * @param appId
     * @param version
     * @return
     */
    AppVersionDO getAppVersion(String appId, String version);

    AppVersionDO getLatestAppVersion(String appId);
}
