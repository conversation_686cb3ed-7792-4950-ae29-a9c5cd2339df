package com.alibaba.smart.processor.app.impl;

import com.alibaba.smart.processor.app.AppService;
import com.alibaba.smart.processor.context.UserContextUtil;
import com.alibaba.smart.processor.mapper.AppInfoExtMapper;
import com.alibaba.smart.processor.mapper.AppInfoMapper;
import com.alibaba.smart.processor.model.AppInfoDO;
import com.alibaba.smart.processor.model.AppInfoDOExample;
import com.alibaba.smart.processor.utils.DaoPropertyModifier;
import com.alibaba.smart.processor.utils.UUIDUtils;
import com.alibaba.smart.processor.utils.YNConstant;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.session.RowBounds;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class AppServiceImpl implements AppService {
    @Resource
    private AppInfoExtMapper appInfoExtMapper;
    @Override
    public void createApp(AppInfoDO appInfo) {
        if(StringUtils.isBlank(appInfo.getAppId())){
            String appId = UUIDUtils.generateShortUuid();
            appInfo.setAppId(appId);
        }
        DaoPropertyModifier.buildInsert(appInfo);
        appInfoExtMapper.insert(appInfo);
    }

    @Override
    public void updateApp(AppInfoDO appInfo) {
        AppInfoDOExample example = new AppInfoDOExample();
        AppInfoDOExample.Criteria criteria = example.createCriteria();
        criteria.andAppIdEqualTo(appInfo.getAppId());
        criteria.andIsDeletedEqualTo(YNConstant.N.getKey());
        criteria.andCorpIdEqualTo(UserContextUtil.corpId());

        DaoPropertyModifier.buildUpdate(appInfo);
        appInfoExtMapper.updateByExampleSelective(appInfo, example);
    }

    @Override
    public void deleteApp(String appId) {
        AppInfoDO appInfoDO = new AppInfoDO();
        appInfoDO.setAppId(appId);
        appInfoDO.setIsDeleted(YNConstant.Y.getKey());
        updateApp(appInfoDO);
    }

    @Override
    public AppInfoDO getByAppId(String appId) {
        AppInfoDOExample example = new AppInfoDOExample();
        AppInfoDOExample.Criteria criteria = example.createCriteria();
        criteria.andAppIdEqualTo(appId);
        criteria.andIsDeletedEqualTo(YNConstant.N.getKey());
        criteria.andCorpIdEqualTo(UserContextUtil.corpId());

        List<AppInfoDO> result = appInfoExtMapper.selectByExampleWithBLOBs(example);
        return CollectionUtils.isNotEmpty(result) ? result.get(0) : null;
    }

    @Override
    public AppInfoDO getBySessionId(String sessionId) {
        AppInfoDOExample example = new AppInfoDOExample();
        AppInfoDOExample.Criteria criteria = example.createCriteria();
        criteria.andSessionIdEqualTo(sessionId);
        criteria.andIsDeletedEqualTo(YNConstant.N.getKey());
        criteria.andCorpIdEqualTo(UserContextUtil.corpId());

        List<AppInfoDO> result = appInfoExtMapper.selectByExample(example);
        return CollectionUtils.isNotEmpty(result) ? result.get(0) : null;
    }

    @Override
    public List<AppInfoDO> listApps(Integer currentPage, Integer pageSize) {
        return appInfoExtMapper.listAppWithPage(UserContextUtil.corpId(), (currentPage - 1) * pageSize, pageSize);
    }

    @Override
    public Long countApps() {
        AppInfoDOExample example = new AppInfoDOExample();
        AppInfoDOExample.Criteria criteria = example.createCriteria();
        criteria.andIsDeletedEqualTo(YNConstant.N.getKey());
        criteria.andCorpIdEqualTo(UserContextUtil.corpId());
        return appInfoExtMapper.countByExample(example);
    }
}
