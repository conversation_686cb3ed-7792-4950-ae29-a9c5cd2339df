package com.alibaba.smart.processor.python.impl;

import com.alibaba.smart.processor.python.PythonEngineExecuteService;
import com.alibaba.smart.processor.python.model.PythonEngineContext;
import com.alibaba.smart.processor.utils.HttpUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

@Service
public class PythonEngineExecuteServiceImpl implements PythonEngineExecuteService {
//    @Value("${python.engine.endpoint:https://polymind-portal.alibaba.net}")
    @Value("${python.engine.endpoint:http://127.0.0.1:8000/}")

    private String endpoint;

    @Override
    public Object invoke(PythonEngineContext context) {
        String url = endpoint + context.getApiName();
        return HttpUtils.post(url, context.getParams(), null, null);
    }
}
