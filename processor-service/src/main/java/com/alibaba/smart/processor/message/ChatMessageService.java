package com.alibaba.smart.processor.message;

import com.alibaba.smart.processor.model.ChatMessageDO;

import java.util.List;

public interface ChatMessageService {

    String saveMessage(String sessionId, String senderType);

    ChatMessageDO saveMessage(String sessionId, String messageId, String senderType, String message, String files, String historyResponses);

    List<ChatMessageDO> listMessage(String sessionId);

    ChatMessageDO getMessage(String messageId);

    void updateChatHistory(String sessionId, String messageId, String message);

}
