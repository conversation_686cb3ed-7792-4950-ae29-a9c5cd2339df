package com.alibaba.smart.processor.user.impl;

import com.alibaba.boot.hsf.annotation.HSFConsumer;
import com.alibaba.smart.processor.user.UserService;
import com.alibaba.smart.processor.user.model.UserInfoModel;
import com.dingtalk.org.service.OrgEmpService;
import com.dingtalk.org.vo.OrgEmpExtVO;
import com.laiwang.common.model.ServiceResult;
import com.laiwang.protocol.media.MediaIdManager;
import com.laiwang.user.lippi.service.UserProfileService;
import com.laiwang.user.lippi.vo.ProfileVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Slf4j
@Service
public class UserServiceImpl implements UserService {

    @HSFConsumer(serviceVersion = "${view.hsf.version:1.0.0}")
    private OrgEmpService orgEmpService;

    @HSFConsumer(serviceVersion = "${view.hsf.version:1.0.0}")
    private UserProfileService userProfileService;

    @Override
    public UserInfoModel getUserInfo(Long uid) {
        UserInfoModel userInfoModel = new UserInfoModel();
        ServiceResult<ProfileVO> serviceResult = userProfileService.getProfileByUid(uid);
        if (serviceResult != null && serviceResult.isSuccess() && serviceResult.getResult() != null) {
            userInfoModel.setUserName(serviceResult.getResult().getNick());
            String avatar = null;
            try {
                avatar = MediaIdManager.transferTo(serviceResult.getResult().getAvatarMediaId(), true);
            } catch (Exception e) {
                log.error("get avatar error!" + uid);
            }
            userInfoModel.setAvatar(avatar);
            return userInfoModel;
        } else {
            log.error("获取钉钉用户头像失败!errorCode:{},errorMsg:{}", serviceResult.getErrorCode(), serviceResult.getErrorMsg());
            return userInfoModel;
        }
    }
}
