package com.alibaba.smart.processor.config;

import com.alibaba.boot.hsf.annotation.HSFConsumer;
import com.dingtalk.org.service.OrgAdminService;
import com.dingtalk.org.service.OrgDeptService;
import com.dingtalk.org.service.OrgEmpService;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Configuration;

@Configuration
@ConditionalOnProperty(name = "spring.hsf.consumer.config.enable",havingValue = "true", matchIfMissing = true)
public class HsfConsumerConfig {

    @HSFConsumer(serviceVersion = "${dingtalk.hsf.version:1.0.0}")
    private OrgAdminService orgAdminService;

    @HSFConsumer(serviceVersion = "${dingtalk.hsf.version:1.0.0}")
    private OrgEmpService orgEmpService;

    @HSFConsumer(serviceVersion = "${dingtalk.hsf.version:1.0.0}")
    private OrgDeptService dingOrgDeptService;
}
