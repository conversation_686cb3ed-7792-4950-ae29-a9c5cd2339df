package com.alibaba.smart.processor.app.impl;

import com.alibaba.smart.processor.app.AppResourceService;
import com.alibaba.smart.processor.mapper.AppResourceMapper;
import com.alibaba.smart.processor.model.AppResourceDO;
import com.alibaba.smart.processor.model.AppResourceDOExample;
import com.alibaba.smart.processor.model.AppVersionDO;
import com.alibaba.smart.processor.model.AppVersionDOExample;
import com.alibaba.smart.processor.utils.DaoPropertyModifier;
import com.alibaba.smart.processor.utils.UUIDUtils;
import com.alibaba.smart.processor.utils.YNConstant;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service
public class AppResourceServiceImpl implements AppResourceService {
    @Resource
    private AppResourceMapper appResourceMapper;

    @Override
    public void saveAppResource(AppResourceDO appResourceDO) {
        if(StringUtils.isBlank(appResourceDO.getResourceId())){
            appResourceDO.setResourceId(UUIDUtils.randomUUID());
        }
        DaoPropertyModifier.buildInsert(appResourceDO);
        appResourceMapper.insert(appResourceDO);
    }

    @Override
    public void updateAppResource(AppResourceDO appResourceDO) {
        if(StringUtils.isBlank(appResourceDO.getResourceId())){
            throw new RuntimeException("resourceId is null");
        }
        DaoPropertyModifier.buildUpdate(appResourceDO);

        AppResourceDOExample example = new AppResourceDOExample();
        AppResourceDOExample.Criteria criteria = example.createCriteria();
        criteria.andResourceIdEqualTo(appResourceDO.getResourceId())
                .andIsDeletedEqualTo(YNConstant.N.getKey());

        appResourceMapper.updateByExampleSelective(appResourceDO, example);
    }

    @Override
    public void deleteByResourceId(String resourceId) {
        AppResourceDO appResourceDO = new AppResourceDO();
        appResourceDO.setResourceId(resourceId);
        DaoPropertyModifier.buildDelete(appResourceDO);
        updateAppResource(appResourceDO);
    }

    @Override
    public void deleteByAppId(String appId) {
        AppResourceDOExample example = new AppResourceDOExample();
        AppResourceDOExample.Criteria criteria = example.createCriteria();
        criteria.andAppIdEqualTo(appId);
        criteria.andIsDeletedEqualTo(YNConstant.N.getKey());

        AppResourceDO appResourceDO = new AppResourceDO();
        appResourceDO.setIsDeleted(YNConstant.Y.getKey());
        DaoPropertyModifier.buildUpdate(appResourceDO);
        appResourceMapper.updateByExampleSelective(appResourceDO, example);
    }

    @Override
    public AppResourceDO getAppResource(String resourceId) {
        AppResourceDOExample example = new AppResourceDOExample();
        AppResourceDOExample.Criteria criteria = example.createCriteria();
        criteria.andResourceIdEqualTo(resourceId)
                .andIsDeletedEqualTo(YNConstant.N.getKey());
        List<AppResourceDO> result = appResourceMapper.selectByExample(example);
        return CollectionUtils.isEmpty(result) ? null : result.get(0);
    }
}
