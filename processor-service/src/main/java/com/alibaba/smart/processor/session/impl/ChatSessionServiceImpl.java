package com.alibaba.smart.processor.session.impl;

import com.alibaba.smart.processor.context.UserContextUtil;
import com.alibaba.smart.processor.mapper.ChatSessionMapper;
import com.alibaba.smart.processor.model.ChatSessionDO;
import com.alibaba.smart.processor.model.ChatSessionDOExample;
import com.alibaba.smart.processor.session.ChatSessionService;
import com.alibaba.smart.processor.session.model.SessionStatusEnum;
import com.alibaba.smart.processor.utils.UUIDUtils;
import com.alibaba.smart.processor.utils.YNConstant;
import com.dingtalk.common.sso.filter.RpcContext;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class ChatSessionServiceImpl implements ChatSessionService {
    private final static String SESSION_ID_PREFIX = "session";
    @Resource
    private ChatSessionMapper chatSessionMapper;

    @Override
    public String createSession() {
        ChatSessionDO session = new ChatSessionDO();
        session.setCorpId(UserContextUtil.corpId());
        session.setCreator(UserContextUtil.userId());
        session.setModifier(UserContextUtil.userId());
        session.setGmtCreate(new Date());
        session.setGmtModified(new Date());
        session.setIsDeleted(YNConstant.N.getKey());

        String sessionId = UUIDUtils.randomUUID(SESSION_ID_PREFIX).toLowerCase();
        session.setSessionId(sessionId);
        session.setStatus(SessionStatusEnum.ACTIVE.name());
        chatSessionMapper.insert(session);
        return sessionId;
    }

    @Override
    public void updateSessionTitle(String sessionId, String sessionTitle) {
        ChatSessionDO chatSessionDO = new ChatSessionDO();
        chatSessionDO.setTitle(sessionTitle);
        chatSessionDO.setGmtModified(new Date());
        chatSessionDO.setModifier(UserContextUtil.userId());

        ChatSessionDOExample example = new ChatSessionDOExample();
        example.createCriteria().andSessionIdEqualTo(sessionId);
        chatSessionMapper.updateByExampleSelective(chatSessionDO, example);
    }

    @Override
    public void deleteSession(String sessionId) {
        ChatSessionDO chatSessionDO = new ChatSessionDO();
        chatSessionDO.setIsDeleted(YNConstant.Y.getKey());
        chatSessionDO.setModifier(UserContextUtil.userId());
        chatSessionDO.setGmtModified(new Date());

        ChatSessionDOExample example = new ChatSessionDOExample();
        example.createCriteria().andSessionIdEqualTo(sessionId);
        chatSessionMapper.updateByExampleSelective(chatSessionDO, example);
    }

    @Override
    public Long countSession() {
        ChatSessionDOExample example = new ChatSessionDOExample();
        example.createCriteria()
                .andIsDeletedEqualTo(YNConstant.N.getKey())
                .andCreatorEqualTo(UserContextUtil.userId())
                .andCorpIdEqualTo(UserContextUtil.corpId());

        return chatSessionMapper.countByExample(example);
    }

    @Override
    public List<ChatSessionDO> listSessions() {
        ChatSessionDOExample example = new ChatSessionDOExample();
        example.createCriteria()
                .andIsDeletedEqualTo(YNConstant.N.getKey())
                .andCreatorEqualTo(UserContextUtil.userId())
                .andCorpIdEqualTo(UserContextUtil.corpId());
        List<ChatSessionDO> sessions = chatSessionMapper.selectByExample(example).stream()
                .sorted(Comparator.comparing(ChatSessionDO::getGmtCreate).reversed())
                .collect(Collectors.toList());
        if(CollectionUtils.isEmpty(sessions)){
            return new ArrayList<>();
        }
        return sessions;
    }

    @Override
    public ChatSessionDO getSession(String sessionId) {
        ChatSessionDOExample example = new ChatSessionDOExample();
        example.createCriteria()
                .andIsDeletedEqualTo(YNConstant.N.getKey())
                .andSessionIdEqualTo(sessionId);
        List<ChatSessionDO> sessions = chatSessionMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(sessions)) {
            return null;
        }
        return sessions.get(0);
    }
}
