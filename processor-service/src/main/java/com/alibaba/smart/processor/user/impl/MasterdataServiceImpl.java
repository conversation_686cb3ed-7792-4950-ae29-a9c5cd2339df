package com.alibaba.smart.processor.user.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.smart.processor.user.MasterdataService;
import com.alibaba.smart.processor.user.adapter.DingOrgEmpConverter;
import com.alibaba.smart.processor.user.model.MasterdataUser;
import com.dingtalk.common.cache.layer.utils.CollectionUtil;
import com.dingtalk.org.constants.EmpAdminRole;
import com.dingtalk.org.service.OrgAdminService;
import com.dingtalk.org.service.OrgEmpService;
import com.dingtalk.org.utils.CorpIdUtils;
import com.dingtalk.org.vo.OrgAdminVO;
import com.dingtalk.org.vo.OrgEmpExtVO;
import com.dingtalk.org.vo.OrgEmpVO;
import com.google.common.collect.ImmutableList;
import com.laiwang.common.model.ServiceResult;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.DependsOn;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Service
@ConditionalOnProperty(name = "spring.hsf.consumer.config.enable",havingValue = "true", matchIfMissing = true)
public class MasterdataServiceImpl implements MasterdataService {
    private static final Logger logger = LoggerFactory.getLogger(MasterdataServiceImpl.class);
    @Resource
    private OrgAdminService orgAdminService;
    @Resource
    private OrgEmpService orgEmpService;

    @Override
    public List<MasterdataUser> findMasterdataUserByUserIds(String corpId, List<String> userIds) {
        return findMasterdataUser(corpId, userIds);
    }

    @Override
    public MasterdataUser findMasterdataUserByUserId(String corpId, String userId) {
        List<MasterdataUser> masterdataUserList = findMasterdataUser(corpId, ImmutableList.of(userId));
        if(CollectionUtils.isNotEmpty(masterdataUserList)){
            return masterdataUserList.get(0);
        }
        return null;
    }

    @Override
    public List<String> getMainAdminsByCorpId(String corpId) {
        Long orgId = CorpIdUtils.corpIdToOrgId(corpId);
        ServiceResult<List<OrgAdminVO>> serviceResult =orgAdminService.getOrgAdminsByOrgId(orgId);
        List<String> adminStuffIdList = Collections.emptyList();
        if (serviceResult != null && serviceResult.isSuccess()) {
            if (CollectionUtils.isNotEmpty(serviceResult.getResult())) {
                List<Long> uidList = serviceResult.getResult()
                        .stream()
                        .filter(orgAdminVO -> EmpAdminRole.SUPER_ADMIN == orgAdminVO.getRoleV2() || EmpAdminRole.MULTI_MAIN_ADMIN == orgAdminVO.getRoleV2())
                        .map(OrgAdminVO::getUid)
                        .collect(Collectors.toList());

                ServiceResult<List<OrgEmpExtVO>> adminServiceResult = orgEmpService.getEmpInfoByUids(orgId, uidList);
                if (adminServiceResult != null && adminServiceResult.isSuccess()) {
                    if (CollectionUtils.isNotEmpty(adminServiceResult.getResult())) {
                        adminStuffIdList = adminServiceResult.getResult()
                                .stream()
                                .map(OrgEmpVO::getStaffId)
                                .collect(Collectors.toList());
                        return adminStuffIdList;
                    }
                } else {
                    logger.info("getMainAdminsByCorpId getEmpInfoByUids error: corpId:{}, uidList:{}, result: {} "
                            , corpId, JSON.toJSONString(uidList), JSON.toJSONString(adminServiceResult));
                }
            }
        } else {
            logger.info("getMainAdminsByCorpId getOrgAdminsByOrgId error: corpId:{}, result: {} "
                    , corpId, JSON.toJSONString(serviceResult));
        }
        return adminStuffIdList;
    }

    @Override
    public List<String> getSubAdminsByCorpId(String corpId) {
        Long orgId = CorpIdUtils.corpIdToOrgId(corpId);
        ServiceResult<List<OrgAdminVO>> serviceResult = orgAdminService.getOrgAdminsByOrgId(orgId);
        List<String> adminStuffIdList = Collections.emptyList();
        if (serviceResult != null && serviceResult.isSuccess()) {
            if (CollectionUtils.isNotEmpty(serviceResult.getResult())) {
                List<Long> uidList = serviceResult.getResult()
                        .stream()
                        .filter(orgAdminVO -> EmpAdminRole.GENERAL_ADMIN == orgAdminVO.getRoleV2())
                        .map(OrgAdminVO::getUid)
                        .collect(Collectors.toList());

                ServiceResult<List<OrgEmpExtVO>> adminServiceResult = orgEmpService.getEmpInfoByUids(orgId, uidList);
                if (adminServiceResult != null && adminServiceResult.isSuccess()) {
                    if (CollectionUtils.isNotEmpty(adminServiceResult.getResult())) {
                        adminStuffIdList = adminServiceResult.getResult()
                                .stream()
                                .map(OrgEmpVO::getStaffId)
                                .collect(Collectors.toList());
                        return adminStuffIdList;
                    }
                } else {
                    logger.info("getSubAdminsByCorpId getEmpInfoByUids error: corpId:{}, uidList:{}, result: {} ", corpId, JSON.toJSONString(uidList), JSON.toJSONString(adminServiceResult));
                }
            }
        } else {
            logger.info("getSubAdminsByCorpId getOrgAdminsByOrgId error: corpId:{}, result: {} ", corpId, JSON.toJSONString(serviceResult));
        }
        return adminStuffIdList;
    }

    public List<MasterdataUser> findMasterdataUser(String corpId, List<String> userIds) {
        List<MasterdataUser> masterdataUserList = new ArrayList<>();
        if (StringUtils.isNotBlank(corpId) && CollectionUtil.isNotEmpty(userIds)) {
            Long orgId = CorpIdUtils.corpIdToOrgId(corpId);
            userIds = userIds.stream().filter(StringUtils::isNotBlank).collect(Collectors.toList());
            ServiceResult<List<OrgEmpExtVO>> serviceResult = orgEmpService.getEmpInfoByStaffIdsWithDeleted(orgId, userIds);
            if (serviceResult != null && serviceResult.isSuccess() && CollectionUtils.isNotEmpty(
                    serviceResult.getResult())) {
                for (OrgEmpExtVO orgEmpExtVO : serviceResult.getResult()) {
                    MasterdataUser masterdataUser = DingOrgEmpConverter.toMasterdataUser(orgEmpExtVO, corpId);
                    if (masterdataUser != null) {
                        masterdataUserList.add(masterdataUser);
                    }
                }
            } else {
                logger.info("findMasterdataUserByUserIds orgEmpService.getEmpInfoByStaffIds corpId:{}, userIds:{}, error: {}", corpId, JSON.toJSONString(userIds), JSON.toJSONString(serviceResult));
            }
        }
        return masterdataUserList;
    }
}
