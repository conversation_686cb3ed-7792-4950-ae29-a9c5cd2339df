package com.alibaba.smart.processor.user;

import com.alibaba.smart.processor.user.model.MasterdataUser;

import java.util.List;

public interface MasterdataService {
    /**
     * 获取人员列表
     * @param corpId
     * @param userIds
     * @return
     */
    List<MasterdataUser> findMasterdataUserByUserIds(String corpId, List<String> userIds);

    /**
     * 获取指定人员信息
     * @param corpId
     * @param userId
     * @return
     */
    MasterdataUser findMasterdataUserByUserId(String corpId, String userId);
    /**
     * 获取企业下的主管理员
     *
     * @param corpId
     * @return
     */
    List<String> getMainAdminsByCorpId(String corpId);

    /**
     * 获取企业下的子管理员
     *
     * @param corpId
     * @return
     */
    List<String> getSubAdminsByCorpId(String corpId);
}
