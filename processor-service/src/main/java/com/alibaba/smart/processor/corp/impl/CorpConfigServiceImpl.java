package com.alibaba.smart.processor.corp.impl;

import com.alibaba.smart.processor.context.UserContextUtil;
import com.alibaba.smart.processor.corp.CorpConfigService;
import com.alibaba.smart.processor.exception.BusinessException;
import com.alibaba.smart.processor.exception.ExceptionContext;
import com.alibaba.smart.processor.mapper.CorpConfigMapper;
import com.alibaba.smart.processor.model.CorpConfigDO;
import com.alibaba.smart.processor.model.CorpConfigDOExample;
import com.alibaba.smart.processor.utils.DaoPropertyModifier;
import com.alibaba.smart.processor.utils.YNConstant;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service
public class CorpConfigServiceImpl implements CorpConfigService {
    @Resource
    private CorpConfigMapper corpConfigMapper;
    @Override
    public void saveConfig(String config) {
        if(StringUtils.isBlank(config)){
            throw new BusinessException(new ExceptionContext("配置内容不能为空"),"000000","配置内容不能为空");
        }
        CorpConfigDOExample query = new CorpConfigDOExample();
        query.createCriteria().andCorpIdEqualTo(UserContextUtil.corpId())
                .andIsDeletedEqualTo(YNConstant.N.getKey());
        List<CorpConfigDO> configDOList = corpConfigMapper.selectByExample(query);
        if(CollectionUtils.isNotEmpty(configDOList)){
            throw new BusinessException(new ExceptionContext("数据已经存在，无法新增"),"000000","数据已经存在，无法新增");
        }
        CorpConfigDO configDO = new CorpConfigDO();
        DaoPropertyModifier.buildInsert(configDO);

        configDO.setConfig(config);
        corpConfigMapper.insert(configDO);
    }

    @Override
    public void updateConfig(Long id, String config) {
        CorpConfigDOExample query = new CorpConfigDOExample();
        query.createCriteria().andCorpIdEqualTo(UserContextUtil.corpId())
                .andIsDeletedEqualTo(YNConstant.N.getKey())
                .andIdEqualTo(id);
        List<CorpConfigDO> configDOList = corpConfigMapper.selectByExample(query);
        if(CollectionUtils.isEmpty(configDOList)){
            throw new BusinessException(new ExceptionContext("配置数据不存在，无法更新"),"000000","配置数据不存在，无法更新");
        }
        CorpConfigDO configDO = configDOList.get(0);
        DaoPropertyModifier.buildUpdate(configDO);

        configDO.setConfig(config);
        corpConfigMapper.updateByPrimaryKeyWithBLOBs(configDO);
    }

    @Override
    public CorpConfigDO getConfig() {
        CorpConfigDOExample query = new CorpConfigDOExample();
        query.createCriteria().andCorpIdEqualTo(UserContextUtil.corpId())
                .andIsDeletedEqualTo(YNConstant.N.getKey());
        List<CorpConfigDO> configDOList = corpConfigMapper.selectByExampleWithBLOBs(query);
        if(CollectionUtils.isNotEmpty(configDOList)){
            return configDOList.get(0);
        }
        return null;
    }
}
