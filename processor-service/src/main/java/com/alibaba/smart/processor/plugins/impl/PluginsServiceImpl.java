package com.alibaba.smart.processor.plugins.impl;

import com.alibaba.smart.processor.context.UserContextUtil;
import com.alibaba.smart.processor.mapper.PluginsExtMapper;
import com.alibaba.smart.processor.mapper.PluginsMapper;
import com.alibaba.smart.processor.mapper.SessionPluginBindingExtMapper;
import com.alibaba.smart.processor.mapper.SessionPluginBindingMapper;
import com.alibaba.smart.processor.model.*;
import com.alibaba.smart.processor.plugins.PluginsService;
import com.alibaba.smart.processor.utils.DaoPropertyModifier;
import com.alibaba.smart.processor.utils.YNConstant;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Service
public class PluginsServiceImpl implements PluginsService {

    @Resource
    private PluginsMapper pluginsMapper;

    @Resource
    private PluginsExtMapper pluginsExtMapper;

    @Resource
    private SessionPluginBindingExtMapper sessionPluginBindingExtMapper;

    @Resource
    private SessionPluginBindingMapper sessionPluginBindingMapper;

    @Override
    public void createPlugins(PluginsDO pluginsDO) {

        DaoPropertyModifier.buildInsert(pluginsDO);
        pluginsMapper.insert(pluginsDO);
    }

    @Override
    public long countPlugins(String key) {
        return pluginsExtMapper.countPlugins(UserContextUtil.corpId(), key);
    }

    @Override
    public PluginsDO getByPluginId(String pluginId) {
        PluginsDOExample query = new PluginsDOExample();
        query.createCriteria().andPluginIdEqualTo(pluginId);
        List<PluginsDO> pluginsDOList = pluginsMapper.selectByExampleWithBLOBs(query);
        return CollectionUtils.isNotEmpty(pluginsDOList) ? pluginsDOList.get(0) : null;
    }

    @Override
    public List<PluginsDO> listPlugins(String key, Integer pageIndex, Integer pageSize) {
        return pluginsExtMapper.listPlugins(UserContextUtil.corpId(), key, (pageIndex - 1) * pageSize, pageSize);
    }

    @Override
    public void deletePlugin(String pluginId) {
        PluginsDO pluginsDO = new PluginsDO();
        pluginsDO.setPluginId(pluginId);
        pluginsDO.setIsDeleted(YNConstant.Y.getKey());
        updatePlugin(pluginsDO);
    }

    @Override
    public void deleteBySessionId(String sessionId) {
        sessionPluginBindingExtMapper.deleteBySessionId(sessionId);
    }

    @Override
    public void updatePlugin(PluginsDO pluginsDO) {
        PluginsDOExample example = new PluginsDOExample();
        PluginsDOExample.Criteria criteria = example.createCriteria();
        criteria.andPluginIdEqualTo(pluginsDO.getPluginId());
        criteria.andIsDeletedEqualTo(YNConstant.N.getKey());
        criteria.andCorpIdEqualTo(UserContextUtil.corpId());

        DaoPropertyModifier.buildUpdate(pluginsDO);
        pluginsExtMapper.updateByExampleSelective(pluginsDO, example);
    }

    @Override
    public List<PluginsDO> batchSelect(List<String> pluginIds) {
        PluginsDOExample example = new PluginsDOExample();
        PluginsDOExample.Criteria criteria = example.createCriteria();
        criteria.andPluginIdIn(pluginIds);
        criteria.andIsDeletedEqualTo(YNConstant.N.getKey());
        return pluginsExtMapper.selectByExampleWithBLOBs(example);
    }

    @Override
    public void createPluginBind(String sessionId, List<String> pluginIds) {
        sessionPluginBindingExtMapper.deleteBySessionId(sessionId);

        List<SessionPluginBindingDO> bindList = new ArrayList<>();
        for(String pluginId : pluginIds) {
            SessionPluginBindingDO sessionPluginBindingDO = new SessionPluginBindingDO();
            sessionPluginBindingDO.setSessionId(sessionId);
            sessionPluginBindingDO.setPluginId(pluginId);
            DaoPropertyModifier.buildInsert(sessionPluginBindingDO);

            bindList.add(sessionPluginBindingDO);
        }
        sessionPluginBindingExtMapper.batchInsert(bindList);
    }

    @Override
    public void batchInsert(List<SessionPluginBindingDO> sessionPluginBindingDOS) {
        for (SessionPluginBindingDO sessionPluginBindingDO : sessionPluginBindingDOS) {
            DaoPropertyModifier.buildInsert(sessionPluginBindingDO);
        }
        sessionPluginBindingExtMapper.batchInsert(sessionPluginBindingDOS);
    }

    @Override
    public void batchDelete(String sessionId, List<String> pluginIds) {
        sessionPluginBindingExtMapper.batchUpdate(sessionId, pluginIds, UserContextUtil.userId());
    }

    @Override
    public List<SessionPluginBindingDO> listSessionPluginBinding(String sessionId) {
        SessionPluginBindingDOExample example = new SessionPluginBindingDOExample();
        SessionPluginBindingDOExample.Criteria criteria = example.createCriteria();
        criteria.andSessionIdEqualTo(sessionId);
        criteria.andIsDeletedEqualTo(YNConstant.N.getKey());
        return sessionPluginBindingExtMapper.selectByExample(example);
    }
}
