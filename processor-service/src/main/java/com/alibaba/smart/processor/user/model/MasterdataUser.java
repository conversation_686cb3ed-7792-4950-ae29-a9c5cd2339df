package com.alibaba.smart.processor.user.model;

import java.io.Serializable;
import java.util.List;

public class MasterdataUser implements Serializable {
    /**
     * 工号
     */
    private String userId;
    /**
     * 花名
     */
    private String nickName;
    /**
     * 员工姓名
     */
    private String name;
    /**
     * 人员头像
     */
    private String personalPhoto;
    /**
     * 人员头像url
     */
    private String personalPhotoUrl;
    /**
     * 姓名全拼
     */
    private String pinyinNameAll;
    /**
     * 花名拼音
     */
    private String pinyinNick;
    /**
     * 公司编号
     */
    private String companyNo;

    /**
     * 邮箱
     */
    private String buMail;


    /**
     * 钉钉号，可以唤起钉钉详情页
     */
    private String dingtalkId;

    private String workStatus;

    /**
     * 部门列表
     */
    private List<MastedataDeptment> mastedataDeptments;

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getNickName() {
        return nickName;
    }

    public void setNickName(String nickName) {
        this.nickName = nickName;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getPersonalPhoto() {
        return personalPhoto;
    }

    public void setPersonalPhoto(String personalPhoto) {
        this.personalPhoto = personalPhoto;
    }

    public String getPersonalPhotoUrl() {
        return personalPhotoUrl;
    }

    public void setPersonalPhotoUrl(String personalPhotoUrl) {
        this.personalPhotoUrl = personalPhotoUrl;
    }

    public String getPinyinNameAll() {
        return pinyinNameAll;
    }

    public void setPinyinNameAll(String pinyinNameAll) {
        this.pinyinNameAll = pinyinNameAll;
    }

    public String getPinyinNick() {
        return pinyinNick;
    }

    public void setPinyinNick(String pinyinNick) {
        this.pinyinNick = pinyinNick;
    }

    public String getCompanyNo() {
        return companyNo;
    }

    public void setCompanyNo(String companyNo) {
        this.companyNo = companyNo;
    }

    public String getBuMail() {
        return buMail;
    }

    public void setBuMail(String buMail) {
        this.buMail = buMail;
    }

    public String getDingtalkId() {
        return dingtalkId;
    }

    public void setDingtalkId(String dingtalkId) {
        this.dingtalkId = dingtalkId;
    }

    public List<MastedataDeptment> getMastedataDeptments() {
        return mastedataDeptments;
    }

    public void setMastedataDeptments(List<MastedataDeptment> mastedataDeptments) {
        this.mastedataDeptments = mastedataDeptments;
    }

    public String getWorkStatus() {
        return workStatus;
    }

    public void setWorkStatus(String workStatus) {
        this.workStatus = workStatus;
    }
}
