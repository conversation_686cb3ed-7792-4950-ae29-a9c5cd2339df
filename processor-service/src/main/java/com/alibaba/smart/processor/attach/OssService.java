package com.alibaba.smart.processor.attach;

import com.alibaba.smart.processor.attach.model.OssToken;
import com.aliyun.oss.model.ObjectMetadata;

import java.io.InputStream;

/**
 * 和polymind自己的bucket对接，通过诺曼底的密钥托管模式实现
 * 和 AttachmentService的区别是使用的bucket不同，AttachmentService借用了yida的oss做过渡，后面有时间再统一
 */
public interface OssService {
    OssToken getOssToken(String type);

    InputStream getFileInputStreamDirectly(String fileName);

    ObjectMetadata getObjectMetadata(String fileName);
}
