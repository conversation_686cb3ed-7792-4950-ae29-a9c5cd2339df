package com.alibaba.smart.processor.plugins;

import com.alibaba.smart.processor.model.PluginsDO;
import com.alibaba.smart.processor.model.SessionPluginBindingDO;

import java.util.List;


public interface PluginsService {

    void createPlugins(PluginsDO pluginsDO);

    long countPlugins(String key);

    PluginsDO getByPluginId(String pluginId);

    List<PluginsDO> listPlugins(String key, Integer pageIndex, Integer pageSize);

    void deletePlugin(String pluginId);

    /**
     * 根据会话id物理删除绑定的插件
     * @param sessionId
     */
    void deleteBySessionId(String sessionId);

    void updatePlugin(PluginsDO pluginsDO);

    List<PluginsDO> batchSelect(List<String> pluginIds);

    void createPluginBind(String sessionId, List<String> pluginIds);

    void batchInsert(List<SessionPluginBindingDO> sessionPluginBindingDOS);

    void batchDelete(String sessionId, List<String> pluginIds);

    List<SessionPluginBindingDO> listSessionPluginBinding(String sessionId);
}
