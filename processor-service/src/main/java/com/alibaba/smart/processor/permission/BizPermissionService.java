package com.alibaba.smart.processor.permission;

public interface BizPermissionService {
    /***
     * 判断是否钉钉企业主管理员
     *
     * @return 布尔值
     */
    boolean isSuperOrCorpMainManager();

    /***
     * 判断是否钉钉企业管理员或者子管理员
     *
     * @return 布尔值
     */
    boolean isSuperOrCorpManager();

    /***
     * 判断是否超管
     *
     * @return
     */
    boolean isSuperManager();

    /**
     * 是否企业主管理员
     *
     * @return
     */
    boolean isCorpMainManager();

    /**
     * 是否企业子管理员
     *
     * @return
     */
    boolean isCorpSubManager();
}
