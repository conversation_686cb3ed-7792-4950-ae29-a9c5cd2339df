package com.alibaba.smart.processor.history.impl;

import com.alibaba.smart.processor.context.UserContextUtil;
import com.alibaba.smart.processor.history.ChatHistoryService;
import com.alibaba.smart.processor.mapper.ChatMessageHistoryMapper;
import com.alibaba.smart.processor.model.ChatMessageDO;
import com.alibaba.smart.processor.model.ChatMessageDOExample;
import com.alibaba.smart.processor.model.ChatMessageHistoryDO;
import com.alibaba.smart.processor.model.ChatMessageHistoryDOExample;
import com.alibaba.smart.processor.utils.YNConstant;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.List;

@Service
public class ChatHistoryServiceImpl implements ChatHistoryService {

    @Resource
    private ChatMessageHistoryMapper chatMessageHistoryMapper;

    @Override
    public void saveChatHistory(String sessionId, String messageId, String content) {
        ChatMessageHistoryDO history = new ChatMessageHistoryDO();
        history.setCreator(UserContextUtil.userId());
        history.setModifier(UserContextUtil.userId());
        history.setGmtCreate(new Date());
        history.setGmtModified(new Date());
        history.setIsDeleted(YNConstant.N.getKey());

        history.setMessageId(messageId);
        history.setSessionId(sessionId);
        history.setContent(content);
        chatMessageHistoryMapper.insert(history);
    }

    @Override
    public List<ChatMessageHistoryDO> listCharHistory(String sessionId, String messageId) {
        ChatMessageHistoryDOExample example = new ChatMessageHistoryDOExample();
        example.createCriteria()
                .andIsDeletedEqualTo(YNConstant.N.getKey())
                .andSessionIdEqualTo(sessionId)
                .andMessageIdEqualTo(messageId);
        List<ChatMessageHistoryDO> history = chatMessageHistoryMapper.selectByExampleWithBLOBs(example);
        history.sort(Comparator.comparing(ChatMessageHistoryDO::getGmtCreate));
        if(CollectionUtils.isEmpty(history)){
            return null;
        }
        return history;
    }

    @Override
    public void updateChatHistory(String sessionId, String messageId, String content) {
        ChatMessageHistoryDOExample example = new ChatMessageHistoryDOExample();
        example.createCriteria()
                .andIsDeletedEqualTo(YNConstant.N.getKey())
                .andSessionIdEqualTo(sessionId)
                .andMessageIdEqualTo(messageId);
        List<ChatMessageHistoryDO> history = chatMessageHistoryMapper.selectByExampleWithBLOBs(example);
        if(CollectionUtils.isEmpty(history)){
            return;
        }
        ChatMessageHistoryDO historyDO = history.get(0);
        historyDO.setContent(content);
        chatMessageHistoryMapper.updateByPrimaryKeySelective(historyDO);
    }

    @Override
    public ChatMessageHistoryDO getChatHistory(String sessionId, String messageId) {
        ChatMessageHistoryDOExample example = new ChatMessageHistoryDOExample();
        example.createCriteria()
                .andIsDeletedEqualTo(YNConstant.N.getKey())
                .andSessionIdEqualTo(sessionId)
                .andMessageIdEqualTo(messageId);
        List<ChatMessageHistoryDO> history = chatMessageHistoryMapper.selectByExampleWithBLOBs(example);
        if(CollectionUtils.isEmpty(history)){
            return null;
        }
        return history.get(0);
    }
}
