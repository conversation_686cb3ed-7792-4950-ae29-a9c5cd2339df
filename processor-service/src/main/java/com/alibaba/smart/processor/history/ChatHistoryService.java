package com.alibaba.smart.processor.history;

import com.alibaba.smart.processor.model.ChatMessageHistoryDO;

import java.util.List;

public interface ChatHistoryService {

    void saveChatHistory(String sessionId, String messageId, String content);

    List<ChatMessageHistoryDO> listCharHistory(String sessionId, String messageId);

    void updateChatHistory(String sessionId, String messageId, String content);

    ChatMessageHistoryDO getChatHistory(String sessionId, String messageId);
}
