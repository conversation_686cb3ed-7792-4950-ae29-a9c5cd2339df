package com.alibaba.smart.processor.attach.impl;

import com.alibaba.normandy.credential.Credential;
import com.alibaba.normandy.credential.CredentialProvider;
import com.alibaba.normandy.credential.ResourceNames;
import com.alibaba.smart.processor.attach.OssService;
import com.alibaba.smart.processor.attach.model.OssToken;
import com.alibaba.smart.processor.exception.BusinessException;
import com.alibaba.smart.processor.exception.ExceptionContext;
import com.aliyun.oss.ClientBuilderConfiguration;
import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import com.aliyun.oss.OSSException;
import com.aliyun.oss.common.comm.SignVersion;
import com.aliyun.oss.model.GetObjectRequest;
import com.aliyun.oss.model.OSSObject;
import com.aliyun.oss.model.ObjectMetadata;
import com.aliyuncs.DefaultAcsClient;
import com.aliyuncs.IAcsClient;
import com.aliyuncs.auth.AlibabaCloudCredentials;
import com.aliyuncs.auth.BasicCredentials;
import com.aliyuncs.auth.sts.AssumeRoleRequest;
import com.aliyuncs.auth.sts.AssumeRoleResponse;
import com.aliyuncs.exceptions.ClientException;
import com.aliyuncs.http.MethodType;
import com.aliyuncs.http.ProtocolType;
import com.aliyuncs.profile.DefaultProfile;
import com.aliyuncs.profile.IClientProfile;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import com.aliyun.oss.common.auth.*;

import java.io.File;
import java.io.InputStream;


@Service
public class OssServiceImpl implements OssService {
    private static final Logger logger = LoggerFactory.getLogger(OssServiceImpl.class);

    public static final String ENDPOINT = "oss-cn-shanghai.aliyuncs.com";
    public static final String ENDPOINT_WITH_PROTOCOL = "https://oss-cn-shanghai.aliyuncs.com";
    public static final String REGION_ID = "cn-shanghai";
    private static final String ROLE_SESSION_NAME = "smart-processor-code-package";
    private static final long DURATION_SECONDS = 15L * 60;
    private static final String STS_API_VERSION = "2015-04-01";
    private static final String USER_FILE_DIR = "user/";

    @Value("${appcode.oss.bucket}")
    private String codeBucketName;
    @Value("${appcode.oss.role.arn}")
    private String roleArn;
    @Value("${normandy.account.id}")
    private String accountId;
    @Value("${normandy.ram.usename}")
    private String ramUserName;

    @Autowired
    private CredentialProvider credentialProvider;

    private static final String OSS_POLICY_W_DIR_PATTEN =
            "{\n" +
            "  \"Statement\": [\n" +
            "    {\n" +
            "      \"Action\": [\n" +
            "        \"oss:PutObject\"\n" +
            "      ],\n" +
            "      \"Effect\": \"Allow\",\n" +
            "      \"Resource\": [\"acs:oss:*:*:%s/%s/*\"]\n" +
            "    }\n" +
            "  ],\n" +
            "  \"Version\": \"1\"\n" +
            "}";

    @Override
    public OssToken getOssToken(String packageName) {
        if(StringUtils.isBlank(packageName)){
            throw new IllegalArgumentException("type is null");
        }
        String ossPolicy = String.format(OSS_POLICY_W_DIR_PATTEN, codeBucketName, packageName);

        AssumeRoleResponse stsResponse = assumeRole(ossPolicy);
        OssToken ossTokenVO = new OssToken();
        ossTokenVO.setAccessKeyId(stsResponse.getCredentials().getAccessKeyId());
        ossTokenVO.setAccessKeySecret(stsResponse.getCredentials().getAccessKeySecret());
        ossTokenVO.setExpiration(stsResponse.getCredentials().getExpiration());
        ossTokenVO.setSecurityToken(String.valueOf(stsResponse.getCredentials().getSecurityToken()));
        ossTokenVO.setEndpoint(ENDPOINT);
        ossTokenVO.setBucketName(codeBucketName);
        ossTokenVO.setName(packageName);
        ossTokenVO.setRegionId(REGION_ID);
        return ossTokenVO;
    }

    @Override
    public InputStream getFileInputStreamDirectly(String fileName) {
        OSS ossClient = createOssClient();
        try {
            String objectName = USER_FILE_DIR + fileName;
            OSSObject ossObject = ossClient.getObject(new GetObjectRequest(codeBucketName, objectName));
            if(ossObject.getObjectContent() != null){
                return ossObject.getObjectContent();
            }
        } catch (Exception e) {
            logger.error("download file error", e);
            throw new BusinessException(new ExceptionContext("附件下载失败"),"000000","附件下载失败");
        } finally {
            ossClient.shutdown();
        }
        return null;
    }

    @Override
    public ObjectMetadata getObjectMetadata(String fileName) {
        OSS ossClient = createOssClient();
        String objectName = USER_FILE_DIR + fileName;
        return ossClient.getObjectMetadata(codeBucketName, objectName);
    }

    private OSS createOssClient() {
        Credential credential = buildCredential();
        DefaultCredentialProvider credentialsProvider = CredentialsProviderFactory.newDefaultCredentialProvider(
                credential.getAccessKeyId(),
                credential.getAccessKeySecret()
        );
        ClientBuilderConfiguration clientBuilderConfiguration = new ClientBuilderConfiguration();
        clientBuilderConfiguration.setSignatureVersion(SignVersion.V4);
        return OSSClientBuilder.create()
                .endpoint(ENDPOINT_WITH_PROTOCOL)
                .credentialsProvider(credentialsProvider)
                .clientConfiguration(clientBuilderConfiguration)
                .region(REGION_ID)
                .build();
    }

    /**
     * 获取STS授权
     * @param ossPolicy
     * @return
     */
    private AssumeRoleResponse assumeRole(String ossPolicy) {
        try {
            Credential credential = buildCredential();

            IClientProfile profile = DefaultProfile.getProfile(REGION_ID);
            AlibabaCloudCredentials credentials = new BasicCredentials(credential.getAccessKeyId(), credential.getAccessKeySecret());
            IAcsClient client = new DefaultAcsClient(profile, credentials);

            AssumeRoleRequest request = new AssumeRoleRequest();
            request.setVersion(STS_API_VERSION);
            request.setSysMethod(MethodType.POST);
            request.setSysProtocol(ProtocolType.HTTPS);
            request.setRoleArn(roleArn);
            request.setRoleSessionName(ROLE_SESSION_NAME);
            request.setPolicy(ossPolicy);
            request.setDurationSeconds(DURATION_SECONDS);

            return client.getAcsResponse(request);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 通过诺曼底获取凭证
     * @return
     */
    private Credential buildCredential(){
        String resourceName = ResourceNames.ofInternalAliyunAccessPackage(accountId, ramUserName);
        return credentialProvider.getCredential(resourceName);
    }
}
