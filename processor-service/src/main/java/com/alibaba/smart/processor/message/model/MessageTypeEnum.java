package com.alibaba.smart.processor.message.model;

public enum MessageTypeEnum {

    /**
     * 系统消息
     */
    SYSTEM,

    /**
     * 用户消息
     */
    USER,

    /**
     * 助理回复
     */
    ASSISTANT,

    /**
     * 工具调用
     */
    TOOL;

    public static MessageTypeEnum fromValue(String value) {
        if (value == null || value.trim().isEmpty()) {
            throw new IllegalArgumentException("输入值不能为空");
        }

        try {
            return valueOf(value.trim().toUpperCase());
        } catch (IllegalArgumentException | NullPointerException ex) {
            throw new IllegalArgumentException("非法的枚举值: " + value);
        }
    }
}
