package com.alibaba.smart.processor.utils;

import com.alibaba.smart.processor.context.UserContextUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.lang.reflect.Field;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.function.BiFunction;

public class DaoPropertyModifier {
    private static Logger logger = LoggerFactory.getLogger(DaoPropertyModifier.class);

    private static String CORP_ID = "corpId";
    private static String CREATOR = "creator";
    private static String MODIFIER = "modifier";
    private static String IS_DELETED = "isDeleted";
    private static String GMT_CREATE = "gmtCreate";
    private static String GMT_MODIFIED = "gmtModified";

    private static final Map<String, BiFunction<Field, Object, Object>> CREATE_PROPERTY_MAP = new HashMap<>();

    private static final Map<String, BiFunction<Field, Object, Object>> MODIFIED_PROPERTY_MAP = new HashMap<>();

    private static final Map<String, BiFunction<Field, Object, Object>> DELETED_PROPERTY_MAP = new HashMap<>();

    static {
        CREATE_PROPERTY_MAP.put(CORP_ID, (f, o) -> UserContextUtil.corpId());
        CREATE_PROPERTY_MAP.put(CREATOR, (f, o) -> UserContextUtil.userId());
        CREATE_PROPERTY_MAP.put(MODIFIER, (f, o) -> UserContextUtil.userId());
        CREATE_PROPERTY_MAP.put(IS_DELETED, (f, o) -> YNConstant.N.getKey());
        CREATE_PROPERTY_MAP.put(GMT_CREATE, (f, o) -> new Date());
        CREATE_PROPERTY_MAP.put(GMT_MODIFIED, (f, o) -> new Date());

        MODIFIED_PROPERTY_MAP.put(MODIFIER, (f, o) -> UserContextUtil.userId());
        MODIFIED_PROPERTY_MAP.put(GMT_MODIFIED, (f, o) -> new Date());

        DELETED_PROPERTY_MAP.put(MODIFIER, (f, o) -> UserContextUtil.userId());
        DELETED_PROPERTY_MAP.put(GMT_MODIFIED, (f, o) -> new Date());
        DELETED_PROPERTY_MAP.put(IS_DELETED, (f, o) -> YNConstant.Y.getKey());
    }

    public static void buildInsert(Object obj) {
        innerValueSet(obj, CREATE_PROPERTY_MAP);
    }

    public static void buildUpdate(Object obj){
        innerValueSet(obj, MODIFIED_PROPERTY_MAP);
    }

    public static void buildDelete(Object obj){
        innerValueSet(obj, DELETED_PROPERTY_MAP);
    }

    private static void innerValueSet(Object obj, Map<String, BiFunction<Field, Object, Object>> propertyMap){
        for (Field field : obj.getClass().getDeclaredFields()) {
            String name = field.getName();
            if (propertyMap.containsKey(name)) {
                field.setAccessible(true);
                Object newValue = propertyMap.get(name).apply(field, obj);
                try {
                    field.set(obj, newValue);
                } catch (IllegalAccessException e) {
                    logger.error("set dao property value error", e);
                }
            }
        }
    }
}
