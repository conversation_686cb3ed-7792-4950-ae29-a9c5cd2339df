package com.alibaba.smart.processor.permission;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.smart.processor.context.UserContextUtil;
import com.alibaba.smart.processor.diamond.DiamondEnums;
import com.alibaba.smart.processor.user.MasterdataService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class BizPermissionServiceImpl implements BizPermissionService {

    @Autowired(required = false)
    private MasterdataService masterdataService;

    @Override
    public boolean isSuperOrCorpMainManager() {
        if(isSuperManager()){
            return true;
        }

        return isCorpMainManager();
    }

    @Override
    public boolean isSuperOrCorpManager() {
        return isSuperOrCorpMainManager() || isCorpSubManager();
    }

    @Override
    public boolean isSuperManager() {
        String value = DiamondEnums.polymind_super_manager.getValue();
        JSONArray userArray = JSON.parseArray(value);
        if (userArray != null && !userArray.isEmpty()) {
            return userArray.contains(UserContextUtil.userId());
        }
        return false;
    }

    @Override
    public boolean isCorpMainManager() {
        List<String> adminUserIds = masterdataService.getMainAdminsByCorpId(UserContextUtil.corpId());
        return CollectionUtils.isNotEmpty(adminUserIds) && adminUserIds.contains(UserContextUtil.userId());
    }

    @Override
    public boolean isCorpSubManager() {
        List<String> subAdminUserIds = masterdataService.getSubAdminsByCorpId(UserContextUtil.corpId());
        return CollectionUtils.isNotEmpty(subAdminUserIds) && subAdminUserIds.contains(UserContextUtil.userId());
    }
}
