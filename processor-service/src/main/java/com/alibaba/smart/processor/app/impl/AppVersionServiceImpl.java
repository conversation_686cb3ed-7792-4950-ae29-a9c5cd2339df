package com.alibaba.smart.processor.app.impl;

import com.alibaba.smart.processor.app.AppVersionService;
import com.alibaba.smart.processor.app.enums.*;
import com.alibaba.smart.processor.mapper.AppVersionExtMapper;
import com.alibaba.smart.processor.mapper.AppVersionMapper;
import com.alibaba.smart.processor.model.AppVersionDO;
import com.alibaba.smart.processor.model.AppVersionDOExample;
import com.alibaba.smart.processor.utils.DaoPropertyModifier;
import com.alibaba.smart.processor.utils.YNConstant;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.checkerframework.checker.units.qual.C;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class AppVersionServiceImpl implements AppVersionService {

    @Resource
    private AppVersionMapper appVersionMapper;

    @Resource
    private AppVersionExtMapper appVersionExtMapper;

    @Override
    public void createAppVersion(AppVersionDO appVersionDO) {

        DaoPropertyModifier.buildInsert(appVersionDO);
        appVersionMapper.insert(appVersionDO);
    }

    @Override
    public void offLineAppVersion(AppVersionDO appVersionDO) {

        AppVersionDOExample example = new AppVersionDOExample();
        AppVersionDOExample.Criteria criteria = example.createCriteria();
        criteria.andAppIdEqualTo(appVersionDO.getAppId());
        criteria.andIsDeletedEqualTo(YNConstant.N.getKey());
        criteria.andStatusEqualTo(AppStatusType.ON_LINE.getStatus());

        DaoPropertyModifier.buildUpdate(appVersionDO);
        appVersionMapper.updateByExampleSelective(appVersionDO, example);
    }

    @Override
    public void deleteAppVersion(String appId) {
        AppVersionDOExample example = new AppVersionDOExample();
        AppVersionDOExample.Criteria criteria = example.createCriteria();
        criteria.andAppIdEqualTo(appId);
        criteria.andIsDeletedEqualTo(YNConstant.N.getKey());

        AppVersionDO appVersionDO = new AppVersionDO();
        appVersionDO.setIsDeleted(YNConstant.Y.getKey());
        DaoPropertyModifier.buildUpdate(appVersionDO);
        appVersionMapper.updateByExampleSelective(appVersionDO, example);
    }

    @Override
    public List<AppVersionDO> getAppVersions(String appId, Integer page, Integer pageSize, String key) {
        return appVersionExtMapper.listAppVersion(appId, (page - 1) * pageSize, pageSize, key);
    }

    @Override
    public long countAppVersion(String appId, String key) {
        return appVersionExtMapper.countAppVersion(appId, key);
    }

    @Override
    public void updateAppVersionNode(AppVersionDO appVersionDO) {
        AppVersionDOExample example = new AppVersionDOExample();
        AppVersionDOExample.Criteria criteria = example.createCriteria();
        criteria.andAppIdEqualTo(appVersionDO.getAppId());
        criteria.andVersionEqualTo(appVersionDO.getVersion());
        criteria.andIsDeletedEqualTo(YNConstant.N.getKey());

        DaoPropertyModifier.buildUpdate(appVersionDO);
        appVersionMapper.updateByExampleSelective(appVersionDO, example);
    }

    @Override
    public AppVersionDO getAppVersion(String appId, String version) {
        AppVersionDOExample example = new AppVersionDOExample();
        AppVersionDOExample.Criteria criteria = example.createCriteria();
        criteria.andAppIdEqualTo(appId);

        if(StringUtils.isNotBlank(version)){
            criteria.andVersionEqualTo(version);
        }
        criteria.andIsDeletedEqualTo(YNConstant.N.getKey());
        List<AppVersionDO> appVersionDOS = appVersionMapper.selectByExampleWithBLOBs(example);
        if (CollectionUtils.isNotEmpty(appVersionDOS)) {
            return appVersionDOS.get(0);
        }
        return null;
    }

    @Override
    public AppVersionDO getLatestAppVersion(String appId) {
        AppVersionDOExample example = new AppVersionDOExample();
        AppVersionDOExample.Criteria criteria = example.createCriteria();
        criteria.andAppIdEqualTo(appId);
        criteria.andIsDeletedEqualTo(YNConstant.N.getKey());
        List<AppVersionDO> appVersionDOS = appVersionMapper.selectByExampleWithBLOBs(example).stream()
                .sorted(Comparator.comparing(AppVersionDO::getGmtCreate).reversed()).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(appVersionDOS)) {
            return appVersionDOS.get(0);
        }
        return null;
    }
}
