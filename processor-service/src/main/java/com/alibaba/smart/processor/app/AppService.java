package com.alibaba.smart.processor.app;

import com.alibaba.smart.processor.model.AppInfoDO;

import java.util.List;

public interface AppService {
    /**
     * 创建应用基本信息
     * @param appInfo
     */
    void createApp(AppInfoDO appInfo);
    /**
     * 更新应用信息
     * @param appInfo
     */
    void updateApp(AppInfoDO appInfo);
    /**
     * 删除应用
     * @param appId
     */
    void deleteApp(String appId);

    /**
     * 获取应用详情
     * @param appId
     * @return
     */
    AppInfoDO getByAppId(String appId);

    /**
     * 获取应用详情
     * @param sessionId
     * @return
     */
    AppInfoDO getBySessionId(String sessionId);

    /**
     * 查询组织下的应用列表
     * @return
     */
    List<AppInfoDO> listApps(Integer currentPage, Integer pageSize);

    /**
     * 查询组织下的应用数量
     * @return
     */
    Long countApps();
}
