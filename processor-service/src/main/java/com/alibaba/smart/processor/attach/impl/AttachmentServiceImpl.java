package com.alibaba.smart.processor.attach.impl;

import com.alibaba.smart.processor.attach.AttachmentService;
import com.alibaba.smart.processor.mapper.FileRecordMapper;
import com.alibaba.smart.processor.model.attach.FileRecord;
import com.alibaba.smart.processor.utils.BaseDataBuildUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;

@Service
public class AttachmentServiceImpl implements AttachmentService {
    @Resource
    private FileRecordMapper fileRecordMapper;

    @Transactional
    @Override
    public void saveAttachment(FileRecord fileRecord) {
        BaseDataBuildUtils.insertBuild(fileRecord);
        fileRecordMapper.insert(fileRecord);
    }

    @Override
    public FileRecord getAttachmentByKey(String ossKey) {
        if(StringUtils.isBlank(ossKey)){
            return null;
        }
        return fileRecordMapper.getFileByKey(ossKey);
    }

    @Override
    public void updateAttachmentImmKey(String ossKey, String immKey) {

    }
}
