package com.alibaba.smart.processor.user.adapter;

import com.alibaba.smart.processor.user.model.MasterdataUser;
import com.dingtalk.org.vo.OrgEmpExtVO;
import org.apache.commons.lang3.StringUtils;

import java.util.Objects;

public class DingOrgEmpConverter {
    public static String USER_LEAVE_TITLE_ZN = "已离职";

    public static MasterdataUser toMasterdataUser(OrgEmpExtVO empExtVO, String corpId) {
        if (Objects.nonNull(empExtVO)) {
            MasterdataUser masterdataUser = new MasterdataUser();
            if (empExtVO.getDelete() != null && empExtVO.getDelete()) {
                masterdataUser.setName(String.format("%s[%s]", empExtVO.getName(), USER_LEAVE_TITLE_ZN));
                masterdataUser.setWorkStatus("UA");
            } else {
                masterdataUser.setName(empExtVO.getName());
                masterdataUser.setWorkStatus("A");
            }
            masterdataUser.setNickName(empExtVO.getNick());

            masterdataUser.setPinyinNick(
                    StringUtils.isNotBlank(empExtVO.getNameEnUs()) ? empExtVO.getNameEnUs() : empExtVO.getName());
            masterdataUser.setPinyinNameAll(StringUtils.isNotBlank(empExtVO.getNamePinyin()) ? empExtVO.getNamePinyin()
                    : masterdataUser.getPinyinNick());
            masterdataUser.setUserId(empExtVO.getStaffId());
            masterdataUser.setCompanyNo(corpId);
            return masterdataUser;
        }
        return null;
    }
}
