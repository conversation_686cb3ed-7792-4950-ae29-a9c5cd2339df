<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.taobao</groupId>
		<artifactId>parent</artifactId>
		<version>2.0.0</version>
	</parent>
	<groupId>com.alibaba.smart.processor</groupId>
	<artifactId>processor</artifactId>
	<version>1.0.0</version>
	<packaging>pom</packaging>
	<name>smart-processor</name>

	<properties>
		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
		<project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
		<jacoco-plugin.version>0.8.11</jacoco-plugin.version>
		<java.version>11</java.version>
		<maven-antrun.version>1.8</maven-antrun.version>
		<maven-compile-plugin.version>3.11.0</maven-compile-plugin.version>
		<maven-surefire-plugin.version>3.2.2</maven-surefire-plugin.version>
		<maven.compiler.source>11</maven.compiler.source>
		<maven.compiler.target>11</maven.compiler.target>
		<mybatis-starter.version>2.1.0</mybatis-starter.version>
		<pandora-boot-maven-plugin.version>10.0.1.3</pandora-boot-maven-plugin.version>
		<pandora-boot.version>2024-12-release-fix2</pandora-boot.version>
		<spring-boot.version>2.7.18</spring-boot.version>
		<fastjson.version>1.2.83</fastjson.version>
		<normandy-credential-spring-boot-starter.version>1.2.0</normandy-credential-spring-boot-starter.version>
	</properties>

	<modules>
		<module>processor-service</module>
		<module>processor-start</module>
		<module>processor-dao</module>
		<module>processor-facade</module>
		<module>processor-framework</module>
	</modules>
	<dependencyManagement>
		<dependencies>
			<dependency>
				<groupId>com.taobao.pandora</groupId>
				<artifactId>pandora-boot-starter-bom</artifactId>
				<version>${pandora-boot.version}</version>
				<type>pom</type>
				<scope>import</scope>
			</dependency>
			<dependency>
				<groupId>com.alibaba.smart.processor</groupId>
				<artifactId>processor-service</artifactId>
				<version>${project.version}</version>
			</dependency>
			<dependency>
				<groupId>com.alibaba.smart.processor</groupId>
				<artifactId>processor-dao</artifactId>
				<version>${project.version}</version>
			</dependency>
			<dependency>
				<groupId>com.alibaba.smart.processor</groupId>
				<artifactId>processor-framework</artifactId>
				<version>${project.version}</version>
			</dependency>
			<dependency>
				<groupId>com.alibaba.smart.processor</groupId>
				<artifactId>processor-facade</artifactId>
				<version>${project.version}</version>
			</dependency>
			<dependency>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-dependencies</artifactId>
				<version>${spring-boot.version}</version>
				<type>pom</type>
				<scope>import</scope>
			</dependency>
			<dependency>
				<groupId>org.springframework</groupId>
				<artifactId>spring-jcl</artifactId>
				<version>999-not-exist</version>
			</dependency>
			<dependency>
				<groupId>org.slf4j</groupId>
				<artifactId>jcl-over-slf4j</artifactId>
				<version>1.7.26</version>
			</dependency>
			<dependency>
				<groupId>org.slf4j</groupId>
				<artifactId>slf4j-log4j12</artifactId>
				<version>999-not-exist-v3</version>
			</dependency>
			<dependency>
				<groupId>javax.servlet</groupId>
				<artifactId>servlet-api</artifactId>
				<version>99.0-does-not-exist</version>
			</dependency>
			<dependency>
				<groupId>servlet-api</groupId>
				<artifactId>servlet-api</artifactId>
				<version>999-no-exist-SNAPSHOT</version>
			</dependency>
			<dependency>
				<groupId>javax.servlet</groupId>
				<artifactId>javax.servlet-api</artifactId>
				<version>999-not-exist-SNAPSHOT</version>
			</dependency>
			<dependency>
				<groupId>com.taobao.diamond</groupId>
				<artifactId>diamond-client</artifactId>
				<version>999-not-exist</version>
			</dependency>
			<dependency>
				<groupId>org.mybatis.spring.boot</groupId>
				<artifactId>mybatis-spring-boot-starter</artifactId>
				<version>${mybatis-starter.version}</version>
			</dependency>

			<dependency>
				<groupId>com.alibaba.boot</groupId>
				<artifactId>velocity-spring-boot-starter</artifactId>
				<version>1.0.5-SNAPSHOT</version>
				<exclusions>
					<exclusion>
						<groupId>com.alibaba.spring</groupId>
						<artifactId>spring-context-support</artifactId>
					</exclusion>
					<exclusion>
						<groupId>org.apache.velocity</groupId>
						<artifactId>velocity-tools</artifactId>
					</exclusion>
				</exclusions>
			</dependency>
			<dependency>
				<groupId>org.apache.velocity</groupId>
				<artifactId>velocity-tools</artifactId>
				<version>2.0</version>
			</dependency>
			<dependency>
				<groupId>org.apache.velocity</groupId>
				<artifactId>velocity-engine-core</artifactId>
				<version>2.3</version>
			</dependency>

			<!-- common utils-->
			<dependency>
				<groupId>org.apache.commons</groupId>
				<artifactId>commons-lang3</artifactId>
				<version>3.8.1</version>
			</dependency>
			<dependency>
				<groupId>com.alibaba</groupId>
				<artifactId>fastjson</artifactId>
				<version>${fastjson.version}</version>
			</dependency>
			<dependency>
				<groupId>org.apache.commons</groupId>
				<artifactId>commons-collections4</artifactId>
				<version>4.1</version>
			</dependency>

			<!-- 前端灰度-->
			<dependency>
				<groupId>com.dingtalk.dbase</groupId>
				<artifactId>dbase-trial</artifactId>
				<version>1.0.4</version>
			</dependency>

			<!-- oss-->
			<dependency>
				<groupId>com.aliyun.oss</groupId>
				<artifactId>aliyun-sdk-oss</artifactId>
				<version>3.17.4</version>
			</dependency>

			<dependency>
				<groupId>com.google.guava</groupId>
				<artifactId>guava</artifactId>
				<version>23.0</version>
			</dependency>

			<dependency>
				<groupId>com.aliyun</groupId>
				<artifactId>fc20230330</artifactId>
				<version>4.3.5</version>
			</dependency>

			<!-- Normandy 资源凭证 Spring Boot Starter SDK -->
			<dependency>
				<groupId>com.alibaba.normandy.credential</groupId>
				<artifactId>normandy-credential-spring-boot-starter</artifactId>
				<version>${normandy-credential-spring-boot-starter.version}</version>
			</dependency>

			<dependency>
				<groupId>com.aliyun</groupId>
				<artifactId>imm20200930</artifactId>
				<version>1.26.6</version>
			</dependency>

			<dependency>
				<groupId>com.aliyun</groupId>
				<artifactId>aliyun-java-sdk-imm</artifactId>
				<version>1.16.0-imm-SNAPSHOT</version>
				<exclusions>
					<exclusion>
						<groupId>javax.servlet</groupId>
						<artifactId>javax.servlet-api</artifactId>
					</exclusion>
				</exclusions>
			</dependency>

			<dependency>
				<groupId>com.alibaba.security</groupId>
				<artifactId>security-all</artifactId>
				<version>2.3.6</version>
			</dependency>

			<dependency>
				<groupId>com.dingtalk</groupId>
				<artifactId>lippi-common-sso</artifactId>
				<version>1.2.10</version>
				<exclusions>
					<exclusion>
						<groupId>com.taobao.diamond</groupId>
						<artifactId>diamond-client</artifactId>
					</exclusion>
					<exclusion>
						<groupId>com.alibaba.security</groupId>
						<artifactId>security-all</artifactId>
					</exclusion>
					<exclusion>
						<groupId>javax.validation</groupId>
						<artifactId>validation-api</artifactId>
					</exclusion>
					<exclusion>
						<groupId>apache-codec</groupId>
						<artifactId>commons-codec</artifactId>
					</exclusion>
				</exclusions>
			</dependency>

			<dependency>
				<groupId>com.laiwang</groupId>
				<artifactId>lippi-org.api</artifactId>
				<version>2.1.96</version>
			</dependency>

			<dependency>
				<groupId>com.laiwang</groupId>
				<artifactId>lippi-user.api</artifactId>
				<version>2.0.56</version>
			</dependency>

			<dependency>
				<groupId>com.laiwang.protocol</groupId>
				<artifactId>mediaid-core</artifactId>
				<version>1.5.7</version>
			</dependency>
			<dependency>
				<groupId>io.swagger.parser.v3</groupId>
				<artifactId>swagger-parser</artifactId>
				<version>2.1.16</version>
			</dependency>

			<dependency>
				<groupId>com.github.ben-manes.caffeine</groupId>
				<artifactId>caffeine</artifactId>
				<version>3.1.8</version>
			</dependency>
		</dependencies>
	</dependencyManagement>

	<build>
		<pluginManagement>
			<plugins>
				<plugin>
					<groupId>com.alibaba.maven.plugins</groupId>
					<artifactId>error-prone-maven-plugin</artifactId>
					<version>2.0.0</version>
					<configuration>
						<ruleProfileName>Error-Prone</ruleProfileName>
						<engineVersion>2.23.0-SNAPSHOT</engineVersion>
					</configuration>
					<executions>
						<execution>
							<id>check</id>
							<goals>
								<goal>check</goal>
								<goal>testCheck</goal>
							</goals>
						</execution>
					</executions>
				</plugin>
				<plugin>
					<groupId>org.apache.maven.plugins</groupId>
					<artifactId>maven-antrun-plugin</artifactId>
					<version>${maven-antrun.version}</version>
				</plugin>
				<plugin>
					<groupId>com.taobao.pandora</groupId>
					<artifactId>pandora-boot-maven-plugin</artifactId>
					<version>${pandora-boot-maven-plugin.version}</version>
				</plugin>
				<plugin>
					<groupId>org.apache.maven.plugins</groupId>
					<artifactId>maven-surefire-plugin</artifactId>
					<version>${maven-surefire-plugin.version}</version>
					<configuration>
						<argLine>${argLine} -Dfile.encoding=UTF-8</argLine>
					</configuration>
				</plugin>
				<plugin>
					<groupId>org.jacoco</groupId>
					<artifactId>jacoco-maven-plugin</artifactId>
					<version>${jacoco-plugin.version}</version>
					<configuration>
						<propertyName>argLine</propertyName>
					</configuration>
					<executions>
						<execution>
							<id>default-prepare-agent</id>
							<goals>
								<goal>prepare-agent</goal>
							</goals>
						</execution>
						<execution>
							<id>default-report</id>
							<phase>test</phase>
							<goals>
								<goal>report</goal>
								<goal>report-aggregate</goal>
							</goals>
						</execution>
					</executions>
				</plugin>
				<plugin>
					<groupId>org.apache.maven.plugins</groupId>
					<artifactId>maven-compiler-plugin</artifactId>
					<version>${maven-compile-plugin.version}</version>
					<configuration>
						<parameters>true</parameters>
					</configuration>
				</plugin>
			</plugins>
		</pluginManagement>
		<plugins>
			<plugin>
				<groupId>com.alibaba.maven.plugins</groupId>
				<artifactId>error-prone-maven-plugin</artifactId>
			</plugin>
			<plugin>
				<groupId>org.jacoco</groupId>
				<artifactId>jacoco-maven-plugin</artifactId>
			</plugin>
		</plugins>
	</build>

</project>
