<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.alibaba.smart.processor</groupId>
		<artifactId>processor</artifactId>
		<version>1.0.0</version>
		<relativePath>../pom.xml</relativePath>
	</parent>
	<artifactId>processor-facade</artifactId>
	<packaging>jar</packaging>
	<name>smart-processor-facade</name>

	<dependencies>
		<dependency>
			<groupId>com.alibaba.smart.processor</groupId>
			<artifactId>processor-service</artifactId>
		</dependency>
		<dependency>
			<groupId>com.aliyun</groupId>
			<artifactId>fc20230330</artifactId>
		</dependency>
		<dependency>
			<groupId>com.aliyun</groupId>
			<artifactId>imm20200930</artifactId>
		</dependency>
		<dependency>
			<groupId>com.aliyun</groupId>
			<artifactId>aliyun-java-sdk-imm</artifactId>
		</dependency>

		<dependency>
			<groupId>com.dingtalk</groupId>
			<artifactId>lippi-common-sso</artifactId>
		</dependency>

		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-webmvc</artifactId>
		</dependency>

		<dependency>
			<groupId>io.swagger.parser.v3</groupId>
			<artifactId>swagger-parser</artifactId>
		</dependency>
	</dependencies>

</project>
