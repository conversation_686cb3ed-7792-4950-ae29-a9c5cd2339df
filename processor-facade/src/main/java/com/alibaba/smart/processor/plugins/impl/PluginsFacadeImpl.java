package com.alibaba.smart.processor.plugins.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.smart.processor.adapter.PluginsAdapter;
import com.alibaba.smart.processor.context.UserContextUtil;
import com.alibaba.smart.processor.diamond.DiamondEnums;
import com.alibaba.smart.processor.model.PluginsDO;
import com.alibaba.smart.processor.model.SessionPluginBindingDO;
import com.alibaba.smart.processor.model.dto.PageDto;
import com.alibaba.smart.processor.model.dto.PluginsDTO;
import com.alibaba.smart.processor.model.dto.SessionPluginsBindingDTO;
import com.alibaba.smart.processor.model.vo.PluginsVO;
import com.alibaba.smart.processor.model.vo.SessionPluginsBindingVO;
import com.alibaba.smart.processor.model.vo.UserInfoVO;
import com.alibaba.smart.processor.plugins.PluginsFacade;
import com.alibaba.smart.processor.plugins.PluginsService;
import com.alibaba.smart.processor.plugins.execute.PluginApiCallService;
import com.alibaba.smart.processor.plugins.execute.model.ApiCallRequest;
import com.alibaba.smart.processor.plugins.execute.model.ApiCallResponse;
import com.alibaba.smart.processor.user.MasterdataService;
import com.alibaba.smart.processor.user.model.MasterdataUser;
import com.alibaba.smart.processor.utils.UUIDUtils;
import com.google.common.collect.ImmutableList;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class PluginsFacadeImpl implements PluginsFacade {

    @Resource
    private PluginsService pluginsService;

    @Autowired(required = false)
    private MasterdataService masterdataService;

    @Resource
    private PluginApiCallService pluginApiCallService;

    @Override
    public void createPlugins(PluginsDTO pluginsDTO) {

        String pluginId = UUIDUtils.randomUUID();

        PluginsDO pluginsDO = new PluginsDO();
        pluginsDO.setPluginId(pluginId);
        pluginsDO.setName(pluginsDTO.getName());
        pluginsDO.setDescription(pluginsDTO.getDescription());
        pluginsDO.setIcon(pluginsDTO.getIcon());
        pluginsDO.setType(pluginsDTO.getType());
        pluginsDO.setAuthConfig(pluginsDTO.getAuthConfig());
        pluginsDO.setContent(pluginsDTO.getContent());
        pluginsService.createPlugins(pluginsDO);
    }

    @Override
    public PageDto<PluginsVO> listPlugins(String key, Integer page, Integer pageSize) {
        long totalCount = pluginsService.countPlugins(key);
        if (totalCount == 0) {
            return new PageDto<>();
        }

        List<PluginsDO> plugins = pluginsService.listPlugins(key, page, pageSize);
        List<PluginsVO> pluginsVOS = PluginsAdapter.toPluginsVOList(plugins);

        Map<String, UserInfoVO> userInfoLocalCache = new HashMap<>();
        for (PluginsVO pluginsVO : pluginsVOS) {
            if (StringUtils.isNotBlank(pluginsVO.getModifier())) {
                UserInfoVO userInfoVO = userInfoLocalCache.computeIfAbsent(pluginsVO.getModifier(), this::getUserByUserId);
                pluginsVO.setUserInfo(userInfoVO);
            }
        }
        PageDto<PluginsVO> result = new PageDto<>();
        result.setData(pluginsVOS);
        result.setTotalCount(totalCount);
        result.setCurrentPage(page);
        return result;
    }

    @Override
    public List<PluginsVO> listSystemPlugins() {
        String systemPluginConfig = DiamondEnums.polymind_system_plugin_config.getValue();
        if(StringUtils.isEmpty(systemPluginConfig)){
            return null;
        }
        return JSON.parseArray(systemPluginConfig, PluginsVO.class);
    }

    @Override
    public void deletePlugin(String pluginId) {
        pluginsService.deletePlugin(pluginId);
    }

    @Override
    public void updatePlugin(PluginsDTO pluginsDTO) {
        PluginsDO pluginsDO = PluginsAdapter.toAppInfoVO(pluginsDTO);
        pluginsService.updatePlugin(pluginsDO);
    }


    @Transactional
    @Override
    public void bindingCreate(SessionPluginsBindingDTO sessionPluginsBindingDTO) {
        if (StringUtils.isBlank(sessionPluginsBindingDTO.getPluginIds())
                || StringUtils.isBlank(sessionPluginsBindingDTO.getSessionId())) {
            return;
        }
        List<String> pluginIds = Arrays.asList(sessionPluginsBindingDTO.getPluginIds().split(","));
        pluginsService.createPluginBind(sessionPluginsBindingDTO.getSessionId(), pluginIds);
    }

    @Override
    public SessionPluginsBindingVO bindingList(String sessionId) {
        List<SessionPluginBindingDO> sessionPluginBindingDOS = pluginsService.listSessionPluginBinding(sessionId);
        if (CollectionUtils.isEmpty(sessionPluginBindingDOS)) {
            return new SessionPluginsBindingVO();
        }

        SessionPluginsBindingVO sessionPluginsBindingVO = new SessionPluginsBindingVO();
        sessionPluginsBindingVO.setSessionId(sessionId);

        List<String> pluginIds = sessionPluginBindingDOS.stream()
                .map(SessionPluginBindingDO::getPluginId)
                .collect(Collectors.toList());
        List<PluginsDO> plugins = pluginsService.batchSelect(pluginIds);
        List<PluginsVO> pluginsVOS = PluginsAdapter.toPluginsVOList(plugins);

        String systemPluginConfig = DiamondEnums.polymind_system_plugin_config.getValue();
        if(StringUtils.isNotEmpty(systemPluginConfig)){
            List<PluginsVO> systemPlugins = JSON.parseArray(systemPluginConfig, PluginsVO.class);
            if(pluginsVOS == null){
                pluginsVOS = new ArrayList<>();
            }
            pluginsVOS.addAll(systemPlugins);
        }

        sessionPluginsBindingVO.setPluginsVOS(pluginsVOS);
        return sessionPluginsBindingVO;
    }

    @Override
    public void deletePluginBind(String sessionId, String pluginId) {
        List<String> pluginIds = Arrays.asList(pluginId.split(","));
        pluginsService.batchDelete(sessionId, pluginIds);
    }

    @Override
    public void executePlugin(String pluginId, String params) {
        log.info("pluginId:{}, params:{}", pluginId, params);
    }

    @Override
    public ApiCallResponse callPlugin(ApiCallRequest apiCallRequest) {
        String pluginId = apiCallRequest.getPluginId();
        PluginsDO pluginsDO = pluginsService.getByPluginId(pluginId);
        return pluginApiCallService.callPluginApi(pluginsDO, apiCallRequest);
    }

    private UserInfoVO getUserByUserId(String userId) {
        UserInfoVO userInfoVO = new UserInfoVO();
        try {
            MasterdataUser masterdataUser = masterdataService.findMasterdataUserByUserId(UserContextUtil.corpId(), userId);
            if (masterdataUser == null) {
                return userInfoVO;
            }
            userInfoVO.setUserName(masterdataUser.getName());
            userInfoVO.setAvatar(masterdataUser.getPersonalPhotoUrl());
            return userInfoVO;
        } catch (Exception ignored) {
            userInfoVO.setUserName("张三");
            userInfoVO.setAvatar("");
            return userInfoVO;
        }
    }
}
