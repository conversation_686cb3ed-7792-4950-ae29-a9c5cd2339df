package com.alibaba.smart.processor.app.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.smart.processor.adapter.AppInfoAdapter;
import com.alibaba.smart.processor.adapter.AppVersionAdapter;
import com.alibaba.smart.processor.app.AppFacade;
import com.alibaba.smart.processor.app.AppResourceService;
import com.alibaba.smart.processor.app.AppService;
import com.alibaba.smart.processor.app.AppVersionService;
import com.alibaba.smart.processor.app.enums.AppConfigType;
import com.alibaba.smart.processor.app.enums.AppResourceType;
import com.alibaba.smart.processor.app.enums.AppStatusType;
import com.alibaba.smart.processor.context.UserContextUtil;
import com.alibaba.smart.processor.exception.BusinessException;
import com.alibaba.smart.processor.exception.ExceptionContext;
import com.alibaba.smart.processor.model.AppInfoDO;
import com.alibaba.smart.processor.model.AppResourceDO;
import com.alibaba.smart.processor.model.AppVersionDO;
import com.alibaba.smart.processor.model.dto.AppInfoDTO;
import com.alibaba.smart.processor.model.dto.AppVersionDTO;
import com.alibaba.smart.processor.model.dto.AppVersionRevertDTO;
import com.alibaba.smart.processor.model.dto.PageDto;
import com.alibaba.smart.processor.model.vo.AppInfoVO;
import com.alibaba.smart.processor.model.vo.AppVersionVO;
import com.alibaba.smart.processor.model.vo.UserInfoVO;
import com.alibaba.smart.processor.permission.BizPermissionService;
import com.alibaba.smart.processor.user.MasterdataService;
import com.alibaba.smart.processor.user.model.MasterdataUser;
import com.alibaba.smart.processor.utils.UUIDUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;


@Slf4j
@Service
public class AppFacadeImpl implements AppFacade {
    @Resource
    private AppService appService;
    @Resource
    private AppResourceService appResourceService;
    @Resource
    private BizPermissionService bizPermissionService;
    @Autowired(required = false)
    private MasterdataService masterdataService;

    @Resource
    private AppVersionService appVersionService;

    @Transactional
    @Override
    public AppInfoVO createApp(AppInfoDTO appInfoDTO) {
        boolean hasPermission = bizPermissionService.isSuperOrCorpManager();
        if(!hasPermission){
            throw new BusinessException(new ExceptionContext("无权限"),"000000","无权限");
        }

        return firstCreateApp(appInfoDTO);
    }

    /**
     * 创建资源
     *
     * @param appId
     * @param resourceId
     * @param appInfoDTO
     */
    private void createResource(String appId, String resourceId, AppInfoDTO appInfoDTO) {
        AppResourceDO appResourceDO = new AppResourceDO();
        appResourceDO.setAppId(appId);
        appResourceDO.setResourceId(resourceId);
        appResourceDO.setResourceType(AppResourceType.PAGE.name());
        appResourceDO.setSourceKey(appInfoDTO.getSourceKey());
        appResourceDO.setSourceCompiledKey(appInfoDTO.getSourceCompiledKey());
        appResourceDO.setVersion("0");
        appResourceService.saveAppResource(appResourceDO);
    }

    /**
     * 新增线上的版本
     *
     * @param appId
     * @param updateNode
     * @param resourceId
     */
    private void createOnlineAppVersion(String appId, String updateNode, String resourceId, String version) {
        AppVersionDO appVersionDO = new AppVersionDO();
        appVersionDO.setAppId(appId);
        appVersionDO.setVersion(version);
        appVersionDO.setResourceId(resourceId);
        appVersionDO.setStatus(AppStatusType.ON_LINE.getStatus());
        appVersionDO.setUpdateNote(updateNode);
        appVersionService.createAppVersion(appVersionDO);
    }

    /**
     * 下线线上的版本
     *
     * @param appId
     */
    private void offlineAppVersion(String appId) {
        AppVersionDO offLineAppVersionDO = new AppVersionDO();
        offLineAppVersionDO.setAppId(appId);
        offLineAppVersionDO.setStatus(AppStatusType.OFF_LINE.getStatus());
        appVersionService.offLineAppVersion(offLineAppVersionDO);
    }

    /**
     * 更新应用信息
     *
     * @param appInfoDTO
     */
    private void updateAppInfo(AppInfoDTO appInfoDTO, String resourceId) {
        AppInfoDO appInfoDO = new AppInfoDO();
        appInfoDO.setAppId(appInfoDTO.getAppId());
        appInfoDO.setAppName(appInfoDTO.getAppName());
        appInfoDO.setAppDesc(appInfoDTO.getAppDesc());
        appInfoDO.setAppIcon(appInfoDTO.getAppIcon());
        appInfoDO.setSessionId(appInfoDTO.getSessionId());
        appInfoDO.setVersion(appInfoDTO.getVersion());
        Map<String, String> appConfig = new HashMap<>();
        appConfig.put(AppConfigType.RESOURCE_ID.getAlias(), resourceId);
        appInfoDO.setAppConfig(JSON.toJSONString(appConfig));
        appService.updateApp(appInfoDO);
    }

    /**
     * 首次发布
     *
     * @param appInfoDTO
     * @return
     */
    private AppInfoVO firstCreateApp(AppInfoDTO appInfoDTO) {
        String appId = UUIDUtils.generateShortUuid();
        String resourceId = UUIDUtils.randomUUID();

        AppInfoDO appInfoDO = createAppInfo(appInfoDTO, appId, resourceId);
//        createOnlineAppVersion(appId, null, resourceId, appInfoDO.getVersion());
//        createResource(appId, resourceId, appInfoDTO);

        return AppInfoAdapter.toAppInfoVO(appInfoDO);
    }

    @NotNull
    private AppInfoDO createAppInfo(AppInfoDTO appInfoDTO, String appId, String resourceId) {
        AppInfoDO appInfoDO = new AppInfoDO();
//        String version = "1";
        appInfoDO.setAppId(appId);
        appInfoDO.setAppName(appInfoDTO.getAppName());
        appInfoDO.setAppDesc(appInfoDTO.getAppDesc());
        appInfoDO.setAppIcon(appInfoDTO.getAppIcon());
        appInfoDO.setSessionId(appInfoDTO.getSessionId());
//        appInfoDO.setVersion(version);

        Map<String, String> appConfig = new HashMap<>();
        appConfig.put(AppConfigType.RESOURCE_ID.getAlias(), resourceId);
        appInfoDO.setAppConfig(JSON.toJSONString(appConfig));
        appService.createApp(appInfoDO);
        return appInfoDO;
    }

    @Override
    public AppInfoVO getByAppId(String appId) {
        AppInfoDO appInfoDO = appService.getByAppId(appId);
        AppInfoVO appInfoVO = AppInfoAdapter.toAppInfoVO(appInfoDO);
        if (StringUtils.isNotBlank(appInfoDO.getModifier())) {
            UserInfoVO userInfoVO = getUserByUserId(appInfoDO.getModifier());
            appInfoVO.setUserInfo(userInfoVO);
        }
        return appInfoVO;
    }

    @Override
    public AppInfoVO getBySessionId(String sessionId) {
        AppInfoDO appInfoDO = appService.getBySessionId(sessionId);
        AppInfoVO appInfoVO = AppInfoAdapter.toAppInfoVO(appInfoDO);
        if (appInfoDO != null && StringUtils.isNotBlank(appInfoDO.getModifier())) {
            UserInfoVO userInfoVO = getUserByUserId(appInfoDO.getModifier());
            appInfoVO.setUserInfo(userInfoVO);
        }
        return appInfoVO;
    }

    @Transactional
    @Override
    public void updateApp(AppInfoDTO appInfoDTO) {
        boolean hasPermission = bizPermissionService.isSuperOrCorpManager();
        if (!hasPermission) {
            throw new BusinessException(new ExceptionContext("无权限"), "000000", "无权限");
        }

        AppInfoDO appInfoDO = appService.getByAppId(appInfoDTO.getAppId());
        if (appInfoDO == null) {
            throw new RuntimeException("App:" + appInfoDTO.getAppId() + " not exist");
        }
        appInfoDO.setAppName(appInfoDTO.getAppName());
        appInfoDO.setAppDesc(appInfoDTO.getAppDesc());
        appInfoDO.setAppIcon(appInfoDTO.getAppIcon());
        appService.updateApp(appInfoDO);

        String appConfig = appInfoDO.getAppConfig();
        if (StringUtils.isBlank(appConfig)) {
            return;
        }
        JSONObject jsonObject = JSON.parseObject(appConfig);
        String resourceId = jsonObject.getString(AppConfigType.RESOURCE_ID.getAlias());
        if (StringUtils.isBlank(resourceId)) {
            return;
        }

        AppResourceDO appResourceDO = appResourceService.getAppResource(resourceId);
        if (appResourceDO == null) {
            return;
        }
        appResourceDO.setSourceKey(appInfoDTO.getSourceKey());
        appResourceDO.setSourceCompiledKey(appInfoDTO.getSourceCompiledKey());
        appResourceService.updateAppResource(appResourceDO);
    }

    @Override
    public void updateApp(String appId, String name, String desc) {
        boolean hasPermission = bizPermissionService.isSuperOrCorpManager();
        if (!hasPermission) {
            throw new BusinessException(new ExceptionContext("无权限"), "000000", "无权限");
        }

        AppInfoDO appInfoDO = appService.getByAppId(appId);
        if (appInfoDO == null) {
            throw new RuntimeException("App:" + appId + " not exist");
        }
        appInfoDO.setAppName(name);
        appInfoDO.setAppDesc(desc);
        appService.updateApp(appInfoDO);
    }

    @Transactional
    @Override
    public void deleteApp(String appId) {
        boolean hasPermission = bizPermissionService.isSuperOrCorpManager();
        if(!hasPermission){
            throw new BusinessException(new ExceptionContext("无权限"),"000000","无权限");
        }

        appService.deleteApp(appId);
        appResourceService.deleteByAppId(appId);
        appVersionService.deleteAppVersion(appId);
    }

    @Override
    public PageDto<AppInfoVO> pageApp(int currentPage, int pageSize) {
        long totalCount = appService.countApps();
        if (totalCount == 0) {
            return new PageDto<>();
        }
        List<AppInfoDO> appInfoDOList = appService.listApps(currentPage, pageSize);
        List<AppInfoVO> appInfoVOS = AppInfoAdapter.toAppInfoVOList(appInfoDOList);
        Map<String, AppInfoVO> appInfoVOMap = appInfoVOS.stream().collect(Collectors.toMap(AppInfoVO::getAppId, Function.identity()));

        Map<String, UserInfoVO> userInfoLocalCache = new HashMap<>();
        for (AppInfoDO appInfoDO : appInfoDOList) {
            AppInfoVO appInfoVO = appInfoVOMap.get(appInfoDO.getAppId());
            if (StringUtils.isBlank(appInfoDO.getModifier())) {
                continue;
            }
            UserInfoVO userInfoVO = userInfoLocalCache.computeIfAbsent(appInfoDO.getModifier(), this::getUserByUserId);
            appInfoVO.setUserInfo(userInfoVO);
        }
        PageDto<AppInfoVO> result = new PageDto<>();
        result.setData(appInfoVOS);
        result.setTotalCount(totalCount);
        result.setCurrentPage(currentPage);
        return result;
    }

    @Transactional
    @Override
    public void deployApp(String appId, String sourceCompiledKey, String updateNode) {
        boolean hasPermission = bizPermissionService.isSuperOrCorpManager();
        if (!hasPermission) {
            throw new BusinessException(new ExceptionContext("无权限"), "000000", "无权限");
        }

        String resourceId = UUIDUtils.randomUUID();
        AppResourceDO appResourceDO = new AppResourceDO();
        appResourceDO.setAppId(appId);
        appResourceDO.setResourceId(resourceId);
        appResourceDO.setResourceType(AppResourceType.PAGE.name());
        appResourceDO.setSourceCompiledKey(sourceCompiledKey);
        appResourceDO.setVersion("0");
        appResourceService.saveAppResource(appResourceDO);

        String version = buildNewVersion(appId);

        Map<String, String> appConfig = new HashMap<>();
        appConfig.put(AppConfigType.RESOURCE_ID.getAlias(), resourceId);
        AppInfoDO appInfoDO = new AppInfoDO();
        appInfoDO.setAppId(appId);
        appInfoDO.setVersion(version);
        appInfoDO.setAppConfig(JSON.toJSONString(appConfig));
        appService.updateApp(appInfoDO);

        offlineAppVersion(appId);
        createOnlineAppVersion(appId, updateNode, resourceId, version);
    }

    @NotNull
    private String buildNewVersion(String appId) {
        AppInfoDO appInfo = appService.getByAppId(appId);
        if (StringUtils.isEmpty(appInfo.getVersion())) {
            return "V1";
        }

        // 版本号递增
        AppVersionDO appVersionDO = appVersionService.getLatestAppVersion(appId);
        if (Objects.isNull(appVersionDO)) {
            return "V1";
        }

        String numberPart = appVersionDO.getVersion().substring(1);
        int versionNumber;
        try {
            versionNumber = Integer.parseInt(numberPart);
        } catch (NumberFormatException e) {
            throw new IllegalArgumentException("版本号数字部分超出整数范围");
        }
        return "V" + (versionNumber + 1);
    }

    @Override
    public PageDto<AppVersionVO> getAppVersions(String appId, Integer page, Integer pageSize, String key) {
        PageDto<AppVersionVO> result = new PageDto<>();
        long countAppVersion = appVersionService.countAppVersion(appId, key);
        if(countAppVersion == 0) {
            return result;
        }
        List<AppVersionDO> appVersions = appVersionService.getAppVersions(appId, page, pageSize, key);
        List<AppVersionVO> appVersionVOS = AppVersionAdapter.toAppVersionVOList(appVersions);

        for (AppVersionVO appVersionVO : appVersionVOS) {
            if (StringUtils.isNotBlank(appVersionVO.getModifier())) {
                UserInfoVO userInfoVO = getUserByUserId(appVersionVO.getModifier());
                appVersionVO.setUserInfo(userInfoVO);
            }
        }

        result.setData(appVersionVOS);
        result.setTotalCount(countAppVersion);
        return result;
    }

    @Override
    public void updateVersionNode(AppVersionDTO appVersionDTO) {
        AppVersionDO appVersionDO = new AppVersionDO();
        appVersionDO.setAppId(appVersionDTO.getAppId());
        appVersionDO.setVersion(appVersionDTO.getVersion());
        appVersionDO.setUpdateNote(appVersionDTO.getUpdateNote());
        appVersionService.updateAppVersionNode(appVersionDO);
    }

    @Transactional
    @Override
    public void revertVersion(AppVersionRevertDTO appVersionRevertDTO) {
        updateAppBindingVersion(appVersionRevertDTO);
        offlineCurrentAppVersion(appVersionRevertDTO);
        onlineNewAppVersion(appVersionRevertDTO);
    }

    @Override
    public AppVersionVO getAppVersion(String appId, String version) {
        AppVersionDO appVersion = appVersionService.getAppVersion(appId, version);
        if (Objects.isNull(appVersion)) {
            throw new BusinessException(new ExceptionContext("应用版本不存在"),"000101");
        }
        return AppVersionAdapter.toAppVersionVO(appVersion);
    }

    /**
     * 更新应用绑定的版本
     *
     * @param appVersionRevertDTO
     */
    private void updateAppBindingVersion(AppVersionRevertDTO appVersionRevertDTO) {
        AppInfoDO appInfoDO = appService.getByAppId(appVersionRevertDTO.getAppId());
        if (appInfoDO == null) {
            throw new BusinessException(new ExceptionContext("应用版本不存在"),"000101");
        }
        AppVersionDO appVersion = appVersionService.getAppVersion(appVersionRevertDTO.getAppId(), appVersionRevertDTO.getRevertVersion());
        if (Objects.isNull(appVersion)) {
            throw new BusinessException(new ExceptionContext("指定的应用版本不存在"),"000103");
        }
        Map<String, String> appConfig = new HashMap<>();
        appConfig.put(AppConfigType.RESOURCE_ID.getAlias(), appVersion.getResourceId());
        appInfoDO.setAppConfig(JSON.toJSONString(appConfig));
        appInfoDO.setVersion(appVersionRevertDTO.getRevertVersion());
        appService.updateApp(appInfoDO);
    }

    /**
     * 下线当前版本
     *
     * @param appVersionRevertDTO
     */
    private void offlineCurrentAppVersion(AppVersionRevertDTO appVersionRevertDTO) {
        AppVersionDO appVersion = appVersionService.getAppVersion(appVersionRevertDTO.getAppId(), appVersionRevertDTO.getCurrentVersion());
        if (Objects.isNull(appVersion)) {
            throw new BusinessException(new ExceptionContext("应用版本不存在"),"000101");
        }
        AppVersionDO appVersionDO = new AppVersionDO();
        appVersionDO.setAppId(appVersionRevertDTO.getAppId());
        appVersionDO.setVersion(appVersionRevertDTO.getCurrentVersion());
        appVersionDO.setStatus(AppStatusType.OFF_LINE.getStatus());
        appVersionService.updateAppVersionNode(appVersionDO);
    }

    /**
     * 上线新版本
     *
     * @param appVersionRevertDTO
     */
    private void onlineNewAppVersion(AppVersionRevertDTO appVersionRevertDTO) {
        AppVersionDO appVersion = appVersionService.getAppVersion(appVersionRevertDTO.getAppId(), appVersionRevertDTO.getRevertVersion());
        if (Objects.isNull(appVersion)) {
            throw new BusinessException(new ExceptionContext("指定的应用版本不存在"),"000103");
        }
        AppVersionDO appVersionDO = new AppVersionDO();
        appVersionDO.setAppId(appVersionRevertDTO.getAppId());
        appVersionDO.setVersion(appVersionRevertDTO.getRevertVersion());
        appVersionDO.setStatus(AppStatusType.ON_LINE.getStatus());
        appVersionDO.setUpdateNote(appVersionRevertDTO.getUpdateNote());
        appVersionService.updateAppVersionNode(appVersionDO);
    }

    private UserInfoVO getUserByUserId(String userId) {
        UserInfoVO userInfoVO = new UserInfoVO();
        try {
            MasterdataUser masterdataUser = masterdataService.findMasterdataUserByUserId(UserContextUtil.corpId(), userId);
            if (masterdataUser == null) {
                return userInfoVO;
            }
            userInfoVO.setUserName(masterdataUser.getName());
            userInfoVO.setAvatar(masterdataUser.getPersonalPhotoUrl());
            return userInfoVO;
        } catch (Exception ignored) {
            userInfoVO.setUserName("张三");
            userInfoVO.setAvatar("");
            return userInfoVO;
        }
    }
}
