package com.alibaba.smart.processor.fc.impl;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.smart.processor.attach.AttachmentFacade;
import com.alibaba.smart.processor.fc.AliyunFunctionComputeFacade;
import com.alibaba.smart.processor.model.vo.FcExecuteResult;
import com.alibaba.smart.processor.tool.ToolFacade;
import com.alibaba.smart.processor.utils.YNConstant;
import com.aliyun.fc20230330.models.InvokeFunctionHeaders;
import com.aliyun.fc20230330.models.InvokeFunctionRequest;
import com.aliyun.fc20230330.models.InvokeFunctionResponse;
import com.aliyun.tea.TeaException;
import com.aliyun.teautil.models.RuntimeOptions;
import com.taobao.wsgfstjson.JSON;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.BufferedReader;
import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.Base64;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Service
public class AliyunFunctionComputeFacadeImpl implements AliyunFunctionComputeFacade {
    private static final Logger logger = LoggerFactory.getLogger(AliyunFunctionComputeFacadeImpl.class);

    @Resource
    private com.aliyun.fc20230330.Client client;
    @Resource
    private AttachmentFacade attachmentFacade;

    @Override
    public FcExecuteResult executeWithOssKey(String functionName, String codePath) {
        if(StringUtils.isBlank(codePath)){
            return new FcExecuteResult();
        }
        InputStream codeStream = attachmentFacade.getFileInputStream(codePath);
        return innerExecute(functionName, codeStream);
    }

    @Override
    public FcExecuteResult executeWithContent(String functionName, String codeContent, Map<String, Object> params) {
        if(StringUtils.isBlank(codeContent)){
            return new FcExecuteResult();
        }
        JSONObject bodyJson = new JSONObject();
        bodyJson.put("code", codeContent);
        bodyJson.put("params", params);
        bodyJson.put("isTest", YNConstant.Y.getKey());

        InputStream codeStream = new ByteArrayInputStream(JSON.toJSONBytes(bodyJson));
        return innerExecute(functionName, codeStream);
    }

    private FcExecuteResult innerExecute(String functionName, InputStream body) {
        FcExecuteResult fcExecuteResult = new FcExecuteResult();
        try {

            InvokeFunctionRequest request = new InvokeFunctionRequest();
            request.setBody(body);

            InvokeFunctionHeaders header = new InvokeFunctionHeaders();
            header.setXFcInvocationType("Sync");
            header.setXFcLogType("Tail");

            RuntimeOptions runtimeOptions = new RuntimeOptions();
            runtimeOptions.setReadTimeout(600 * 1000);

            StringBuilder result = new StringBuilder();
            InvokeFunctionResponse response = client.invokeFunctionWithOptions(functionName, request, header, runtimeOptions);
            BufferedReader reader = new BufferedReader(new InputStreamReader(response.getBody()));
            String line;
            while ((line = reader.readLine()) != null) {
                result.append(line); // 或者根据需要选择是否加换行
            }
            if(response.getStatusCode() == 200){
                fcExecuteResult.setResult(result.toString());
                fcExecuteResult.setSuccess(!result.toString().toLowerCase().startsWith("error:"));
            }else{
                fcExecuteResult.setSuccess(false);
            }
            Object logResult = response.getHeaders().get("x-fc-log-result");
            byte[] logBytes = Base64.getDecoder().decode(logResult.toString());
            String executeLog = new String(logBytes).trim();
            formatExecuteLog(fcExecuteResult, executeLog);

            return fcExecuteResult;
        } catch (TeaException error) {
            fcExecuteResult.setSuccess(false);
            fcExecuteResult.setErrorMsg(error.getMessage());
            return fcExecuteResult;
        } catch (Exception _error) {
            TeaException error = new TeaException(_error.getMessage(), _error);
            fcExecuteResult.setSuccess(false);
            fcExecuteResult.setErrorMsg(error.message);
            return fcExecuteResult;
        }
    }

    private void formatExecuteLog(FcExecuteResult executeResult, String executeLog) {
        Pattern requestIdPattern = Pattern.compile("RequestId: (\\S+)");
        Matcher requestIdMatcher = requestIdPattern.matcher(executeLog);
        if (requestIdMatcher.find()) {
            String requestId = requestIdMatcher.group(1).trim();
            executeResult.setRequestId(requestId);
        }

        String logContent = "";
        try {
            logContent = executeLog.substring(executeLog.indexOf("script_log_start") + "script_log_start".length());
            logContent = logContent.substring(0, logContent.indexOf("FC Invoke End RequestId")).trim();
        } catch (Exception e) {
            logger.warn("formatExecuteLog error:", e);
        }
        executeResult.setExecuteLog(logContent);
    }
}
