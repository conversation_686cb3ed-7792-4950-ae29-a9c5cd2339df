package com.alibaba.smart.processor.utils;

import com.alibaba.smart.processor.model.dto.StreamHttpClientContext;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.httpclient.NameValuePair;
import org.apache.commons.lang.StringUtils;
import org.apache.http.Header;
import org.apache.http.HttpException;
import org.apache.http.HttpResponse;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.concurrent.FutureCallback;
import org.apache.http.config.ConnectionConfig;
import org.apache.http.conn.ssl.NoopHostnameVerifier;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.impl.nio.client.CloseableHttpAsyncClient;
import org.apache.http.impl.nio.client.HttpAsyncClients;
import org.apache.http.impl.nio.conn.PoolingNHttpClientConnectionManager;
import org.apache.http.impl.nio.reactor.DefaultConnectingIOReactor;
import org.apache.http.impl.nio.reactor.IOReactorConfig;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.nio.IOControl;
import org.apache.http.nio.client.methods.AsyncByteConsumer;
import org.apache.http.nio.client.methods.HttpAsyncMethods;
import org.apache.http.nio.conn.ssl.SSLIOSessionStrategy;
import org.apache.http.nio.protocol.HttpAsyncRequestProducer;
import org.apache.http.nio.reactor.IOReactorException;
import org.apache.http.protocol.HttpContext;
import org.apache.http.ssl.SSLContexts;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import javax.net.ssl.SSLContext;
import javax.net.ssl.TrustManager;
import javax.net.ssl.X509TrustManager;
import java.io.IOException;
import java.nio.ByteBuffer;
import java.nio.charset.StandardCharsets;
import java.security.cert.X509Certificate;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

public class StreamHttpClient {
    private final static Logger logger = LoggerFactory.getLogger(StreamHttpClient.class);

    public static List<org.apache.http.NameValuePair> buildNameValuePairs(Map<String, String> params) {
        if (MapUtils.isEmpty(params)) {
            return new ArrayList<>();
        }
        return params.entrySet().stream().filter(entry -> StringUtils.isNotBlank(entry.getKey()) && StringUtils.isNotBlank(entry.getValue())).map(entry -> new BasicNameValuePair(entry.getKey(), entry.getValue())).collect(Collectors.toList());
    }

    public static void invoke(StreamHttpClientContext context) throws Exception {

        SseEmitter emitter = context.getSseEmitter();
        String url = context.getEndpoint() + context.getApiName();
        HttpPost httpPost = new HttpPost(url);
        UrlEncodedFormEntity entity = new UrlEncodedFormEntity(buildNameValuePairs(context.getParams()), StandardCharsets.UTF_8);

        httpPost.setEntity(entity);

        IOReactorConfig ioReactorConfig = IOReactorConfig.custom()
                .setSoKeepAlive(true)
                .setIoThreadCount(2)
                .build();
        DefaultConnectingIOReactor ioReactor = null;
        try {
            ioReactor = new DefaultConnectingIOReactor(ioReactorConfig);
        } catch (IOReactorException e) {
            throw new RuntimeException(e);
        }
        ConnectionConfig connectionConfig = ConnectionConfig.custom()
                .setCharset(StandardCharsets.UTF_8)
                .build();
        PoolingNHttpClientConnectionManager connectionManager = new PoolingNHttpClientConnectionManager(ioReactor);
        connectionManager.setDefaultConnectionConfig(connectionConfig);

        // 创建信任所有证书的 TrustManager
        TrustManager[] trustAllCerts = new TrustManager[]{
                new X509TrustManager() {
                    public X509Certificate[] getAcceptedIssuers() {  return new X509Certificate[]{}; }
                    public void checkClientTrusted(X509Certificate[] certs, String authType) {}
                    public void checkServerTrusted(X509Certificate[] certs, String authType) {}
                }
        };

        // 构建 SSLContext
        SSLContext sslContext = SSLContexts.custom()
                .loadTrustMaterial(null, (chain, authType) -> true) // 信任所有证书
                .build();
        sslContext.init(null, trustAllCerts, new java.security.SecureRandom());

        SSLIOSessionStrategy sessionStrategy = new SSLIOSessionStrategy(sslContext, NoopHostnameVerifier.INSTANCE);
        CloseableHttpAsyncClient httpClient = HttpAsyncClients.custom()
                .setKeepAliveStrategy((response, ctx) -> context.getTimeout()) // 保持连接60秒
//                .setConnectionManager(connectionManager)
                .setDefaultIOReactorConfig(ioReactorConfig)
                .setSSLStrategy(sessionStrategy)
                .build();
        httpClient.start();

        HttpAsyncRequestProducer producer = HttpAsyncMethods.create(httpPost);

        httpClient.execute(producer, new StreamResponseHandler(emitter, httpClient), new StreamFutureCallback(emitter));
    }

    private static class StreamFutureCallback implements FutureCallback<Void> {
        private final SseEmitter emitter;
        public StreamFutureCallback(SseEmitter emitter) {
            this.emitter = emitter;
        }
        @Override
        public void completed(Void unused) {
        }

        @Override
        public void failed(Exception e) {
            emitter.completeWithError(e);
        }

        @Override
        public void cancelled() {
            emitter.completeWithError(new RuntimeException("Cancelled"));
        }
    }


    private static class StreamResponseHandler extends AsyncByteConsumer<Void> {
        private final SseEmitter emitter;
        private final StringBuilder buffer = new StringBuilder();
        private final CloseableHttpAsyncClient client;

        public StreamResponseHandler(SseEmitter emitter, CloseableHttpAsyncClient client) {
            this.emitter = emitter;
            this.client = client;
        }

        @Override
        protected void onResponseReceived(HttpResponse httpResponse) throws HttpException, IOException {
            httpResponse.setHeader("X-Accel-Buffering", "no");

            Header contentTypeHeader = httpResponse.getFirstHeader("Content-Type");
            String currentContentType = contentTypeHeader != null ? contentTypeHeader.getValue() : null;

            if (currentContentType == null || !currentContentType.contains("charset=")) {
                httpResponse.setHeader("Content-Type", "text/event-stream; charset=UTF-8");
            } else {
                String newContentType = currentContentType.replaceAll("charset=.*?(;|$)", "charset=UTF-8$1");
                httpResponse.setHeader("Content-Type", newContentType);
            }
        }

        @Override
        protected Void buildResult(HttpContext httpContext) throws Exception {
            return null;
        }

        @Override
        protected void onByteReceived(ByteBuffer buf, IOControl ioControl) throws IOException {
            // 实时处理字节流
            byte[] bytes = new byte[buf.remaining()];
            buf.get(bytes);
            String chunk = new String(bytes, StandardCharsets.UTF_8);
//            emitter.send(chunk.substring(5));
            buffer.append(chunk);

            // 解析SSE格式
            int endIdx;
            while ((endIdx = buffer.indexOf("\n\n")) != -1) {
                String event = buffer.substring(0, endIdx).trim();
                buffer.delete(0, endIdx + 2);
                emitter.send(event.substring(5)); // 发送有效数据

            }
        }

        @Override
        protected void releaseResources() {
            if (buffer.length() > 0) {
                try {
                    emitter.send(buffer.toString());
                } catch (IOException e) {
                    throw new RuntimeException(e);
                }
            }
            emitter.complete();

            if(client != null){
                try {
                    client.close();
                } catch (IOException e) {
                    throw new RuntimeException(e);
                }
            }
        }
    }

}
