package com.alibaba.smart.processor.plugins.execute;

import com.alibaba.smart.processor.plugins.execute.model.PluginApiDefinition;
import com.alibaba.smart.processor.plugins.execute.model.PluginApiOperation;
import com.alibaba.smart.processor.plugins.execute.model.PluginApiParameter;
import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.Operation;
import io.swagger.v3.oas.models.PathItem;
import io.swagger.v3.oas.models.media.Schema;
import io.swagger.v3.oas.models.parameters.RequestBody;
import io.swagger.v3.oas.models.security.SecurityScheme;
import io.swagger.v3.parser.OpenAPIV3Parser;
import io.swagger.v3.parser.core.models.SwaggerParseResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
public class PluginApiDefinitionParser {
    private final OpenAPIV3Parser parser;
    public PluginApiDefinitionParser() {
        this.parser = new OpenAPIV3Parser();
    }

    public PluginApiDefinition parseApiDefinition(String yamlContent) {
        try {
            // 使用Swagger Parser解析
            SwaggerParseResult result = parser.readContents(yamlContent, null, null);

            if (result.getMessages() != null && !result.getMessages().isEmpty()) {
                log.warn("OpenAPI解析警告: {}", result.getMessages());
            }

            OpenAPI openAPI = result.getOpenAPI();
            if (openAPI == null) {
                throw new RuntimeException("无法解析OpenAPI定义");
            }

            return convertToInternalModel(openAPI);

        } catch (Exception e) {
            log.error("解析OpenAPI定义失败", e);
            throw new RuntimeException("解析OpenAPI定义失败: " + e.getMessage());
        }
    }

    private PluginApiDefinition convertToInternalModel(OpenAPI openAPI) {
        PluginApiDefinition definition = new PluginApiDefinition();
        definition.setTitle(openAPI.getInfo().getTitle());
        definition.setDescription(openAPI.getInfo().getDescription());

        // 解析服务器信息
        if (openAPI.getServers() != null && !openAPI.getServers().isEmpty()) {
            definition.setBaseUrl(openAPI.getServers().get(0).getUrl());
        }

        // 解析路径和操作
        Map<String, PluginApiOperation> operations = new HashMap<>();
        if (openAPI.getPaths() != null) {
            openAPI.getPaths().forEach((path, pathItem) -> {
                convertPathItem(path, pathItem, operations, openAPI);
            });
        }
        definition.setOperations(operations);

        // 解析安全方案
        if (openAPI.getComponents() != null && openAPI.getComponents().getSecuritySchemes() != null) {
            Map<String, Object> securitySchemes = new HashMap<>();
            openAPI.getComponents().getSecuritySchemes().forEach((name, scheme) -> {
                securitySchemes.put(name, convertSecurityScheme(scheme));
            });
            definition.setSecuritySchemes(securitySchemes);
        }

        return definition;
    }

    private void convertPathItem(String path, PathItem pathItem,
                                 Map<String, PluginApiOperation> operations, OpenAPI openAPI) {
        // GET
        if (pathItem.getGet() != null) {
            operations.put(
                    getOperationId(pathItem.getGet(), "get", path),
                    convertOperation("GET", path, pathItem.getGet(), openAPI)
            );
        }

        // POST
        if (pathItem.getPost() != null) {
            operations.put(
                    getOperationId(pathItem.getPost(), "post", path),
                    convertOperation("POST", path, pathItem.getPost(), openAPI)
            );
        }

        // PUT
        if (pathItem.getPut() != null) {
            operations.put(
                    getOperationId(pathItem.getPut(), "put", path),
                    convertOperation("PUT", path, pathItem.getPut(), openAPI)
            );
        }

        // DELETE
        if (pathItem.getDelete() != null) {
            operations.put(
                    getOperationId(pathItem.getDelete(), "delete", path),
                    convertOperation("DELETE", path, pathItem.getDelete(), openAPI)
            );
        }

        // PATCH
        if (pathItem.getPatch() != null) {
            operations.put(
                    getOperationId(pathItem.getPatch(), "patch", path),
                    convertOperation("PATCH", path, pathItem.getPatch(), openAPI)
            );
        }
    }

    private String getOperationId(Operation operation, String method, String path) {
        if (StringUtils.hasText(operation.getOperationId())) {
            return operation.getOperationId();
        }
        // 生成默认的operationId
        return method + "_" + path.replaceAll("[^a-zA-Z0-9]", "_");
    }

    private PluginApiOperation convertOperation(String method, String path, Operation operation, OpenAPI openAPI) {
        PluginApiOperation apiOp = new PluginApiOperation();
        apiOp.setMethod(method);
        apiOp.setPath(path);
        apiOp.setSummary(operation.getSummary());
        apiOp.setDescription(operation.getDescription());

        // 转换参数
        Map<String, PluginApiParameter> parameters = new HashMap<>();
        // 处理URL参数（query、path、header等）
        if (operation.getParameters() != null) {
            operation.getParameters().forEach(param -> {
                parameters.put(param.getName(), convertParameter(param));
            });
        }

        // 处理请求体参数 - 关键修复
        if (operation.getRequestBody() != null) {
            parameters.putAll(convertRequestBody(operation.getRequestBody(), openAPI));
        }

        apiOp.setParameters(parameters);

        // 转换安全要求
        if (operation.getSecurity() != null) {
            List<String> security = new ArrayList<>();
            operation.getSecurity().forEach(secReq -> {
                security.addAll(secReq.keySet());
            });
            apiOp.setSecurity(security);
        }

        return apiOp;
    }

    private PluginApiParameter convertParameter(io.swagger.v3.oas.models.parameters.Parameter swaggerParam) {
        PluginApiParameter param = new PluginApiParameter();
        param.setName(swaggerParam.getName());
        param.setIn(swaggerParam.getIn());
        param.setRequired(Boolean.TRUE.equals(swaggerParam.getRequired()));
        param.setDescription(swaggerParam.getDescription());

        if (swaggerParam.getSchema() != null) {
            param.setType(swaggerParam.getSchema().getType());
            param.setDefaultValue(swaggerParam.getSchema().getDefault());
        }

        return param;
    }

    private Map<String, PluginApiParameter> convertRequestBody(RequestBody requestBody, OpenAPI openAPI) {
        Map<String, PluginApiParameter> bodyParams = new HashMap<>();

        if (requestBody.getContent() != null) {
            requestBody.getContent().forEach((mediaType, mediaTypeObject) -> {
                if (mediaTypeObject.getSchema() != null) {
                    Schema<?> schema = mediaTypeObject.getSchema();

                    // 处理引用类型的schema
                    if (schema.get$ref() != null) {
                        Schema<?> resolvedSchema = resolveSchemaReference(schema.get$ref(), openAPI);
                        if (resolvedSchema != null) {
                            bodyParams.putAll(extractSchemaParameters(resolvedSchema, "body"));
                        } else {
                            // 如果无法解析引用，创建一个通用的body参数
                            PluginApiParameter bodyParam = new PluginApiParameter();
                            bodyParam.setName("requestBody");
                            bodyParam.setIn("body");
                            bodyParam.setType("object");
                            bodyParam.setRequired(requestBody.getRequired() != null ? requestBody.getRequired() : false);
                            bodyParam.setDescription("请求体参数，引用类型: " + schema.get$ref());
                            bodyParams.put("requestBody", bodyParam);
                        }
                    } else {
                        // 处理直接定义的schema
                        bodyParams.putAll(extractSchemaParameters(schema, "body"));
                    }
                }
            });
        }

        return bodyParams;
    }

    private Map<String, PluginApiParameter> extractSchemaParameters(Schema<?> schema, String location) {
        Map<String, PluginApiParameter> params = new HashMap<>();

        if (schema.getProperties() != null) {
            // 处理对象类型的schema，提取每个属性
            schema.getProperties().forEach((propName, propSchema) -> {
                PluginApiParameter param = new PluginApiParameter();
                param.setName(propName);
                param.setIn(location);
                param.setType(propSchema.getType() != null ? propSchema.getType() : "string");
                param.setRequired(schema.getRequired() != null && schema.getRequired().contains(propName));
                param.setDescription(propSchema.getDescription());
                param.setDefaultValue(propSchema.getDefault());

                params.put(propName, param);
            });
        } else {
            // 如果没有具体的属性定义，创建一个通用的body参数
            PluginApiParameter param = new PluginApiParameter();
            param.setName("body");
            param.setIn(location);
            param.setType(schema.getType() != null ? schema.getType() : "object");
            param.setRequired(true);
            param.setDescription(schema.getDescription());

            params.put("body", param);
        }

        return params;
    }

    private Map<String, Object> convertSecurityScheme(SecurityScheme scheme) {
        Map<String, Object> result = new HashMap<>();
        result.put("type", scheme.getType().toString());
        result.put("scheme", scheme.getScheme());
        result.put("bearerFormat", scheme.getBearerFormat());
        result.put("in", scheme.getIn() != null ? scheme.getIn().toString() : null);
        result.put("name", scheme.getName());
        return result;
    }

    private Schema<?> resolveSchemaReference(String ref, OpenAPI openAPI) {
        try {
            if (ref.startsWith("#/components/schemas/")) {
                String schemaName = ref.substring("#/components/schemas/".length());
                if (openAPI.getComponents() != null &&
                        openAPI.getComponents().getSchemas() != null) {
                    return openAPI.getComponents().getSchemas().get(schemaName);
                }
            }
        } catch (Exception e) {
            log.warn("解析schema引用失败: {}", ref, e);
        }
        return null;
    }
}
