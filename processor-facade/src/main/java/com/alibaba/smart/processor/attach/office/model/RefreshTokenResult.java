package com.alibaba.smart.processor.attach.office.model;

import com.aliyun.imm20200930.models.RefreshWebofficeTokenResponseBody;
import com.aliyuncs.imm.model.v20170906.RefreshOfficeEditTokenResponse;
import com.aliyuncs.imm.model.v20170906.RefreshOfficePreviewTokenResponse;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020/3/30 11:15 上午
 */
public class RefreshTokenResult extends OfficeCallResult {

    public RefreshTokenResult() {
    }

    public RefreshTokenResult(RefreshOfficePreviewTokenResponse response) {
        this.requestId = response.getRequestId();
        this.accessToken = response.getAccessToken();
        this.accessTokenExpiredTime = response.getAccessTokenExpiredTime();
        this.refreshToken = response.getRefreshToken();
        this.refreshTokenExpiredTime = response.getRefreshTokenExpiredTime();
    }

    public RefreshTokenResult(RefreshOfficeEditTokenResponse response) {
        this.requestId = response.getRequestId();
        this.accessToken = response.getAccessToken();
        this.accessTokenExpiredTime = response.getAccessTokenExpiredTime();
        this.refreshToken = response.getRefreshToken();
        this.refreshTokenExpiredTime = response.getRefreshTokenExpiredTime();
    }

    public RefreshTokenResult(RefreshWebofficeTokenResponseBody response) {
        this.requestId = response.getRequestId();
        this.accessToken = response.getAccessToken();
        this.accessTokenExpiredTime = response.getAccessTokenExpiredTime();
        this.refreshToken = response.getRefreshToken();
        this.refreshTokenExpiredTime = response.getRefreshTokenExpiredTime();
    }

    @Override
    public Map<String, String> toResponse() {
        Map<String, String> result = new HashMap<>(5);
        result.put("requestId", getRequestId());
        result.put("accessTokenExpiredTime", getAccessTokenExpiredTime());
        result.put("accessToken", getAccessToken());
        result.put("refreshTokenExpiredTime", getRefreshTokenExpiredTime());
        result.put("refreshToken", getRefreshToken());
        return result;
    }

}
