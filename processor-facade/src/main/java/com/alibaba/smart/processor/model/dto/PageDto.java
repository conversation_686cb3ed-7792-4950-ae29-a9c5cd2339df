package com.alibaba.smart.processor.model.dto;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

/**
 * 返回页面级别的对象集合封装
 *
 * @param <TEntity>
 * <AUTHOR>
 */
public class PageDto<TEntity> implements Serializable {

    private static final long serialVersionUID = -8149418271579479189L;

    /**
     * 返回对象集合
     */
    private List<TEntity> data;

    /**
     * 对象的总数量
     */
    private long totalCount;

    /**
     * 当前页面
     */
    private long currentPage;


    private long idCursor;

    private boolean hasMore;

    public PageDto() {
        this.setData(new ArrayList<>());
    }

    public PageDto(List<TEntity> entities, long totalCount) {
        this.setData(entities);
        this.setTotalCount(totalCount);
    }

    public List<TEntity> getData() {
        return data;
    }

    public void setData(List<TEntity> data) {
        this.data = data;
    }

    public long getTotalCount() {
        return totalCount;
    }

    public void setTotalCount(long totalCount) {
        this.totalCount = totalCount;
    }

    public long getCurrentPage() {
        return currentPage;
    }

    public void setCurrentPage(long currentPage) {
        this.currentPage = currentPage;
    }

    public long getIdCursor() {
        return idCursor;
    }

    public void setIdCursor(long idCursor) {
        this.idCursor = idCursor;
    }

    @Override
    public String toString() {
        return "PageDto{" +
                "data=" + data +
                ", totalCount=" + totalCount +
                ", currentPage=" + currentPage +
                '}';
    }

    public boolean isHasMore() {
        return hasMore;
    }

    public void setHasMore(boolean hasMore) {
        this.hasMore = hasMore;
    }
}
