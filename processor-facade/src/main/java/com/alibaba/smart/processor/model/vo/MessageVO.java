package com.alibaba.smart.processor.model.vo;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class MessageVO {

    /**
     * 消息id
     */
    private String messageId;

    /**
     * 消息发送者类型
     */
    private String type;

    /**
     * 消息创建时间
     */
    private Date gmtCreate;

    /**
     * 执行结果的内容，格式为markdown
     */
    private String content;

    /**
     * 消息涉及的文件ID列表（user使用）
     */
    private List<String> files = new ArrayList<>();

    /**
     * 是否在处理中（system使用）
     */
    private boolean processing;

    /**
     * 运行日志历史记录列表
     */
    private String historyResponses;

    public String getMessageId() {
        return messageId;
    }

    public void setMessageId(String messageId) {
        this.messageId = messageId;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public List<String> getFiles() {
        return files;
    }

    public void setFiles(List<String> files) {
        this.files = files;
    }

    public Date getGmtCreate() {
        return gmtCreate;
    }

    public void setGmtCreate(Date gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    public boolean isProcessing() {
        return processing;
    }

    public void setProcessing(boolean processing) {
        this.processing = processing;
    }

    public String getHistoryResponses() {
        return historyResponses;
    }

    public void setHistoryResponses(String historyResponses) {
        this.historyResponses = historyResponses;
    }
}

