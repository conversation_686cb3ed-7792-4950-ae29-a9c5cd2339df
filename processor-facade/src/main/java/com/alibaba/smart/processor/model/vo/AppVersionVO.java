package com.alibaba.smart.processor.model.vo;

import java.util.Date;

public class AppVersionVO {

    /**
     * 应用id
     */
    private String appId;

    /**
     * 版本号
     */
    private String version;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 修改时间
     */
    private Date gmtModified;

    /**
     * 状态
     */
    private String status;

    /**
     * 更新说明
     */
    private String updateNote;

    /**
     * 资源id
     */
    private String resourceId;

    /**
     * 资源id
     */
    private String resourceKey;

    /**
     * 修改人
     */
    private String modifier;

    /**
     * 人员信息
     */
    private UserInfoVO UserInfo;

    public String getResourceKey() {
        return resourceKey;
    }

    public void setResourceKey(String resourceKey) {
        this.resourceKey = resourceKey;
    }

    public String getModifier() {
        return modifier;
    }

    public void setModifier(String modifier) {
        this.modifier = modifier;
    }

    public UserInfoVO getUserInfo() {
        return UserInfo;
    }

    public void setUserInfo(UserInfoVO userInfo) {
        UserInfo = userInfo;
    }

    public String getResourceId() {
        return resourceId;
    }

    public void setResourceId(String resourceId) {
        this.resourceId = resourceId;
    }

    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public Date getGmtCreate() {
        return gmtCreate;
    }

    public void setGmtCreate(Date gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    public Date getGmtModified() {
        return gmtModified;
    }

    public void setGmtModified(Date gmtModified) {
        this.gmtModified = gmtModified;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getUpdateNote() {
        return updateNote;
    }

    public void setUpdateNote(String updateNote) {
        this.updateNote = updateNote;
    }
}
