package com.alibaba.smart.processor.plugins.execute.model;

import java.io.Serializable;

public class ApiCallResponse implements Serializable {
    /**
     * 状态码
     */
    private int statusCode;
    /**
     * 返回结果
     */
    private Object body;
    /**
     * 结果标识
     */
    private boolean success;
    /**
     * 错误信息
     */
    private String errorMessage;

    public int getStatusCode() {
        return statusCode;
    }

    public void setStatusCode(int statusCode) {
        this.statusCode = statusCode;
    }

    public Object getBody() {
        return body;
    }

    public void setBody(Object body) {
        this.body = body;
    }

    public boolean isSuccess() {
        return success;
    }

    public void setSuccess(boolean success) {
        this.success = success;
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }
}
