package com.alibaba.smart.processor.app;
import com.alibaba.smart.processor.model.dto.AppInfoDTO;
import com.alibaba.smart.processor.model.dto.AppVersionDTO;
import com.alibaba.smart.processor.model.dto.AppVersionRevertDTO;
import com.alibaba.smart.processor.model.dto.PageDto;
import com.alibaba.smart.processor.model.vo.AppInfoVO;
import com.alibaba.smart.processor.model.vo.AppVersionVO;

import java.util.List;

public interface AppFacade {
    AppInfoVO createApp(AppInfoDTO appInfoDTO);

    AppInfoVO getByAppId(String appId);

    AppInfoVO getBySessionId(String sessionId);

    void updateApp(AppInfoDTO appInfoDTO);

    void updateApp(String appId, String name, String desc);

    void deleteApp(String appId);

    PageDto<AppInfoVO> pageApp(int currentPage, int pageSize);

    void deployApp(String appId, String sourceCompiledKey, String updateNode);

    PageDto<AppVersionVO> getAppVersions(String appId, Integer page, Integer pageSize, String key);

    void updateVersionNode(AppVersionDTO appVersionDTO);

    void revertVersion(AppVersionRevertDTO appVersionRevertDTO);

    AppVersionVO getAppVersion(String appId, String version);
}
