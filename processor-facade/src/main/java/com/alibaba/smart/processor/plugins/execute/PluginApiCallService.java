package com.alibaba.smart.processor.plugins.execute;

import com.alibaba.smart.processor.model.PluginsDO;
import com.alibaba.smart.processor.model.vo.PluginsVO;
import com.alibaba.smart.processor.plugins.execute.auth.AuthManager;
import com.alibaba.smart.processor.plugins.execute.model.*;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;

import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Service
@Slf4j
public class PluginApiCallService {
    private final PluginApiDefinitionParser pluginApiDefinitionParser;
    private final AuthManager authManager;
    private final HttpClientService httpClientService;
    private final ObjectMapper objectMapper;

    // 缓存解析后的API定义
    private final Map<String, PluginApiDefinition> apiDefinitionCache = new ConcurrentHashMap<>();

    public PluginApiCallService(PluginApiDefinitionParser apiParserService, AuthManager authManager,
                                HttpClientService httpClientService, ObjectMapper objectMapper) {
        this.pluginApiDefinitionParser = apiParserService;
        this.authManager = authManager;
        this.httpClientService = httpClientService;
        this.objectMapper = objectMapper;
    }

    public ApiCallResponse callPluginApi(PluginsDO plugin, ApiCallRequest request) {
        try {
            // 获取或解析API定义
            PluginApiDefinition apiDefinition = getApiDefinition(plugin);

            // 获取操作定义
            PluginApiOperation operation = apiDefinition.getOperations().get(request.getOperationId());
            if (operation == null) {
                throw new IllegalArgumentException("未找到操作: " + request.getOperationId());
            }

            // 验证和转换参数类型
            validateAndConvertParameters(operation, request.getParameters());

            // 构建请求URL
            String url = buildRequestUrl(apiDefinition.getBaseUrl(), operation, request.getParameters());

            // 构建请求头
            HttpHeaders headers = buildRequestHeaders(operation, request);

            // 构建请求体
            Object requestBody = buildRequestBody(operation, request.getParameters());

            ApiCallContext authContext = new ApiCallContext(url, headers, requestBody);
            // 应用鉴权 - 这里可能会修改URL、headers、requestBody等
            authManager.applyAuthentication(authContext, plugin.getAuthConfig());

            log.info("调用插件API: {} {} {}", plugin.getName(), operation.getMethod(), url);

            // 执行HTTP请求
            return httpClientService.executeRequest(url, operation.getMethod(), headers, requestBody);

        } catch (Exception e) {
            log.error("调用插件API失败: pluginId={}, operationId={}", request.getPluginId(), request.getOperationId(), e);

            ApiCallResponse errorResponse = new ApiCallResponse();
            errorResponse.setSuccess(false);
            errorResponse.setStatusCode(500);
            errorResponse.setErrorMessage("调用插件API失败: " + e.getMessage());
            return errorResponse;
        }
    }

    private PluginApiDefinition getApiDefinition(PluginsDO plugin) {
        return apiDefinitionCache.computeIfAbsent(plugin.getPluginId(),
                k -> pluginApiDefinitionParser.parseApiDefinition(plugin.getContent()));
    }

    private String buildRequestUrl(String baseUrl, PluginApiOperation operation, Map<String, Object> parameters) {
        String path = operation.getPath();
        StringBuilder queryParams = new StringBuilder();

        for (Map.Entry<String, PluginApiParameter> entry : operation.getParameters().entrySet()) {
            PluginApiParameter param = entry.getValue();
            Object rawValue = parameters.get(param.getName());

            if (rawValue == null && param.isRequired()) {
                throw new IllegalArgumentException("缺少必需参数: " + param.getName());
            }

            if (rawValue != null) {
                // 使用 objectMapper 进行类型转换
                Object convertedValue = convertParameterValue(param, rawValue);
                String stringValue = convertedValue.toString();

                if ("path".equals(param.getIn())) {
                    path = path.replace("{" + param.getName() + "}", stringValue);
                } else if ("query".equals(param.getIn())) {
                    if (queryParams.length() > 0) {
                        queryParams.append("&");
                    }
                    try {
                        queryParams.append(param.getName())
                                .append("=")
                                .append(URLEncoder.encode(stringValue, StandardCharsets.UTF_8));
                    } catch (Exception e) {
                        log.warn("URL编码失败: {}", stringValue, e);
                        queryParams.append(param.getName()).append("=").append(stringValue);
                    }
                }
            }
        }

        String fullUrl = baseUrl + path;
        if (queryParams.length() > 0) {
            fullUrl += "?" + queryParams;
        }

        return fullUrl;
    }

    private HttpHeaders buildRequestHeaders( PluginApiOperation operation, ApiCallRequest request) {
        HttpHeaders headers = new HttpHeaders();

        // 设置默认Content-Type
        headers.setContentType(MediaType.APPLICATION_JSON);

        // 添加操作级别的头部参数
        for (Map.Entry<String, PluginApiParameter> entry : operation.getParameters().entrySet()) {
            PluginApiParameter param = entry.getValue();
            if ("header".equals(param.getIn())) {
                Object value = request.getParameters().get(param.getName());
                if (value != null) {
                    headers.set(param.getName(), String.valueOf(value));
                }
            }
        }

        // 添加请求中的自定义头部
        if (request.getHeaders() != null) {
            request.getHeaders().forEach(headers::set);
        }

        return headers;
    }

    private Object buildRequestBody(PluginApiOperation operation, Map<String, Object> parameters) {
        Map<String, Object> bodyParams = new HashMap<>();

        for (Map.Entry<String, PluginApiParameter> entry : operation.getParameters().entrySet()) {
            PluginApiParameter param = entry.getValue();
            if ("body".equals(param.getIn()) || "requestBody".equals(param.getIn())) {
                Object value = parameters.get(param.getName());
                if (value != null) {
                    bodyParams.put(param.getName(), value);
                }
            }
        }
        return bodyParams;
    }

    private Object convertParameterValue(PluginApiParameter param, Object value) {
        if (value == null) {
            return null;
        }

        try {
            switch (param.getType()) {
                case "integer":
                    return objectMapper.convertValue(value, Integer.class);
                case "number":
                    return objectMapper.convertValue(value, Double.class);
                case "boolean":
                    return objectMapper.convertValue(value, Boolean.class);
                case "array":
                    return objectMapper.convertValue(value, List.class);
                case "object":
                    return objectMapper.convertValue(value, Map.class);
                default:
                    return value.toString();
            }
        } catch (Exception e) {
            log.warn("参数类型转换失败: {} -> {}", value, param.getType(), e);
            return value;
        }
    }

    private void validateAndConvertParameters(PluginApiOperation operation, Map<String, Object> parameters) {
        operation.getParameters().forEach((name, param) -> {
            Object value = parameters.get(name);
            if (value != null) {
                Object convertedValue = convertParameterValue(param, value);
                parameters.put(name, convertedValue);
            }
        });
    }

    // 清除缓存
    public void clearCache(String pluginId) {
        apiDefinitionCache.remove(pluginId);
    }

    public void clearAllCache() {
        apiDefinitionCache.clear();
    }
}
