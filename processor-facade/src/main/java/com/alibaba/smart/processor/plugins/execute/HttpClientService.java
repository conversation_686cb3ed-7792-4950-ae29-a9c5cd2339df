package com.alibaba.smart.processor.plugins.execute;

import com.alibaba.fastjson.JSON;
import com.alibaba.security.SecurityUtil;
import com.alibaba.smart.processor.diamond.DiamondEnums;
import com.alibaba.smart.processor.exception.BusinessException;
import com.alibaba.smart.processor.exception.ExceptionContext;
import com.alibaba.smart.processor.plugins.execute.model.ApiCallResponse;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.HttpServerErrorException;
import org.springframework.web.client.RestTemplate;

import java.time.Duration;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
@Slf4j
public class HttpClientService {
    private final RestTemplate restTemplate;
    private final ObjectMapper objectMapper;

    public HttpClientService(RestTemplateBuilder restTemplateBuilder, ObjectMapper objectMapper) {
        CloseableHttpClient httpClient = HttpClientBuilder.create()
                .setDefaultRequestConfig(RequestConfig.custom()
                        .setRedirectsEnabled(false)
                        .setConnectTimeout(10000)
                        .setSocketTimeout(30000)
                        .build())
                .build();

        HttpComponentsClientHttpRequestFactory requestFactory = new HttpComponentsClientHttpRequestFactory(httpClient);
        this.restTemplate = new RestTemplate(requestFactory);
        this.objectMapper = objectMapper;
    }

    public ApiCallResponse executeRequest(String url, String method, HttpHeaders headers, Object body) {
        try {
            HttpEntity<?> entity = new HttpEntity<>(body, headers);
            ResponseEntity<String> response;

            boolean ignoreSSRFCheck = false;
            List<String> whiteUrlList = JSON.parseArray(DiamondEnums.polymind_http_url_white_list.getValue(), String.class);
            if(!CollectionUtils.isEmpty(whiteUrlList)){
                ignoreSSRFCheck = whiteUrlList.contains(url);
            }
            if(!ignoreSSRFCheck){
                boolean checkResult = SecurityUtil.checkSSRFWithoutConnection(url, false);
                if(!checkResult){
                    throw new BusinessException(new ExceptionContext("请求地址不合法"),"000200");
                }
            }

            switch (method.toUpperCase()) {
                case "GET":
                    response = restTemplate.exchange(url, HttpMethod.GET, entity, String.class);
                    break;
                case "POST":
                    response = restTemplate.exchange(url, HttpMethod.POST, entity, String.class);
                    break;
                case "PUT":
                    response = restTemplate.exchange(url, HttpMethod.PUT, entity, String.class);
                    break;
                case "DELETE":
                    response = restTemplate.exchange(url, HttpMethod.DELETE, entity, String.class);
                    break;
                case "PATCH":
                    response = restTemplate.exchange(url, HttpMethod.PATCH, entity, String.class);
                    break;
                default:
                    throw new IllegalArgumentException("不支持的HTTP方法: " + method);
            }

            return buildSuccessResponse(response);

        } catch (HttpClientErrorException | HttpServerErrorException e) {
            return buildErrorResponse(e.getStatusCode().value(), e.getStatusText(), e.getResponseBodyAsString());
        } catch (Exception e) {
            log.error("HTTP请求执行失败", e);
            return buildErrorResponse(500, "Internal Server Error", e.getMessage());
        }
    }

    private ApiCallResponse buildSuccessResponse(ResponseEntity<String> response) {
        ApiCallResponse apiResponse = new ApiCallResponse();
        apiResponse.setStatusCode(response.getStatusCode().value());
        apiResponse.setSuccess(true);

        // 解析响应体
        String responseBody = response.getBody();
        if (StringUtils.hasText(responseBody)) {
            try {
                // 尝试解析为JSON
                apiResponse.setBody(objectMapper.readValue(responseBody, Object.class));
            } catch (Exception e) {
                // 如果不是JSON，直接返回字符串
                apiResponse.setBody(responseBody);
            }
        }

        return apiResponse;
    }

    private ApiCallResponse buildErrorResponse(int statusCode, String statusText, String errorMessage) {
        ApiCallResponse apiResponse = new ApiCallResponse();
        apiResponse.setStatusCode(statusCode);
        apiResponse.setSuccess(false);
        apiResponse.setErrorMessage(errorMessage);
        return apiResponse;
    }
}
