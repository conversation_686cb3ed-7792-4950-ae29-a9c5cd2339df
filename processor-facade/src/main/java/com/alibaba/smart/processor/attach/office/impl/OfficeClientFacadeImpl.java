package com.alibaba.smart.processor.attach.office.impl;

import com.alibaba.smart.processor.attach.AttachmentFacade;
import com.alibaba.smart.processor.attach.office.OfficeClientFacade;
import com.alibaba.smart.processor.config.OfficeClientConfig;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2020/4/8 12:36 上午
 */
@Service
public class OfficeClientFacadeImpl implements OfficeClientFacade {
    private static final String OSS_PROTOCOL = "oss://";

    @Resource
    private OfficeClientConfig officeClientConfig;
    @Resource
    private AttachmentFacade attachmentFacade;

    @Override
    public String getOfficeOssUri(String filePath) {
        // 标准化 filePath, 不以 "/" 开头
        if (filePath.startsWith("/")) {
            filePath = filePath.substring(1);
        }
        return OSS_PROTOCOL + officeClientConfig.getOssBucketName() + "/" + filePath;
    }

    @Override
    public String copyFileToOfficeOss(String filePath) {
        try {
            return attachmentFacade.upload2Imm(filePath);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
}
