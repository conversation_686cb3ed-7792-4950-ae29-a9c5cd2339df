package com.alibaba.smart.processor.plugins.execute.auth;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.smart.processor.plugins.execute.HttpClientService;
import com.alibaba.smart.processor.plugins.execute.model.ApiCallContext;
import com.alibaba.smart.processor.plugins.execute.model.ApiCallResponse;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

@Component
public class DingTalkAuthStrategy implements AuthStrategy {
    @Resource
    private HttpClientService httpClientService;

    private static final String DINGTALK_APP_KEY = "dingxcfkkq89pcp6vezn";
    private static final String DINGTALK_APP_SECRET = "UD7dS6vY4ZBWHzlHqds5LXzBKj352vijJgtbRLCveEBbsnz8Hm_ddWRRVu1Z_U3T";

    @Override
    public void applyAuth(ApiCallContext context, Map<String, Object> config) {
        String accessToken = getAccessToken();
        context.getHeaders().set("x-acs-dingtalk-access-token", accessToken);
    }

    private String getAccessToken() {
        String url = "https://api.dingtalk.com/v1.0/oauth2/accessToken";
        HttpHeaders headers = new HttpHeaders();
        headers.add("Content-Type", "application/json");
        Map<String, String> params = new HashMap<String, String>();
        params.put("appKey", DINGTALK_APP_KEY);
        params.put("appSecret", DINGTALK_APP_SECRET);

        ObjectMapper objectMapper = new ObjectMapper();
        Object body = objectMapper.convertValue(params, Object.class);
        ApiCallResponse response = httpClientService.executeRequest(url, "POST", headers, body);
        if(response.isSuccess()) {
            JSONObject resultJson = JSON.parseObject(JSON.toJSONString(response.getBody()));
            return resultJson == null ? "" : resultJson.getString("accessToken");
        }
        return null;
    }

    @Override
    public String getAuthType() {
        return "DINGTALK";
    }
}
