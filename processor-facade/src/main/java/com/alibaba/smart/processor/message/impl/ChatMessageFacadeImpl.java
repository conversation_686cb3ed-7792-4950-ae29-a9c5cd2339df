package com.alibaba.smart.processor.message.impl;

import com.alibaba.smart.processor.message.ChatMessageFacade;
import com.alibaba.smart.processor.message.ChatMessageService;
import com.alibaba.smart.processor.model.ChatMessageDO;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service
public class ChatMessageFacadeImpl implements ChatMessageFacade {

    @Resource
    private ChatMessageService chatMessageService;

    @Override
    public String saveMessage(String sessionId, String senderType) {
        return chatMessageService.saveMessage(sessionId, senderType);
    }

    @Override
    public List<ChatMessageDO> listMessage(String sessionId) {
        return chatMessageService.listMessage(sessionId);
    }
}
