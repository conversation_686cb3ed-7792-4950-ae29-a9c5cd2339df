package com.alibaba.smart.processor.model.dto;

import lombok.Data;

import java.util.List;

@Data
public class ChatMessageDTO {

    /**
     * 会话ID
     */
    private String sessionId;

    /**
     * 消息ID
     */
    private String messageId;

    /**
     * 发送者类型
     */
    private String type;

    /**
     * 消息内容
     */
    private String content;

    /**
     * 用户输入时，携带的附件
     */
    private List<FileDTO> files;

    /**
     * 历史消息记录，格式：["xxx","bbb"]
     */
    private String historyResponses;
}
