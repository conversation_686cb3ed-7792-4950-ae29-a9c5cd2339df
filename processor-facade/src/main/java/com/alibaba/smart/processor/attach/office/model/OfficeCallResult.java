package com.alibaba.smart.processor.attach.office.model;

import com.alibaba.fastjson.util.TypeUtils;

import java.util.Date;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020/3/30 11:18 上午
 */

public abstract class OfficeCallResult {

    protected String requestId;

    protected String accessToken;

    protected String refreshToken;

    protected String accessTokenExpiredTime;

    protected String refreshTokenExpiredTime;

    public Date asDateByAccessTokenExpiredTime() {
        return TypeUtils.castToDate(accessTokenExpiredTime);
    }

    public Date asDateByRefreshTokenExpiredTime() {
        return TypeUtils.castToDate(refreshTokenExpiredTime);
    }

    public abstract Map<String, String> toResponse();

    public String getRequestId() {
        return requestId;
    }

    public void setRequestId(String requestId) {
        this.requestId = requestId;
    }

    public String getAccessToken() {
        return accessToken;
    }

    public void setAccessToken(String accessToken) {
        this.accessToken = accessToken;
    }

    public String getRefreshToken() {
        return refreshToken;
    }

    public void setRefreshToken(String refreshToken) {
        this.refreshToken = refreshToken;
    }

    public String getAccessTokenExpiredTime() {
        return accessTokenExpiredTime;
    }

    public void setAccessTokenExpiredTime(String accessTokenExpiredTime) {
        this.accessTokenExpiredTime = accessTokenExpiredTime;
    }

    public String getRefreshTokenExpiredTime() {
        return refreshTokenExpiredTime;
    }

    public void setRefreshTokenExpiredTime(String refreshTokenExpiredTime) {
        this.refreshTokenExpiredTime = refreshTokenExpiredTime;
    }
}
