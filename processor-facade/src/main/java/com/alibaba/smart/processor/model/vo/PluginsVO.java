package com.alibaba.smart.processor.model.vo;

import java.util.Date;

public class PluginsVO {

    /**
     * 插件id
     */
    private String pluginId;

    /**
     * 插件名称
     */
    private String name;

    /**
     * 插件描述
     */
    private String description;

    /**
     * 插件图标
     */
    private String icon;

    /**
     * 插件类型
     */
    private String type;

    /**
     * 插件内容
     */
    private String content;
    /**
     * 创建时间
     */
    private Date gmtModified;

    /**
     * 修改人信息
     */
    private String modifier;

    /**
     * 人员信息
     */
    private UserInfoVO UserInfo;

    /**
     * 授权配置
     */
    private String authConfig;

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getModifier() {
        return modifier;
    }

    public void setModifier(String modifier) {
        this.modifier = modifier;
    }

    public String getPluginId() {
        return pluginId;
    }

    public void setPluginId(String pluginId) {
        this.pluginId = pluginId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getIcon() {
        return icon;
    }

    public void setIcon(String icon) {
        this.icon = icon;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public Date getGmtModified() {
        return gmtModified;
    }

    public void setGmtModified(Date gmtModified) {
        this.gmtModified = gmtModified;
    }

    public UserInfoVO getUserInfo() {
        return UserInfo;
    }

    public void setUserInfo(UserInfoVO userInfo) {
        UserInfo = userInfo;
    }

    public String getAuthConfig() {
        return authConfig;
    }

    public void setAuthConfig(String authConfig) {
        this.authConfig = authConfig;
    }
}
