package com.alibaba.smart.processor.history;

import com.alibaba.fastjson.JSON;
import com.alibaba.smart.processor.app.AppService;
import com.alibaba.smart.processor.app.AppVersionService;
import com.alibaba.smart.processor.attach.AttachmentService;
import com.alibaba.smart.processor.message.ChatMessageFacade;
import com.alibaba.smart.processor.message.ChatMessageService;
import com.alibaba.smart.processor.message.model.MessageTypeEnum;
import com.alibaba.smart.processor.model.*;
import com.alibaba.smart.processor.model.attach.FileRecord;
import com.alibaba.smart.processor.model.dto.FileDTO;
import com.alibaba.smart.processor.model.vo.*;
import com.alibaba.smart.processor.plugins.PluginsService;
import com.alibaba.smart.processor.session.ChatSessionService;
import com.aliyun.oss.OSSClient;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.net.URL;
import java.sql.Date;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class ChatHistoryFacadeImpl implements ChatHistoryFacade{

    @Resource
    private ChatHistoryService chatHistoryService;

    @Resource
    private ChatMessageFacade chatMessageFacade;

    @Resource
    private ChatSessionService chatSessionService;

    @Resource
    private ChatMessageService chatMessageService;

    @Resource
    private AttachmentService attachmentService;

    @Resource
    private AppService appService;

    @Resource
    private AppVersionService appVersionService;

    @Resource
    private OSSClient ossClient;

    @Value("${oss_default_bucket_name_private}")
    private String bucket;

    @Value("${oss_default_bucket_dir:smart_processor}")
    private String dir;

    @Resource
    private PluginsService pluginsService;

    @Override
    public List<ChatMessageHistoryDO> listCharHistory(String sessionId, String messageId) {
        return chatHistoryService.listCharHistory(sessionId, messageId);
    }

    @Override
    public void saveChatHistory(String sessionId, String messageId, String content) {
        chatHistoryService.saveChatHistory(sessionId, messageId, content);
    }

    @Override
    public ChatModelVO listMessage(String sessionId) {
        ChatModelVO chatModelVO = new ChatModelVO();
        ChatSessionDO session = chatSessionService.getSession(sessionId);
        if (Objects.isNull(session)) {
            return null;
        }
        chatModelVO.setSessionId(sessionId);
        chatModelVO.setTitle(session.getTitle());

        List<SessionPluginBindingDO> sessionPluginBindingDOS = pluginsService.listSessionPluginBinding(sessionId);
        if (CollectionUtils.isNotEmpty(sessionPluginBindingDOS)) {
            List<String> pluginIds = sessionPluginBindingDOS.stream()
                    .map(SessionPluginBindingDO::getPluginId)
                    .collect(Collectors.toList());
            chatModelVO.setPluginIds(pluginIds);
        }

        List<ChatMessageDO> chatMessageDOList = chatMessageFacade.listMessage(sessionId);
        if (CollectionUtils.isEmpty(chatMessageDOList)) {
            return chatModelVO;
        }

        List<MessageVO> messageVOList = new ArrayList<>();
        for (ChatMessageDO chatMessageDO : chatMessageDOList) {
            if (afterCurrentAppVersion(sessionId, chatMessageDO)) {
                continue;
            }

            MessageVO messageVO = new MessageVO();
            messageVO.setMessageId(chatMessageDO.getMessageId());
            messageVO.setType(chatMessageDO.getSenderType());
            messageVO.setGmtCreate(chatMessageDO.getGmtCreate());
            messageVO.setContent(chatMessageDO.getMessage());
            List<String> fileIds = buildFiles(chatMessageDO.getFiles());
            if (CollectionUtils.isNotEmpty(fileIds)) {
                messageVO.setFiles(fileIds);
                for (String fileId : fileIds) {
                    File file = new File();
                    file.setFileId(fileId);
                    FileRecord fileRecord = attachmentService.getAttachmentByKey(fileId);
                    if (Objects.nonNull(fileRecord)) {
                        file.setFileName(fileRecord.getFileName());
                        file.setFileType(fileRecord.getFileExtType());
                        file.setFileSize(fileRecord.getFileSize());
                        long expireTime = 60 * 60 * 1000;
                        URL url = ossClient.generatePresignedUrl(bucket, dir + "/" + fileId, new Date(System.currentTimeMillis() + expireTime));
                        file.setDownloadUrl(url.toString());
                        file.setPreviewUrl("/filePreview?objectName="+fileId);
                    }
                    chatModelVO.getFiles().add(file);

                    if (StringUtils.equalsIgnoreCase(messageVO.getType(), MessageTypeEnum.USER.name())) {
                        chatModelVO.getAttachmentFileIds().add(fileId);
                    } else if (StringUtils.equalsIgnoreCase(messageVO.getType(), MessageTypeEnum.SYSTEM.name())) {
                        chatModelVO.getGenerateFileIds().add(fileId);
                    }
                }
            }

            List<ChatMessageHistoryDO> historyDOS = chatHistoryService.listCharHistory(sessionId, chatMessageDO.getMessageId());
            if(CollectionUtils.isNotEmpty(historyDOS)){
                List<String> historyContents = historyDOS.stream().map(ChatMessageHistoryDO::getContent).collect(Collectors.toList());
                messageVO.setHistoryResponses(JSON.toJSONString(historyContents));
            }
            messageVOList.add(messageVO);
        }
        messageVOList.sort(Comparator.comparing(MessageVO::getGmtCreate));
        chatModelVO.setMessages(messageVOList);
        return chatModelVO;
    }

    /**
     * 当前会话记录在当前线上应用创建时间之后，则过滤掉不展示
     * @param sessionId
     * @param chatMessageDO
     * @return
     */
    private boolean afterCurrentAppVersion(String sessionId, ChatMessageDO chatMessageDO) {
        AppInfoDO appInfoDO = appService.getBySessionId(sessionId);
        if (Objects.isNull(appInfoDO)) {
            return false;
        }

        AppVersionDO appVersionDO = appVersionService.getAppVersion(appInfoDO.getAppId(), appInfoDO.getVersion());
        if (Objects.isNull(appVersionDO)) {
            return false;
        }

        return chatMessageDO.getGmtCreate().getTime() > appVersionDO.getGmtCreate().getTime();
    }

    @Override
    public String saveMessage(String sessionId, String messageId, String senderType, String content) {
        ChatMessageDO message = chatMessageService.getMessage(messageId);
        if (Objects.nonNull(message)) {
            chatHistoryService.saveChatHistory(sessionId, messageId, content);
            return messageId;
        } else {
            String newMessageId = chatMessageService.saveMessage(sessionId, senderType);
            chatHistoryService.saveChatHistory(sessionId, newMessageId, content);
            return newMessageId;
        }
    }

    @Override
    public MessageVO addMessage(String sessionId, String messageId, String senderType, String content,
                                List<FileDTO> fileDTOS, String historyResponses) {
        String files= null;
        if (CollectionUtils.isNotEmpty(fileDTOS)) {
            files = fileDTOS.stream()
                    .map(FileDTO::getFileId)
                    .collect(Collectors.joining(","));
        }
        ChatMessageDO chatMessageDO = chatMessageService.saveMessage(sessionId, messageId, senderType, content, files, historyResponses);
        ChatSessionDO chatSessionDO = chatSessionService.getSession(sessionId);
        if(chatSessionDO != null && StringUtils.isBlank(chatSessionDO.getTitle())){
            int endIndex = Math.min(content.length(), 30);
            String sessionTitle = content.substring(0,endIndex);
            chatSessionService.updateSessionTitle(sessionId, sessionTitle);
        }

        MessageVO messageVO = new MessageVO();
        messageVO.setMessageId(chatMessageDO.getMessageId());
        messageVO.setType(chatMessageDO.getSenderType());
        messageVO.setGmtCreate(chatMessageDO.getGmtCreate());
        messageVO.setContent(chatMessageDO.getMessage());
        messageVO.setFiles(buildFiles(chatMessageDO.getFiles()));
        messageVO.setHistoryResponses(historyResponses);
        return messageVO;
    }

    @Override
    public void updateMessage(String sessionId, String messageId, String content) {
        chatMessageService.updateChatHistory(sessionId, messageId, content);
    }

    List<String> buildFiles(String files) {
        if (StringUtils.isEmpty(files)) {
            return null;
        }
        return Arrays.asList(files.split(","));
    }

    private List<File> convertFile(List<FileDTO> fileDTOS) {
        if(CollectionUtils.isEmpty(fileDTOS)){
            return new ArrayList<>();
        }
        return fileDTOS.stream().map(fileDTO -> {
            File file = new File();
            file.setFileId(fileDTO.getFileId());
            file.setFileName(fileDTO.getFileName());
            file.setFileType(fileDTO.getFileType());
            file.setFileSize(Long.valueOf(fileDTO.getFileSize()));
            file.setPreviewUrl(fileDTO.getPreviewUrl());
            file.setDownloadUrl(fileDTO.getDownloadUrl());
            return file;
        }).collect(Collectors.toList());
    }
}
