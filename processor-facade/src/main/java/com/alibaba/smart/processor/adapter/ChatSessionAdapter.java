package com.alibaba.smart.processor.adapter;

import com.alibaba.smart.processor.model.ChatSessionDO;
import com.alibaba.smart.processor.model.vo.ChatSessionVO;
import org.apache.commons.collections.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

public class ChatSessionAdapter {
    public static List<ChatSessionVO> toChatSessionVO(List<ChatSessionDO> sessions) {
        if(CollectionUtils.isEmpty(sessions)){
            return new ArrayList<>();
        }
        return sessions.stream().map(ChatSessionAdapter::toChatSessionVO).collect(Collectors.toList());
    }

    public static ChatSessionVO toChatSessionVO(ChatSessionDO session) {
        if(session == null){
            return null;
        }
        ChatSessionVO sessionVO = new ChatSessionVO();
        sessionVO.setSessionId(session.getSessionId());
        sessionVO.setGmtCreate(session.getGmtCreate());
        sessionVO.setGmtModified(session.getGmtModified());
        sessionVO.setTitle(session.getTitle());
        return sessionVO;
    }
}
