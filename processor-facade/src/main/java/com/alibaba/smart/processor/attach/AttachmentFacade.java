package com.alibaba.smart.processor.attach;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.smart.processor.model.dto.FileCompleteDTO;
import com.alibaba.smart.processor.model.vo.FileInfoVO;

import java.io.InputStream;

public interface AttachmentFacade {
    void saveFileInfo(FileCompleteDTO fileCompleteDTO);

    FileInfoVO getFileInfoByKey(String objectName);

    String upload2Imm(String ossKey);

    InputStream getFileInputStream(String objectName);
}
