package com.alibaba.smart.processor.attach.office.model;

import com.aliyun.imm20200930.models.GetWebofficeURLResponseBody;
import com.aliyuncs.imm.model.v20170906.GetOfficePreviewURLResponse;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020/3/30 11:15 上午
 */
public class OfficePreviewResult extends OfficeCallResult {

    private String previewURL;

    public OfficePreviewResult() {
    }

    public OfficePreviewResult(GetOfficePreviewURLResponse response) {
        this.previewURL = response.getPreviewURL();
        this.requestId = response.getRequestId();
        this.accessToken = response.getAccessToken();
        this.refreshToken = response.getRefreshToken();
        this.accessTokenExpiredTime = response.getAccessTokenExpiredTime();
        this.refreshTokenExpiredTime = response.getRefreshTokenExpiredTime();
    }

    public OfficePreviewResult(GetWebofficeURLResponseBody response) {
        this.previewURL = response.getWebofficeURL();
        this.requestId = response.getRequestId();
        this.accessToken = response.getAccessToken();
        this.refreshToken = response.getRefreshToken();
        this.accessTokenExpiredTime = response.getAccessTokenExpiredTime();
        this.refreshTokenExpiredTime = response.getRefreshTokenExpiredTime();
    }

    @Override
    public Map<String, String> toResponse() {
        Map<String, String> result = new HashMap<>(6);
        result.put("requestId", getRequestId());
        result.put("previewURL", getPreviewURL());
        result.put("accessToken", getAccessToken());
        result.put("accessTokenExpiredTime", getAccessTokenExpiredTime());
        result.put("refreshToken", getRefreshToken());
        result.put("refreshTokenExpiredTime", getRefreshTokenExpiredTime());
        return result;
    }

    public String getPreviewURL() {
        return previewURL;
    }

    public void setPreviewURL(String previewURL) {
        this.previewURL = previewURL;
    }
}
