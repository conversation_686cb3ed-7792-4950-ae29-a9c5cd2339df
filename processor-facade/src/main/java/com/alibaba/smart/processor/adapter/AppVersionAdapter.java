package com.alibaba.smart.processor.adapter;

import com.alibaba.smart.processor.model.AppVersionDO;
import com.alibaba.smart.processor.model.vo.AppVersionVO;

import java.util.List;
import java.util.stream.Collectors;

public class AppVersionAdapter {

    public static AppVersionVO toAppVersionVO(AppVersionDO appVersionDO) {
        AppVersionVO appVersionVO = new AppVersionVO();
        appVersionVO.setAppId(appVersionDO.getAppId());
        appVersionVO.setVersion(appVersionDO.getVersion());
        appVersionVO.setGmtCreate(appVersionDO.getGmtCreate());
        appVersionVO.setGmtModified(appVersionDO.getGmtModified());
        appVersionVO.setStatus(appVersionDO.getStatus());
        appVersionVO.setUpdateNote(appVersionDO.getUpdateNote());
        appVersionVO.setResourceId(appVersionDO.getResourceId());
        appVersionVO.setModifier(appVersionDO.getModifier());
        return appVersionVO;
    }

    public static List<AppVersionVO> toAppVersionVOList(List<AppVersionDO> appVersionDOList) {
        return appVersionDOList.stream()
                .map(AppVersionAdapter::toAppVersionVO)
                .collect(Collectors.toList());
    }
}
