package com.alibaba.smart.processor.attach.impl;

import com.alibaba.smart.processor.attach.AttachmentFacade;
import com.alibaba.smart.processor.attach.AttachmentService;
import com.alibaba.smart.processor.config.OfficeClientConfig;
import com.alibaba.smart.processor.config.OssClientConfig;
import com.alibaba.smart.processor.model.attach.FileRecord;
import com.alibaba.smart.processor.model.dto.FileCompleteDTO;
import com.alibaba.smart.processor.model.vo.FileInfoVO;
import com.alibaba.smart.processor.utils.FileUtils;
import com.aliyun.oss.OSSClient;
import com.aliyun.oss.model.OSSObject;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.InputStream;

@Service
public class AttachmentFacadeImpl implements AttachmentFacade {
    @Resource
    private AttachmentService attachmentService;
    @Resource(name = "officeOssClient")
    private OSSClient officeClient;
    @Resource
    private OfficeClientConfig officeClientConfig;
    @Resource
    private OssClientConfig ossClientConfig;

    @Override
    public void saveFileInfo(FileCompleteDTO fileCompleteDTO) {
        FileRecord fileRecord = new FileRecord();
        fileRecord.setFileName(fileCompleteDTO.getFileName());
        fileRecord.setFileExtType(FileUtils.getFileExtension(fileCompleteDTO.getFileName()));
        fileRecord.setFileSize(fileCompleteDTO.getFileSize());
        fileRecord.setOssKey(fileCompleteDTO.getObjectName());
        attachmentService.saveAttachment(fileRecord);
    }

    @Override
    public FileInfoVO getFileInfoByKey(String objectName) {
        if(StringUtils.isBlank(objectName)){
            return null;
        }
        FileRecord fileRecord = attachmentService.getAttachmentByKey(objectName);
        if(fileRecord == null){
            return null;
        }
        FileInfoVO fileInfoVO = new FileInfoVO();
        BeanUtils.copyProperties(fileRecord, fileInfoVO);
        return fileInfoVO;
    }

    @Override
    public String upload2Imm(String ossKey) {
        String fullKey = ossClientConfig.getDir() + "/" + ossKey;
        OSSObject ossObject = ossClientConfig.ossClient().getObject(ossClientConfig.getBucket(), fullKey);
        InputStream inputStream = ossObject.getObjectContent();
        String immKey = FileUtils.getOssFileId(ossKey, true);
        officeClient.putObject(officeClientConfig.getOssBucketName(), immKey, inputStream);

        attachmentService.updateAttachmentImmKey(ossKey, immKey);
        return immKey;
    }

    @Override
    public InputStream getFileInputStream(String objectName) {
        String fullKey = ossClientConfig.getDir() + "/" + objectName;
        OSSObject ossObject = ossClientConfig.ossClient().getObject(ossClientConfig.getBucket(), fullKey);
        return ossObject.getObjectContent();
    }
}
