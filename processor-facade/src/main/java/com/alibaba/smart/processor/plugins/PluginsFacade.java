package com.alibaba.smart.processor.plugins;

import com.alibaba.smart.processor.model.dto.PageDto;
import com.alibaba.smart.processor.model.dto.PluginsDTO;
import com.alibaba.smart.processor.model.dto.SessionPluginsBindingDTO;
import com.alibaba.smart.processor.model.vo.PluginsVO;
import com.alibaba.smart.processor.model.vo.SessionPluginsBindingVO;
import com.alibaba.smart.processor.plugins.execute.model.ApiCallRequest;
import com.alibaba.smart.processor.plugins.execute.model.ApiCallResponse;

import java.util.List;

public interface PluginsFacade {

    void createPlugins(PluginsDTO pluginsDTO);

    PageDto<PluginsVO> listPlugins(String key, Integer page, Integer pageSize);

    List<PluginsVO> listSystemPlugins();

    void deletePlugin(String pluginId);

    void updatePlugin(PluginsDTO pluginsDTO);

    void bindingCreate(SessionPluginsBindingDTO sessionPluginsBindingDTO);

    SessionPluginsBindingVO bindingList(String sessionId);

    /**
     * 移除会话中绑定的某插件
     * @param sessionId
     * @param pluginId
     */
    void deletePluginBind(String sessionId, String pluginId);

    void executePlugin(String pluginId, String params);

    ApiCallResponse callPlugin(ApiCallRequest apiCallRequest);
}
