package com.alibaba.smart.processor.config;

import com.aliyun.oss.OSSClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @date 2020/4/7 9:56 上午
 */
@Configuration
public class OfficeClientConfig {

    /**
     * OSS 访问 AK
     */
    @Value("${aliyun.ram.accessKey}")
    private String accessKeyId;

    /**
     * OSS 访问 SK
     */
    @Value("${aliyun.ram.secretKey}")
    private String accessKeySecret;

    /**
     * OSS 存储桶
     */
    @Value("${aliyun.oss.bucketName}")
    private String ossBucketName;

    /**
     * OSS 访问地址
     */
    @Value("${aliyun.oss.endpoint}")
    private String ossEndpoint;

    /**
     * OSS 文件域名
     */
    @Value("${aliyun.oss.httpurl}")
    private String ossHttpurl;

    @Bean(value = "officeOssClient")
    public OSSClient ossClient() {
        return new OSSClient(ossEndpoint, accessKeyId, accessKeySecret);
    }

    public String getAccessKeyId() {
        return accessKeyId;
    }

    public String getAccessKeySecret() {
        return accessKeySecret;
    }

    public String getOssBucketName() {
        return ossBucketName;
    }

    public String getOssEndpoint() {
        return ossEndpoint;
    }

    public String getOssHttpurl() {
        return ossHttpurl;
    }
}
