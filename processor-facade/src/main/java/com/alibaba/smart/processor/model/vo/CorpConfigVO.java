package com.alibaba.smart.processor.model.vo;

public class CorpConfigVO {
    /**
     * 百炼APIKEY
     */
    private transient String bailianApiKey;
    /**
     * OpenRouter APIKEY
     */
    private transient String openRouterApiKey;
    /**
     * 推理模型名称
     */
    private String reasoningModelName = "qwen3-235b-a22b";
    /**
     * 代码模型类型
     */
    private String coderModelType = "openrouter";
    /**
     * 代码模型名称
     */
    private String coderModelName = "anthropic/claude-3.7-sonnet";

    public String getBailianApiKey() {
        return bailianApiKey;
    }

    public void setBailianApiKey(String bailianApiKey) {
        this.bailianApiKey = bailianApiKey;
    }

    public String getOpenRouterApiKey() {
        return openRouterApiKey;
    }

    public void setOpenRouterApiKey(String openRouterApiKey) {
        this.openRouterApiKey = openRouterApiKey;
    }

    public String getReasoningModelName() {
        return reasoningModelName;
    }

    public void setReasoningModelName(String reasoningModelName) {
        this.reasoningModelName = reasoningModelName;
    }

    public String getCoderModelType() {
        return coderModelType;
    }

    public void setCoderModelType(String coderModelType) {
        this.coderModelType = coderModelType;
    }

    public String getCoderModelName() {
        return coderModelName;
    }

    public void setCoderModelName(String coderModelName) {
        this.coderModelName = coderModelName;
    }
}
