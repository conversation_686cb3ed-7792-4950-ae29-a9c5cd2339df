package com.alibaba.smart.processor.plugins.execute.auth;

import com.alibaba.smart.processor.plugins.execute.model.ApiCallContext;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component
public class HeaderAuthStrategy implements AuthStrategy {
    @Override
    public void applyAuth(ApiCallContext context, Map<String, Object> config) {
        String authType = (String) config.get("headerType"); // API_KEY, BEARER, CUSTOM
        String value = (String) config.get("value");
        String headerName = (String) config.getOrDefault("headerName", "Authorization");

        switch (authType) {
            case "API_KEY":
                context.getHeaders().set(headerName, value);
                break;
            case "BEARER":
                context.getHeaders().setBearerAuth(value);
                break;
            case "CUSTOM":
                context.getHeaders().set(headerName, value);
                break;
        }
    }

    @Override
    public String getAuthType() {
        return "HEADER";
    }
}
