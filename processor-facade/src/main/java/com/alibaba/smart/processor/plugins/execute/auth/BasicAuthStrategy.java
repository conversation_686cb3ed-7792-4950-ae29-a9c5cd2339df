package com.alibaba.smart.processor.plugins.execute.auth;

import com.alibaba.smart.processor.plugins.execute.model.ApiCallContext;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.Map;

@Component
public class BasicAuthStrategy implements AuthStrategy {
    @Override
    public void applyAuth(ApiCallContext context, Map<String, Object> config) {
        String username = (String) config.get("username");
        String password = (String) config.get("password");
        if (StringUtils.hasText(username) && StringUtils.hasText(password)) {
            context.getHeaders().setBasicAuth(username, password);
        }
    }

    @Override
    public String getAuthType() {
        return "BASIC";
    }
}
