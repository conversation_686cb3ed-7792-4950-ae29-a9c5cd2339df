package com.alibaba.smart.processor.model.vo;


import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;


@AllArgsConstructor
@NoArgsConstructor
public class ChatModelVO {

    /**
     * 会话标题
     */
    private String title;

    /**
     * 会话id
     */
    private String sessionId;

    /**
     * 会话里产生的所有文件集合，来源：用户上传、AI执行过程中产生(包括代码文件）、最终输出的结果
     */
    private List<File> files = new ArrayList<>();

    /**
     * 上传的附件ID列表
     */
    private List<String> attachmentFileIds = new ArrayList<>();

    /**
     * 执行结果文件的ID列表
     */
    private List<String> generateFileIds = new ArrayList<>();

    /**
     * 会话消息列表
     */
    private List<MessageVO> messages;

    /**
     * 会话关联的插件id列表
     */
    private List<String> pluginIds;

    public ChatModelVO(String sessionId) {
        this.sessionId = sessionId;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getSessionId() {
        return sessionId;
    }

    public void setSessionId(String sessionId) {
        this.sessionId = sessionId;
    }

    public List<MessageVO> getMessages() {
        return messages;
    }

    public void setMessages(List<MessageVO> messages) {
        this.messages = messages;
    }

    public List<File> getFiles() {
        return files;
    }

    public void setFiles(List<File> files) {
        this.files = files;
    }

    public List<String> getAttachmentFileIds() {
        return attachmentFileIds;
    }

    public void setAttachmentFileIds(List<String> attachmentFileIds) {
        this.attachmentFileIds = attachmentFileIds;
    }

    public List<String> getGenerateFileIds() {
        return generateFileIds;
    }

    public void setGenerateFileIds(List<String> generateFileIds) {
        this.generateFileIds = generateFileIds;
    }

    public List<String> getPluginIds() {
        return pluginIds;
    }

    public void setPluginIds(List<String> pluginIds) {
        this.pluginIds = pluginIds;
    }
}
