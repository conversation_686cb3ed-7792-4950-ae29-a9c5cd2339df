package com.alibaba.smart.processor.plugins.execute.model;

import java.io.Serializable;
import java.util.Map;

public class ApiCallRequest implements Serializable {
    /**
     * 插件id
     */
    private String pluginId;
    /**
     * 操作接口id
     */
    private String operationId;
    /**
     * 参数列表
     */
    private Map<String, Object> parameters;

    /**
     * 自定义请求头
     */
    private Map<String, String> headers;

    public String getPluginId() {
        return pluginId;
    }

    public void setPluginId(String pluginId) {
        this.pluginId = pluginId;
    }

    public String getOperationId() {
        return operationId;
    }

    public void setOperationId(String operationId) {
        this.operationId = operationId;
    }

    public Map<String, Object> getParameters() {
        return parameters;
    }

    public void setParameters(Map<String, Object> parameters) {
        this.parameters = parameters;
    }

    public Map<String, String> getHeaders() {
        return headers;
    }

    public void setHeaders(Map<String, String> headers) {
        this.headers = headers;
    }
}
