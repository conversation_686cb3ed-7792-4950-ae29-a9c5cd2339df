package com.alibaba.smart.processor.model.vo;

import java.util.Date;
import java.util.Map;

public class AppInfoVO {
    private String corpId;
    private String sessionId;
    private String appId;
    private String name;
    private String desc;
    private Date gmtCreate;
    private Date gmtModified;
    private String appIcon;
    private String coverUrl;
    private String entryUrl;
    private UserInfoVO UserInfo;
    private Map<String, Object> appConfig;

    /**
     * 应用版本
     */
    private String version;

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public String getCorpId() {
        return corpId;
    }

    public void setCorpId(String corpId) {
        this.corpId = corpId;
    }

    public String getSessionId() {
        return sessionId;
    }

    public void setSessionId(String sessionId) {
        this.sessionId = sessionId;
    }

    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public Date getGmtCreate() {
        return gmtCreate;
    }

    public void setGmtCreate(Date gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    public Date getGmtModified() {
        return gmtModified;
    }

    public void setGmtModified(Date gmtModified) {
        this.gmtModified = gmtModified;
    }

    public String getAppIcon() {
        return appIcon;
    }

    public void setAppIcon(String appIcon) {
        this.appIcon = appIcon;
    }

    public String getCoverUrl() {
        return coverUrl;
    }

    public void setCoverUrl(String coverUrl) {
        this.coverUrl = coverUrl;
    }

    public String getEntryUrl() {
        return entryUrl;
    }

    public void setEntryUrl(String entryUrl) {
        this.entryUrl = entryUrl;
    }

    public UserInfoVO getUserInfo() {
        return UserInfo;
    }

    public void setUserInfo(UserInfoVO userInfo) {
        UserInfo = userInfo;
    }

    public Map<String, Object> getAppConfig() {
        return appConfig;
    }

    public void setAppConfig(Map<String, Object> appConfig) {
        this.appConfig = appConfig;
    }
}
