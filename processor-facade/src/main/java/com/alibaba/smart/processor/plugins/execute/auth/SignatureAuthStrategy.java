package com.alibaba.smart.processor.plugins.execute.auth;

import com.alibaba.smart.processor.plugins.execute.model.ApiCallContext;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.UUID;

@Component
public class SignatureAuthStrategy implements AuthStrategy {
    @Override
    public void applyAuth(ApiCallContext context, Map<String, Object> config) {
        String accessKey = (String) config.get("accessKey");
        String secretKey = (String) config.get("secretKey");
        String algorithm = (String) config.getOrDefault("algorithm", "HMAC-SHA256");

        // 生成签名
        String timestamp = String.valueOf(System.currentTimeMillis());
        String nonce = UUID.randomUUID().toString();

        String stringToSign = buildStringToSign(context, timestamp, nonce);
        String signature = generateSignature(stringToSign, secretKey, algorithm);

        // 添加签名相关的header
        context.getHeaders().set("X-Access-Key", accessKey);
        context.getHeaders().set("X-Timestamp", timestamp);
        context.getHeaders().set("X-Nonce", nonce);
        context.getHeaders().set("X-Signature", signature);
    }

    @Override
    public String getAuthType() {
        return "SIGNATURE";
    }

    private String buildStringToSign(ApiCallContext context, String timestamp, String nonce) {
        // 构建待签名字符串的逻辑
        return context.getUrl() + "|" + timestamp + "|" + nonce;
    }

    private String generateSignature(String stringToSign, String secretKey, String algorithm) {
        // 生成签名的逻辑
//        try {
//            Mac mac = Mac.getInstance(algorithm);
//            SecretKeySpec secretKeySpec = new SecretKeySpec(secretKey.getBytes(), algorithm);
//            mac.init(secretKeySpec);
//            byte[] signatureBytes = mac.doFinal(stringToSign.getBytes());
//            return Base64.getEncoder().encodeToString(signatureBytes);
//        } catch (Exception e) {
//            throw new RuntimeException("生成签名失败", e);
//        }
        return null;
    }
}
