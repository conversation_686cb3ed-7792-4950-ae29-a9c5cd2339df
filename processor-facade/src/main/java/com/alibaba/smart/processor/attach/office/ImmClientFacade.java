package com.alibaba.smart.processor.attach.office;

import com.alibaba.smart.processor.attach.office.model.*;
import com.alibaba.smart.processor.config.ImmClientConfig;

/**
 * <AUTHOR>
 * @date 2020/4/7 1:46 下午
 */
public interface ImmClientFacade {

    /**
     * 获取配置
     *
     * @return 配置
     */
    ImmClientConfig getImmClientConfig();

    /**
     * 获取预览凭证
     *
     * @return 预览凭证
     */
    OfficePreviewResult getOfficePreviewUrl(OfficePreviewParams officePreviewParams);

    /**
     * 刷新预览凭证
     *
     * @return 凭证
     */
    RefreshTokenResult refreshOfficePreviewToken(RefreshTokenParams refreshTokenParams);
}
