package com.alibaba.smart.processor.plugins.execute.model;

import java.util.Map;

public class PluginApiDefinition {
    private String baseUrl;
    private Map<String, PluginApiOperation> operations;
    private Map<String, Object> securitySchemes;
    private String version;
    private String title;
    private String description;

    public String getBaseUrl() {
        return baseUrl;
    }

    public void setBaseUrl(String baseUrl) {
        this.baseUrl = baseUrl;
    }

    public Map<String, PluginApiOperation> getOperations() {
        return operations;
    }

    public void setOperations(Map<String, PluginApiOperation> operations) {
        this.operations = operations;
    }

    public Map<String, Object> getSecuritySchemes() {
        return securitySchemes;
    }

    public void setSecuritySchemes(Map<String, Object> securitySchemes) {
        this.securitySchemes = securitySchemes;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }
}
