package com.alibaba.smart.processor.plugins.execute.auth;

import com.alibaba.smart.processor.plugins.execute.model.ApiCallContext;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component
public class QueryAuthStrategy implements AuthStrategy {
    @Override
    public void applyAuth(ApiCallContext context, Map<String, Object> config) {
        String paramName = (String) config.get("paramName");
        String value = (String) config.get("value");

        // 修改URL，添加查询参数
        String currentUrl = context.getUrl();
        String separator = currentUrl.contains("?") ? "&" : "?";
        String newUrl = currentUrl + separator + paramName + "=" + value;
        context.setUrl(newUrl);
    }

    @Override
    public String getAuthType() {
        return "QUERY";
    }
}
