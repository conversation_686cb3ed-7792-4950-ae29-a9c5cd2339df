package com.alibaba.smart.processor.attach;


import com.alibaba.smart.processor.attach.office.model.OfficeCallType;
import com.alibaba.smart.processor.attach.office.model.OfficePreviewResult;
import com.alibaba.smart.processor.attach.office.model.RefreshTokenResult;

/**
 *
 */
public interface ImmOfficeFacade {
    /**
     * 打开一个文件，用于在线预览
     *
     * @param fileName 文件名， OSS 全路径
     * @return 预览凭证信息
     */
    OfficePreviewResult previewFile(String fileName);
    /**
     * 刷新 token
     *
     * @param officeType   刷新类型
     * @param accessToken  访问 token
     * @param refreshToken 刷新 token
     * @return 新的凭证信息
     */
    RefreshTokenResult refreshToken(OfficeCallType officeType, String accessToken, String refreshToken);

}
