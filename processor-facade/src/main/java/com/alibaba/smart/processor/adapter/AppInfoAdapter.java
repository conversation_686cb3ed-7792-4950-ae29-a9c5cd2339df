package com.alibaba.smart.processor.adapter;

import com.alibaba.fastjson.JSON;
import com.alibaba.smart.processor.model.AppInfoDO;
import com.alibaba.smart.processor.model.vo.AppInfoVO;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

public class AppInfoAdapter {
    public static AppInfoVO toAppInfoVO(AppInfoDO appInfoDO) {
        if(appInfoDO == null){
            return null;
        }
        AppInfoVO appInfoVO = new AppInfoVO();
        appInfoVO.setAppId(appInfoDO.getAppId());
        appInfoVO.setCorpId(appInfoDO.getCorpId());
        appInfoVO.setAppIcon(appInfoDO.getAppIcon());
        appInfoVO.setDesc(appInfoDO.getAppDesc());
        appInfoVO.setName(appInfoDO.getAppName());
        appInfoVO.setSessionId(appInfoDO.getSessionId());
        appInfoVO.setGmtCreate(appInfoDO.getGmtCreate());
        appInfoVO.setGmtModified(appInfoDO.getGmtModified());
        appInfoVO.setEntryUrl("/app/" + appInfoDO.getAppId() + "/index.html");
        appInfoVO.setVersion(appInfoDO.getVersion());

        if(StringUtils.isNotBlank(appInfoDO.getAppConfig())){
            appInfoVO.setAppConfig(JSON.parseObject(appInfoDO.getAppConfig()).getInnerMap());
        }
        return appInfoVO;
    }

    public static List<AppInfoVO> toAppInfoVOList(List<AppInfoDO> appInfoDOList) {
        if(CollectionUtils.isEmpty(appInfoDOList)){
            return new ArrayList<AppInfoVO>();
        }
        return appInfoDOList.stream().map(AppInfoAdapter::toAppInfoVO).collect(Collectors.toList());
    }
}
