package com.alibaba.smart.processor.config;

import com.aliyun.oss.OSSClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class OssClientConfig {

    @Value("${oss_endpoint_private}")
    private String endpoint;

    @Value("${oss_key_private}")
    private String accessKeyId;

    @Value("${oss_secret_private}")
    private String accessKeySecret;

    @Value("${oss_default_bucket_name_private}")
    private String bucket;

    @Value("${oss_default_bucket_dir:smart_processor}")
    private String dir;

    @Bean(value = "ossClient")
    public OSSClient ossClient() {
        return new OSSClient(endpoint, accessKeyId, accessKeySecret);
    }

    public String getEndpoint() {
        return endpoint;
    }

    public String getAccessKeyId() {
        return accessKeyId;
    }

    public String getAccessKeySecret() {
        return accessKeySecret;
    }

    public String getBucket() {
        return bucket;
    }

    public String getDir() {
        return dir;
    }
}
