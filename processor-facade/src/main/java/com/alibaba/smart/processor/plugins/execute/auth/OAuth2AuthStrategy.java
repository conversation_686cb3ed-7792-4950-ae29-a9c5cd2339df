package com.alibaba.smart.processor.plugins.execute.auth;

import com.alibaba.smart.processor.plugins.execute.model.ApiCallContext;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component
public class OAuth2AuthStrategy implements AuthStrategy {

    @Override
    public void applyAuth(ApiCallContext context, Map<String, Object> config) {
        String clientId = (String) config.get("clientId");
        String clientSecret = (String) config.get("clientSecret");
        String refreshToken = (String) config.get("refreshToken");
//
//        // 获取或刷新access token
//        String accessToken = tokenService.getValidAccessToken(clientId, clientSecret, refreshToken);
//        context.getHeaders().setBearerAuth(accessToken);
    }

    @Override
    public String getAuthType() {
        return "OAUTH2";
    }
}
