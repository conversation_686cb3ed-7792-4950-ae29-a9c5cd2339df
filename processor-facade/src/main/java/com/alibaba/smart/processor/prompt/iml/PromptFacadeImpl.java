package com.alibaba.smart.processor.prompt.iml;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import javax.net.ssl.TrustManager;
import javax.net.ssl.X509TrustManager;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.smart.processor.diamond.DiamondEnums;
import org.apache.http.nio.conn.ssl.SSLIOSessionStrategy;
import org.springframework.stereotype.Service;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;
import com.alibaba.smart.processor.model.dto.PromptDTO;
import com.alibaba.smart.processor.model.dto.StreamHttpClientContext;
import com.alibaba.smart.processor.prompt.PromptFacade;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.conn.ssl.NoopHostnameVerifier;
import org.apache.http.impl.nio.client.CloseableHttpAsyncClient;
import org.apache.http.impl.nio.client.HttpAsyncClients;
import org.apache.http.impl.nio.reactor.IOReactorConfig;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.ssl.SSLContexts;
import javax.net.ssl.SSLContext;
import java.security.cert.X509Certificate;
import org.apache.http.HttpResponse;
import java.util.concurrent.Future;
@Service
public class PromptFacadeImpl implements PromptFacade{
    public static List<org.apache.http.NameValuePair> buildNameValuePairs(Map<String, String> params) {
        if (MapUtils.isEmpty(params)) {
            return new ArrayList<>();
        }
        return params.entrySet().stream().filter(entry -> StringUtils.isNotBlank(entry.getKey()) && StringUtils.isNotBlank(entry.getValue())).map(entry -> new BasicNameValuePair(entry.getKey(), entry.getValue())).collect(Collectors.toList());
    }

    @Override
    public String ask(PromptDTO promptDTO) throws Exception {
        SseEmitter emitter = new SseEmitter(60*1000L);
        StreamHttpClientContext context = new StreamHttpClientContext();
        context.setEndpoint("https://polymind-portal.alibaba.net");
//        context.setEndpoint("http://127.0.0.1:8000");
        context.setSseEmitter(emitter);
        context.setApiName("/optimize_prompt");
        Map<String, String> params = new HashMap<String, String>();
        ObjectMapper mapper = new ObjectMapper();
        params.put("model", promptDTO.getModel());
        params.put("messages", mapper.writeValueAsString(promptDTO.getMessages()));
        params.put("iterations", promptDTO.getIterations());
        context.setParams(params);
        // StreamHttpClient.invoke(context);
        String url = context.getEndpoint() + context.getApiName();
        HttpPost httpPost = new HttpPost(url);
        UrlEncodedFormEntity entity = new UrlEncodedFormEntity(buildNameValuePairs(context.getParams()), StandardCharsets.UTF_8);
        httpPost.setEntity(entity);
        IOReactorConfig ioReactorConfig = IOReactorConfig.custom()
                .setSoKeepAlive(true)
                .setIoThreadCount(2)
                .build();
        TrustManager[] trustAllCerts = new TrustManager[]{
                new X509TrustManager() {
                    public X509Certificate[] getAcceptedIssuers() {  return new X509Certificate[]{}; }
                    public void checkClientTrusted(X509Certificate[] certs, String authType) {}
                    public void checkServerTrusted(X509Certificate[] certs, String authType) {}
                }
        };
        SSLContext sslContext = SSLContexts.custom()
                .loadTrustMaterial(null, (chain, authType) -> true) // 信任所有证书
                .build();
        sslContext.init(null, trustAllCerts, new java.security.SecureRandom());
        SSLIOSessionStrategy sessionStrategy = new SSLIOSessionStrategy(sslContext, NoopHostnameVerifier.INSTANCE);
        CloseableHttpAsyncClient httpClient = HttpAsyncClients.custom()
                .setKeepAliveStrategy((response, ctx) -> context.getTimeout()) // 保持连接60秒
//                .setConnectionManager(connectionManager)
                .setDefaultIOReactorConfig(ioReactorConfig)
                .setSSLStrategy(sessionStrategy)
                .build();
        httpClient.start();
        Future<HttpResponse> response = httpClient.execute(httpPost, null);
        System.out.println(response);
        // 读取 InputStream 内容为字符串返回
        HttpResponse httpResponse = response.get();
        try (java.io.InputStream is = httpResponse.getEntity().getContent();
             java.io.BufferedReader reader = new java.io.BufferedReader(new java.io.InputStreamReader(is, java.nio.charset.StandardCharsets.UTF_8))) {
            StringBuilder sb = new StringBuilder();
            String line;
            while ((line = reader.readLine()) != null) {
                sb.append(line);
            }
            return sb.toString();
        }
    }

    @Override
    public String getSystemPrompt(String promptKey) {
        String systemPrompt = DiamondEnums.polymind_system_prompt_config.getValue();
        if(StringUtils.isBlank(systemPrompt)) {
            return null;
        }
        JSONObject promptJson = JSONObject.parseObject(systemPrompt);
        return promptJson.getString(promptKey);
    }
}
