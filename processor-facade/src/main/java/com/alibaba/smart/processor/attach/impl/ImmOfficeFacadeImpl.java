package com.alibaba.smart.processor.attach.impl;

import com.alibaba.smart.processor.attach.AttachmentService;
import com.alibaba.smart.processor.attach.ImmOfficeFacade;
import com.alibaba.smart.processor.attach.office.ImmClientFacade;
import com.alibaba.smart.processor.attach.office.OfficeClientFacade;
import com.alibaba.smart.processor.attach.office.model.OfficeCallType;
import com.alibaba.smart.processor.attach.office.model.OfficePreviewParams;
import com.alibaba.smart.processor.attach.office.model.OfficePreviewResult;
import com.alibaba.smart.processor.attach.office.model.RefreshTokenResult;
import com.alibaba.smart.processor.model.attach.FileRecord;
import com.alibaba.smart.processor.utils.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 *
 */
@Service("officeFacade")
public class ImmOfficeFacadeImpl implements ImmOfficeFacade {
    @Resource
    private AttachmentService attachmentService;
    @Resource
    private OfficeClientFacade officeClient;
    @Resource
    private ImmClientFacade immClientFacade;

    @Override
    public OfficePreviewResult previewFile(String fileName) {
        FileRecord fileRecord = attachmentService.getAttachmentByKey(fileName);
        if(fileRecord == null){
            return null;
        }
        String immKey = fileRecord.getImmKey();
        if(StringUtils.isBlank(immKey)){
            immKey = officeClient.copyFileToOfficeOss(fileName);
        }
        OfficePreviewParams previewParams = buildPreviewParams(immKey);
        return immClientFacade.getOfficePreviewUrl(previewParams);
    }

    private OfficePreviewParams buildPreviewParams(String immKey) {
        // 由于部分文件允许上传大写的后缀名，例如PDF，而office服务不支持，这里使用toLowerCase
        String srcType = FileUtils.getFileExtension(immKey);
        String srcUri = officeClient.getOfficeOssUri(immKey);
        OfficePreviewParams params = new OfficePreviewParams();
        params.setSrcType(srcType);
        params.setSrcUri(srcUri);
        params.setUserId("0323191242-856571019");
        params.setUid(80012982L);
        params.setUserName("test");
        return params;
    }

    @Override
    public RefreshTokenResult refreshToken(OfficeCallType officeType, String accessToken, String refreshToken) {
        return null;
    }
}
