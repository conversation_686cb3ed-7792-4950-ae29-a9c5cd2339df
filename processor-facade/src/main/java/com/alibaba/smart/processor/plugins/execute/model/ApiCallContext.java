package com.alibaba.smart.processor.plugins.execute.model;

import org.springframework.http.HttpHeaders;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

public class ApiCallContext implements Serializable {
    private String url;
    private HttpHeaders headers;
    private Object requestBody;
    private Map<String, Object> queryParams;
    private Map<String, Object> pathParams;

    public ApiCallContext(String url, HttpHeaders headers, Object requestBody) {
        this.url = url;
        this.headers = headers;
        this.requestBody = requestBody;
        this.queryParams = new HashMap<>();
        this.pathParams = new HashMap<>();
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public HttpHeaders getHeaders() {
        return headers;
    }

    public void setHeaders(HttpHeaders headers) {
        this.headers = headers;
    }

    public Object getRequestBody() {
        return requestBody;
    }

    public void setRequestBody(Object requestBody) {
        this.requestBody = requestBody;
    }

    public Map<String, Object> getQueryParams() {
        return queryParams;
    }

    public void setQueryParams(Map<String, Object> queryParams) {
        this.queryParams = queryParams;
    }

    public Map<String, Object> getPathParams() {
        return pathParams;
    }

    public void setPathParams(Map<String, Object> pathParams) {
        this.pathParams = pathParams;
    }
}
