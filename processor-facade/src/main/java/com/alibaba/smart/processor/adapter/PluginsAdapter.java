package com.alibaba.smart.processor.adapter;

import com.alibaba.smart.processor.model.PluginsDO;
import com.alibaba.smart.processor.model.SessionPluginBindingDO;
import com.alibaba.smart.processor.model.dto.PluginsDTO;
import com.alibaba.smart.processor.model.vo.PluginsVO;
import com.alibaba.smart.processor.model.vo.SessionPluginsBindingVO;
import org.apache.commons.collections.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

public class PluginsAdapter {

    public static PluginsVO toAppInfoVO(PluginsDO pluginsDO) {
        if(pluginsDO == null){
            return null;
        }
        PluginsVO pluginsVO = new PluginsVO();
        pluginsVO.setPluginId(pluginsDO.getPluginId());
        pluginsVO.setName(pluginsDO.getName());
        pluginsVO.setDescription(pluginsDO.getDescription());
        pluginsVO.setIcon(pluginsDO.getIcon());
        pluginsVO.setType(pluginsDO.getType());
        pluginsVO.setGmtModified(pluginsDO.getGmtModified());
        pluginsVO.setModifier(pluginsDO.getModifier());
        pluginsVO.setContent(pluginsDO.getContent());
        pluginsVO.setAuthConfig(pluginsDO.getAuthConfig());
        return pluginsVO;
    }

    public static List<PluginsVO> toPluginsVOList(List<PluginsDO> pluginsDOList) {
        if(CollectionUtils.isEmpty(pluginsDOList)){
            return new ArrayList<PluginsVO>();
        }
        return pluginsDOList.stream().map(PluginsAdapter::toAppInfoVO).collect(Collectors.toList());
    }

    public static PluginsDO toAppInfoVO(PluginsDTO pluginsDTO) {
        if(pluginsDTO == null){
            return null;
        }
        PluginsDO pluginsDO = new PluginsDO();
        pluginsDO.setPluginId(pluginsDTO.getPluginId());
        pluginsDO.setName(pluginsDTO.getName());
        pluginsDO.setDescription(pluginsDTO.getDescription());
        pluginsDO.setIcon(pluginsDTO.getIcon());
        pluginsDO.setType(pluginsDTO.getType());
        pluginsDO.setContent(pluginsDTO.getContent());
        pluginsDO.setAuthConfig(pluginsDTO.getAuthConfig());
        return pluginsDO;
    }

    private static List<String> buildPluginIds(List<SessionPluginBindingDO> sessionPluginBindingDOS) {
        return sessionPluginBindingDOS.stream().map(SessionPluginBindingDO::getPluginId).collect(Collectors.toList());
    }

}
