package com.alibaba.smart.processor.tool.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.smart.processor.fc.AliyunFunctionComputeFacade;
import com.alibaba.smart.processor.mapper.ToolInfoMapper;
import com.alibaba.smart.processor.model.ToolInfoDO;
import com.alibaba.smart.processor.model.ToolInfoDOExample;
import com.alibaba.smart.processor.model.dto.ToolInfoDTO;
import com.alibaba.smart.processor.model.vo.FcExecuteResult;
import com.alibaba.smart.processor.model.vo.ToolInfoVO;
import com.alibaba.smart.processor.tool.ToolFacade;
import com.dingtalk.common.sso.filter.RpcContext;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;

@Service
public class ToolFacadeImpl implements ToolFacade {
    @Resource
    private ToolInfoMapper toolInfoMapper;
    @Resource
    private AliyunFunctionComputeFacade aliyunFunctionComputeFacade;
    @Override
    public Object execute(String toolId, Map<String, Object> params) {
        ToolInfoDOExample example = new ToolInfoDOExample();
        ToolInfoDOExample.Criteria criteria = example.createCriteria();
        criteria.andUuidEqualTo(toolId);
        List<ToolInfoDO> toolInfoDOS = toolInfoMapper.selectByExampleWithBLOBs(example);
        if(toolInfoDOS != null && !toolInfoDOS.isEmpty()) {
            ToolInfoDO toolInfoDO = toolInfoDOS.get(0);
            String codeScript = toolInfoDO.getContent();
            String name = toolInfoDO.getName();

            FcExecuteResult result = aliyunFunctionComputeFacade.executeWithContent(name, codeScript, params);
            return JSON.toJSON(result);
        }
        return null;
    }

    @Override
    public ToolInfoVO getToolInfo(String toolId){
        ToolInfoDOExample query = new ToolInfoDOExample();
        ToolInfoDOExample.Criteria criteria = query.createCriteria();
        criteria.andUuidEqualTo(toolId);
        List<ToolInfoDO> toolInfoDOS = toolInfoMapper.selectByExampleWithBLOBs(query);
        if(CollectionUtils.isEmpty(toolInfoDOS)){
            return null;
        }
        ToolInfoVO toolInfoVO = new ToolInfoVO();
        toolInfoVO.setToolId(toolId);
        toolInfoVO.setContent(toolInfoDOS.get(0).getContent());
        return toolInfoVO;
    }

    @Override
    public void updateToolContent(String toolId, String content) {
        ToolInfoDOExample selective = new ToolInfoDOExample();
        ToolInfoDOExample.Criteria criteria = selective.createCriteria();
        criteria.andUuidEqualTo(toolId);

        ToolInfoDO toolInfoDO = new ToolInfoDO();
        toolInfoDO.setContent(content);
        toolInfoDO.setGmtModified(new Date());
        toolInfoMapper.updateByExampleSelective(toolInfoDO, selective);
    }

    @Override
    public String saveTool(ToolInfoDTO toolInfoDTO) {
        ToolInfoDO toolInfoDO = new ToolInfoDO();
        toolInfoDO.setCorpId(RpcContext.corpId());
        toolInfoDO.setCreator(RpcContext.userId());
        toolInfoDO.setModifier(RpcContext.userId());
        toolInfoDO.setGmtCreate(new Date());
        toolInfoDO.setGmtModified(new Date());
        toolInfoDO.setSessionId(toolInfoDTO.getSessionId());
        toolInfoDO.setName("python_demo");
        toolInfoDO.setContent(toolInfoDTO.getContent());
        toolInfoDO.setDescription(toolInfoDTO.getDescription());

        String uuid = UUID.randomUUID().toString();
        toolInfoDO.setUuid(uuid);
        toolInfoDO.setStatus("online");

        toolInfoMapper.insert(toolInfoDO);
        return uuid;
    }

    @Override
    public void deleteTool(String toolId) {
        toolInfoMapper.deleteByToolId(toolId);
    }

    @Override
    public Object applyTool(String ossKey, String toolId) {
        ToolInfoDO toolInfoDO = toolInfoMapper.selectByToolId(toolId);
        if (Objects.isNull(toolInfoDO)) {
            throw new RuntimeException("tool no exist");
        }

        String codeScript = toolInfoDO.getContent();
        String name = toolInfoDO.getName();
        Map<String, Object> paramsMap = new HashMap<>();
        paramsMap.put("ossKey", ossKey);
        FcExecuteResult result = aliyunFunctionComputeFacade.executeWithContent(name, codeScript, paramsMap);
        return JSON.toJSON(result);
    }

    @Override
    public Object designTool(String prompt, String ossKey) {

        // todo 调用python提供的api接口

        return null;
    }
}
