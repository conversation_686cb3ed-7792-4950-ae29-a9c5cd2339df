package com.alibaba.smart.processor.attach.office.impl;
import com.alibaba.fastjson.JSON;
import com.alibaba.smart.processor.attach.office.ImmClientFacade;
import com.alibaba.smart.processor.attach.office.model.*;
import com.alibaba.smart.processor.config.ImmClientConfig;
import com.aliyun.imm20200930.Client;
import com.aliyun.imm20200930.models.*;
import com.aliyun.teaopenapi.models.Config;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
/**
 * <AUTHOR>
 * @date 2022/3/15
 */
@Service("immClientV2")
public class ImmClientV2 implements ImmClientFacade {

    private static Logger logger = LoggerFactory.getLogger(ImmClientV2.class);

    /**
     * 客户端配置
     */
    @Resource
    private ImmClientConfig immClientConfig;

    /**
     * 云客户端
     */
    private Client client;

    @PostConstruct
    public void init() throws Exception {
        Config config = new Config()
            // 您的AccessKey ID
            .setAccessKeyId(immClientConfig.getAccessKeyId())
            // 您的AccessKey Secret
            .setAccessKeySecret(immClientConfig.getAccessKeySecret());
        // 访问的域名
        config.endpoint = "imm.cn-shanghai.aliyuncs.com";
        client = new Client(config);
    }


    @Override
    public OfficePreviewResult getOfficePreviewUrl(OfficePreviewParams officePreviewParams) {
        WebofficePermission webofficePermission = new WebofficePermission()
            .setReadonly(Boolean.TRUE)
            .setCopy(Boolean.TRUE)
            .setExport(Boolean.TRUE)
            .setPrint(Boolean.TRUE);

        String userId = null != officePreviewParams.getUid() ?
            String.valueOf(officePreviewParams.getUid()) : officePreviewParams.getUserId();
        WebofficeUser user = new WebofficeUser().setId(userId)
            .setName(officePreviewParams.getUserName());
        if (StringUtils.isNotEmpty(officePreviewParams.getAvatar())){
            user.setAvatar(officePreviewParams.getAvatar());
        }
        GetWebofficeURLRequest getWebofficeURLRequest = new GetWebofficeURLRequest()
            .setProjectName(immClientConfig.getImmV2ProjectName())
            .setPermission(webofficePermission)
            .setSourceURI(officePreviewParams.getSrcUri())
            .setFilename(officePreviewParams.getFileName())
            .setUser(user)
            .setExternalUploaded(Boolean.TRUE);
        try {
            // 复制代码运行请自行打印 API 的返回值
            GetWebofficeURLResponse response = client.getWebofficeURL(getWebofficeURLRequest);
            if (null == response || null == response.body) {
                logger.info("ImmClientV2 getOfficePreviewUrl response null {}", JSON.toJSONString(getWebofficeURLRequest));
                throw new RuntimeException("打开文件预览失败");
            }
            return new OfficePreviewResult(response.body);
        } catch (Exception e) {
            logger.error("ImmClientV2 getOfficePreviewUrl {}", JSON.toJSONString(getWebofficeURLRequest), e);
            throw new RuntimeException("打开文件预览失败");
        }
    }

    @Override
    public RefreshTokenResult refreshOfficePreviewToken(RefreshTokenParams refreshTokenParams) {
        RefreshWebofficeTokenRequest request =
            new RefreshWebofficeTokenRequest()
                .setProjectName(immClientConfig.getImmV2ProjectName())
                .setAccessToken(refreshTokenParams.getAccessToken())
                .setRefreshToken(refreshTokenParams.getRefreshToken());
        try {
            RefreshWebofficeTokenResponse refreshWebofficeTokenResponse = client.refreshWebofficeToken(request);
            if (null == refreshWebofficeTokenResponse || null == refreshWebofficeTokenResponse.body){
                logger.info("ImmClientV2 refreshOfficePreviewToken response null {}", JSON.toJSONString(request));
                throw new RuntimeException("刷新预览凭证失败");
            }
            return new RefreshTokenResult(refreshWebofficeTokenResponse.body);
        } catch (Exception e) {
            logger.error("ImmClientV2 refreshOfficePreviewToken {}", JSON.toJSONString(request), e);
            throw new RuntimeException("刷新预览凭证失败");
        }
    }

    @Override
    public ImmClientConfig getImmClientConfig() {
        return immClientConfig;
    }
}
