package com.alibaba.smart.processor.model.dto;

import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class FileAskDTO {

    /**
     * 是否需要思考
     */
    private String enable_thinking;

    private String frequencyPenalty;

    private String maxTokens;

    private String max_tokens;

    private List<Map<String, Object>> messages;

    private String model;

    private String presencePenalty;

    private Map<String, Object> provider;

    private Map<String, Object> response_format;

    private String stream;

    private String temperature;

    private String tool_choice;

    private List<ToolDTO> tools;

    private String topP;

    private String sessionId;

    private Map<String, Object> stream_options;

}
