package com.alibaba.smart.processor.tool;

import com.alibaba.smart.processor.model.dto.ToolInfoDTO;
import com.alibaba.smart.processor.model.vo.ToolInfoVO;

import java.util.Map;

public interface ToolFacade {
    Object execute(String toolId, Map<String, Object> params);

    ToolInfoVO getToolInfo(String toolId);

    void updateToolContent(String toolId, String content);

    String saveTool(ToolInfoDTO toolInfoDTO);

    void deleteTool(String toolId);

    Object applyTool(String ossKey, String toolId);

    Object designTool(String prompt, String ossKey);

}
