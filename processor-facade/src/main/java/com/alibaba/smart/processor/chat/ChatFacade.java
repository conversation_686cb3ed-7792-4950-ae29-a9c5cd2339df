package com.alibaba.smart.processor.chat;

import com.alibaba.smart.processor.model.dto.PageDto;
import com.alibaba.smart.processor.model.vo.ChatSessionVO;

import java.util.List;

import com.alibaba.smart.processor.model.dto.FileAskDTO;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

public interface ChatFacade {
    String createSession();

    void deleteSession(String sessionId);

    List<ChatSessionVO> listSessions();

    SseEmitter ask(FileAskDTO fileAskDTO) throws Exception;

    void updateSession(String sessionId, String title);
}
