package com.alibaba.smart.processor.corp.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.smart.processor.app.enums.CorpConfigType;
import com.alibaba.smart.processor.corp.CorpConfigFacade;
import com.alibaba.smart.processor.corp.CorpConfigService;
import com.alibaba.smart.processor.exception.BusinessException;
import com.alibaba.smart.processor.exception.ExceptionContext;
import com.alibaba.smart.processor.model.CorpConfigDO;
import com.alibaba.smart.processor.model.dto.CorpConfigDTO;
import com.alibaba.smart.processor.model.vo.CorpConfigVO;
import com.alibaba.smart.processor.permission.BizPermissionService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

@Service
public class CorpConfigFacadeImpl implements CorpConfigFacade {
    @Resource
    private CorpConfigService corpConfigService;
    @Resource
    private BizPermissionService bizPermissionService;
    @Override
    public void saveOrUpdateCorpConfig(CorpConfigDTO corpConfigDTO) {
        boolean hasPermission = bizPermissionService.isSuperOrCorpMainManager();
        if(!hasPermission){
            throw new BusinessException(new ExceptionContext("无权限"),"000000","无权限");
        }

        CorpConfigDO configDO = corpConfigService.getConfig();
        Map<String, String> configMap = new HashMap<>();
        configMap.put(CorpConfigType.LLM_CONFIG.alias(), JSON.toJSONString(corpConfigDTO));

        if(configDO == null){
            corpConfigService.saveConfig(JSON.toJSONString(configMap));
        }else{
            corpConfigService.updateConfig(configDO.getId(), JSON.toJSONString(configMap));
        }
    }

    @Override
    public CorpConfigVO getCorpConfig() {
        boolean hasPermission = bizPermissionService.isSuperOrCorpMainManager();
        if(!hasPermission){
            throw new BusinessException(new ExceptionContext("无权限"),"000000","无权限");
        }
        return getCorpConfigNoPermission();
    }

    @Override
    public CorpConfigVO getCorpConfigNoPermission() {
        CorpConfigDO configDO = corpConfigService.getConfig();
        if(configDO == null || StringUtils.isBlank(configDO.getConfig())){
            return null;
        }
        JSONObject configJson = JSON.parseObject(configDO.getConfig());
        String llmConfig = configJson.getString(CorpConfigType.LLM_CONFIG.alias());
        CorpConfigDTO configDTO = JSON.parseObject(llmConfig, CorpConfigDTO.class);
        CorpConfigVO configVO = new CorpConfigVO();
        BeanUtils.copyProperties(configDTO, configVO);
        return configVO;
    }
}
