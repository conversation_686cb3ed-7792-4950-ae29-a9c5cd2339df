package com.alibaba.smart.processor.plugins.execute.model;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

public class PluginApiOperation implements Serializable {
    private String method;
    private String path;
    private String summary;
    private String description;
    private Map<String, PluginApiParameter> parameters;
    private Map<String, String> headers;
    private List<String> security;
    private List<String> tags;
    private Map<String, PluginApiResponse> responses; // 响应定义

    public String getMethod() {
        return method;
    }

    public void setMethod(String method) {
        this.method = method;
    }

    public String getPath() {
        return path;
    }

    public void setPath(String path) {
        this.path = path;
    }

    public String getSummary() {
        return summary;
    }

    public void setSummary(String summary) {
        this.summary = summary;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Map<String, PluginApiParameter> getParameters() {
        return parameters;
    }

    public void setParameters(Map<String, PluginApiParameter> parameters) {
        this.parameters = parameters;
    }

    public Map<String, String> getHeaders() {
        return headers;
    }

    public void setHeaders(Map<String, String> headers) {
        this.headers = headers;
    }

    public List<String> getSecurity() {
        return security;
    }

    public void setSecurity(List<String> security) {
        this.security = security;
    }

    public List<String> getTags() {
        return tags;
    }

    public void setTags(List<String> tags) {
        this.tags = tags;
    }

    public Map<String, PluginApiResponse> getResponses() {
        return responses;
    }

    public void setResponses(Map<String, PluginApiResponse> responses) {
        this.responses = responses;
    }
}
