package com.alibaba.smart.processor.plugins.execute.auth;

import com.alibaba.smart.processor.plugins.execute.model.ApiCallContext;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class AuthManager {
    private final Map<String, AuthStrategy> authStrategies;
    private final ObjectMapper objectMapper;

    public AuthManager(List<AuthStrategy> strategies, ObjectMapper objectMapper) {
        this.authStrategies = strategies.stream()
                .collect(Collectors.toMap(AuthStrategy::getAuthType, Function.identity()));
        this.objectMapper = objectMapper;
    }

    public void applyAuthentication(ApiCallContext context, String authConfigJson) {
        if (!StringUtils.hasText(authConfigJson)) {
            return;
        }

        try {
            Map<String, Object> authConfig = objectMapper.readValue(authConfigJson, Map.class);
            String authType = (String) authConfig.get("authType");

            if (StringUtils.hasText(authType)) {
                AuthStrategy strategy = authStrategies.get(authType.toUpperCase());
                if (strategy != null) {
                    strategy.applyAuth(context, authConfig);
                    log.debug("应用{}鉴权策略", authType);
                } else {
                    log.warn("未找到鉴权策略: {}", authType);
                }
            }
        } catch (Exception e) {
            log.error("解析鉴权配置失败", e);
        }
    }
}
