package com.alibaba.smart.processor.plugins.execute.auth;

import com.alibaba.smart.processor.plugins.execute.model.ApiCallContext;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component
public class ApiKeyAuthStrategy implements AuthStrategy {
    @Override
    public void applyAuth(ApiCallContext context, Map<String, Object> config) {
        String value = (String) config.get("value");
        String headerName = (String) config.getOrDefault("headerName", "Authorization");

        context.getHeaders().set(headerName, value);
    }

    @Override
    public String getAuthType() {
        return "API_KEY";
    }
}
