package com.alibaba.smart.processor.history;

import com.alibaba.smart.processor.model.ChatMessageHistoryDO;
import com.alibaba.smart.processor.model.dto.FileDTO;
import com.alibaba.smart.processor.model.vo.ChatModelVO;
import com.alibaba.smart.processor.model.vo.MessageVO;

import java.util.List;

public interface ChatHistoryFacade {
    List<ChatMessageHistoryDO> listCharHistory(String sessionId, String messageId);

    void saveChatHistory(String sessionId, String messageId, String content);

    /**
     * 获取会话历史
     * @param sessionId
     * @return
     */
    ChatModelVO listMessage(String sessionId);

    String saveMessage(String sessionId, String messageId, String senderType, String content);

    MessageVO addMessage(String sessionId, String messageId, String senderType, String content, List<FileDTO> fileDTOS, String historyResponses);

    void updateMessage(String sessionId, String messageId, String content);

}
