package com.alibaba.smart.processor.chat.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.smart.processor.adapter.ChatSessionAdapter;
import com.alibaba.smart.processor.chat.ChatFacade;
import com.alibaba.smart.processor.corp.CorpConfigFacade;
import com.alibaba.smart.processor.exception.BusinessException;
import com.alibaba.smart.processor.exception.ExceptionContext;
import com.alibaba.smart.processor.model.ChatSessionDO;
import com.alibaba.smart.processor.model.vo.ChatSessionVO;
import com.alibaba.smart.processor.model.dto.FileAskDTO;
import com.alibaba.smart.processor.model.dto.StreamHttpClientContext;
import com.alibaba.smart.processor.model.vo.CorpConfigVO;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.alibaba.smart.processor.utils.StreamHttpClient;
import com.alibaba.smart.processor.session.ChatSessionService;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import javax.annotation.Resource;
import java.util.*;

@Service
public class ChatFacadeImpl implements ChatFacade {
    /**
     * 指定模型名称，用于获取对应的API Key
     */
    private static Set<String> CLAUDE_MODELSET = new HashSet<>(){
        {
            add( "anthropic/claude-3.7-sonnet");
            add( "anthropic/claude-sonnet-4");
        }
    };
    /**
     * 大模型请求路径
     */
    private static String LLM_REQUEST_PATH = "/ask_llm";

    @Resource
    private ChatSessionService chatSessionService;
    @Resource
    private CorpConfigFacade corpConfigFacade;

    @Value("${smart_portal_endpoint:https://polymind-portal.alibaba.net}")
    private String smartPortalEndpoint;

    @Override
    public String createSession() {
        return chatSessionService.createSession();
    }

    @Override
    public void deleteSession(String sessionId) {
        chatSessionService.deleteSession(sessionId);
    }

    @Override
    public List<ChatSessionVO> listSessions() {
        List<ChatSessionDO> sessions = chatSessionService.listSessions();
        return ChatSessionAdapter.toChatSessionVO(sessions);
    }

    @Override
    public SseEmitter ask(FileAskDTO fileAskDTO) throws Exception {
        SseEmitter emitter = new SseEmitter(10 * 60 * 1000L);
        StreamHttpClientContext context = new StreamHttpClientContext();
        context.setEndpoint(smartPortalEndpoint);
        context.setSseEmitter(emitter);
        context.setApiName(LLM_REQUEST_PATH);

        Map<String, String> params = new HashMap<String, String>();
        ObjectMapper mapper = new ObjectMapper();
        params.put("model", fileAskDTO.getModel());
        params.put("messages", mapper.writeValueAsString(fileAskDTO.getMessages()));
        if(StringUtils.isBlank(fileAskDTO.getEnable_thinking())){
            params.put("enable_thinking", "false");
        }else{
            params.put("enable_thinking", fileAskDTO.getEnable_thinking());
        }
        params.put("frequencyPenalty", StringUtils.defaultIfBlank(fileAskDTO.getFrequencyPenalty(), "0"));
        params.put("maxTokens", StringUtils.defaultIfBlank(fileAskDTO.getMaxTokens(), fileAskDTO.getMax_tokens()));
        params.put("presence_penalty", StringUtils.defaultIfBlank(fileAskDTO.getPresencePenalty(), "0"));

        if(fileAskDTO.getProvider() != null){
            params.put("provider", JSON.toJSONString(fileAskDTO.getProvider()));
        }
        if(fileAskDTO.getResponse_format() != null ){
            params.put("response_format", JSON.toJSONString(fileAskDTO.getResponse_format()));
        }

        if(fileAskDTO.getStream_options() != null){
            params.put("stream_options", JSON.toJSONString(fileAskDTO.getStream_options()));
        }

        params.put("stream", fileAskDTO.getStream());
        params.put("temperature", fileAskDTO.getTemperature());

        if(StringUtils.isBlank(fileAskDTO.getTool_choice())){
            params.put("tool_choice", "none");
        }else{
            params.put("tool_choice", fileAskDTO.getTool_choice());
        }

        if(CollectionUtils.isNotEmpty(fileAskDTO.getTools())){
            params.put("tools", mapper.writeValueAsString(fileAskDTO.getTools()));
        }

        params.put("top_p", StringUtils.defaultIfBlank(fileAskDTO.getTopP(),"0.9"));

        CorpConfigVO corpConfigVO = corpConfigFacade.getCorpConfigNoPermission();
        if(corpConfigVO == null
                || StringUtils.isBlank(corpConfigVO.getBailianApiKey())
                || StringUtils.isBlank(corpConfigVO.getOpenRouterApiKey())){
            throw new BusinessException(new ExceptionContext("API密钥配置缺失，请检查"),"000000", "API密钥配置缺失，请检查");
        }

        String appKey = CLAUDE_MODELSET.contains(fileAskDTO.getModel()) ? corpConfigVO.getOpenRouterApiKey() : corpConfigVO.getBailianApiKey();
        params.put("appKey", appKey);
        context.setParams(params);

        StreamHttpClient.invoke(context);
        return emitter;
    }

    @Override
    public void updateSession(String sessionId, String title) {
        if(StringUtils.isBlank(sessionId) || StringUtils.isBlank(title)){
            throw new BusinessException(new ExceptionContext("更新会话时参数不合法"),"000000","更新会话时参数不合法");
        }
        chatSessionService.updateSessionTitle(sessionId, title);
    }
}
