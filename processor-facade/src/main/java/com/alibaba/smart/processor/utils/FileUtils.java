package com.alibaba.smart.processor.utils;

import java.util.UUID;

public class FileUtils {

    public static String getOssFileId(String fileName, Boolean isFormatRequired){
        if (isFormatRequired) {
            return generateFileId() + "." + getFileExtension(fileName);
        } else {
            return generateFileId();
        }
    }

    public static String generateFileId() {
        return UUID.randomUUID().toString();
    }

    public static String getFileExtension(String fileName) {
        String format = "";
        int index = fileName.lastIndexOf(".");
        if (index != -1) {
            format = fileName.substring(index + 1);
            format = format.toLowerCase();
        }
        return format;
    }
}
