package com.alibaba.smart.processor.model.dto;

public class AppVersionRevertDTO {

    /**
     * 应用ID
     */
    private String appId;

    /**
     * 当前应用版本
     */
    private String currentVersion;

    /**
     * 待回滚的版本
     */
    private String revertVersion;

    /**
     * 更新原因
     */
    private String updateNote;

    public String getUpdateNote() {
        return updateNote;
    }

    public void setUpdateNote(String updateNote) {
        this.updateNote = updateNote;
    }

    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    public String getCurrentVersion() {
        return currentVersion;
    }

    public void setCurrentVersion(String currentVersion) {
        this.currentVersion = currentVersion;
    }

    public String getRevertVersion() {
        return revertVersion;
    }

    public void setRevertVersion(String revertVersion) {
        this.revertVersion = revertVersion;
    }
}
