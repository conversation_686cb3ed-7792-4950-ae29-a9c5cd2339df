package com.alibaba.smart.processor.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @date 2020/3/20 2:40 上午
 */
@Configuration
public class ImmClientConfig {
    /**
     * IMM 访问 AK
     */
    @Value("${aliyun.ram.accessKey}")
    private String accessKeyId;

    /**
     * IMM 访问 SK
     */
    @Value("${aliyun.ram.secretKey}")
    private String accessKeySecret;

    /**
     * IMM 默认 region
     */
    @Value("${aliyun.imm.regionId}")
    private String regionId;

    /**
     * IMM 计费项目名
     */
    @Value("${aliyun.imm.projectName}")
    private String projectName;

    @Value("${aliyun.imm.v2.projectName}")
    private String immV2ProjectName;

    /**
     * 预设的 region 对应的 endpoint 地址
     */
    @Value("${aliyun.imm.endpoints}")
    private String endpoints;

    public String getAccessKeyId() {
        return accessKeyId;
    }

    public String getAccessKeySecret() {
        return accessKeySecret;
    }

    public String getRegionId() {
        return regionId;
    }

    public String getProjectName() {
        return projectName;
    }

    public String getImmV2ProjectName() {
        return immV2ProjectName;
    }

    public String getEndpoints() {
        return endpoints;
    }
}
